<!DOCTYPE html>
<!-- saved from url=(0063)https://console.firebase.google.com/alternategtm.html?referrer= -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><!-- Google Tag Manager --><script type="text/javascript" async="" src="./analytics.js" nonce=""></script><script nonce="">
  (window.dataLayer=window.dataLayer||[]).push({'gtm.start':new Date().getTime(),event:'gtm.js'});
  </script><script async="" src="./gtm(1).js" nonce=""></script><!-- End Google Tag Manager --><script nonce="">window.addEventListener('message', function(event) {
    if (event.origin != location.origin) {
      return;
    }
    dataLayer.push(event.data);
  });</script></head><body></body></html>