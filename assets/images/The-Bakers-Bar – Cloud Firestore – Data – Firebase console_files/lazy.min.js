(function(){var m,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},da=ca(this),r=function(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ba(c,a,{configurable:!0,writable:!0,value:b})}};
r("Symbol",function(a){if(a)return a;var b=function(g,f){this.$jscomp$symbol$id_=g;ba(this,"description",{configurable:!0,writable:!0,value:f})};b.prototype.toString=function(){return this.$jscomp$symbol$id_};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(g){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(g||"")+"_"+d++,g)};return e});
r("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ea(aa(this))}})}return a});
var ea=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},v=function(a){return a.raw=a},w=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},fa=function(a){if(!(a instanceof Array)){a=w(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},x=function(a,b){return Object.prototype.hasOwnProperty.call(a,
b)},ha=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)x(d,e)&&(a[e]=d[e])}return a};r("Object.assign",function(a){return a||ha});var ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ja;
if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var oa=ja,y=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(oa)oa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.superClass_=b.prototype},pa=function(){this.isRunning_=!1;this.yieldAllIterator_=null;this.yieldResult=void 0;this.nextAddress=1;this.finallyAddress_=this.catchAddress_=0;this.abruptCompletion_=null},qa=function(a){if(a.isRunning_)throw new TypeError("Generator is already running");
a.isRunning_=!0};pa.prototype.next_=function(a){this.yieldResult=a};pa.prototype.throw_=function(a){this.abruptCompletion_={exception:a,isException:!0};this.nextAddress=this.catchAddress_||this.finallyAddress_};pa.prototype.return=function(a){this.abruptCompletion_={return:a};this.nextAddress=this.finallyAddress_};var ra=function(a,b,c){a.nextAddress=c;return{value:b}},sa=function(a){this.context_=new pa;this.program_=a};
sa.prototype.next_=function(a){qa(this.context_);if(this.context_.yieldAllIterator_)return ta(this,this.context_.yieldAllIterator_.next,a,this.context_.next_);this.context_.next_(a);return ua(this)};var va=function(a,b){qa(a.context_);var c=a.context_.yieldAllIterator_;if(c)return ta(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.context_.return);a.context_.return(b);return ua(a)};
sa.prototype.throw_=function(a){qa(this.context_);if(this.context_.yieldAllIterator_)return ta(this,this.context_.yieldAllIterator_["throw"],a,this.context_.next_);this.context_.throw_(a);return ua(this)};
var ta=function(a,b,c,d){try{var e=b.call(a.context_.yieldAllIterator_,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.context_.isRunning_=!1,e;var g=e.value}catch(f){return a.context_.yieldAllIterator_=null,a.context_.throw_(f),ua(a)}a.context_.yieldAllIterator_=null;d.call(a.context_,g);return ua(a)},ua=function(a){for(;a.context_.nextAddress;)try{var b=a.program_(a.context_);if(b)return a.context_.isRunning_=!1,{value:b.value,done:!1}}catch(c){a.context_.yieldResult=
void 0,a.context_.throw_(c)}a.context_.isRunning_=!1;if(a.context_.abruptCompletion_){b=a.context_.abruptCompletion_;a.context_.abruptCompletion_=null;if(b.isException)throw b.exception;return{value:b.return,done:!0}}return{value:void 0,done:!0}},wa=function(a){this.next=function(b){return a.next_(b)};this.throw=function(b){return a.throw_(b)};this.return=function(b){return va(a,b)};this[Symbol.iterator]=function(){return this}},xa=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}
return new Promise(function(d,e){function g(f){f.done?d(f.value):Promise.resolve(f.value).then(b,c).then(g,e)}g(a.next())})},ya=function(a){return xa(new wa(new sa(a)))},za=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};
r("Promise",function(a){function b(){this.batch_=null}function c(f){return f instanceof e?f:new e(function(h){h(f)})}if(a)return a;b.prototype.asyncExecute=function(f){if(this.batch_==null){this.batch_=[];var h=this;this.asyncExecuteFunction(function(){h.executeBatch_()})}this.batch_.push(f)};var d=da.setTimeout;b.prototype.asyncExecuteFunction=function(f){d(f,0)};b.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var f=this.batch_;this.batch_=[];for(var h=0;h<f.length;++h){var k=
f[h];f[h]=null;try{k()}catch(l){this.asyncThrow_(l)}}}this.batch_=null};b.prototype.asyncThrow_=function(f){this.asyncExecuteFunction(function(){throw f;})};var e=function(f){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var h=this.createResolveAndReject_();try{f(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.createResolveAndReject_=function(){function f(l){return function(n){k||(k=!0,l.call(h,n))}}var h=this,k=!1;return{resolve:f(this.resolveTo_),
reject:f(this.reject_)}};e.prototype.resolveTo_=function(f){if(f===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.settleSameAsPromise_(f);else{a:switch(typeof f){case "object":var h=f!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.resolveToNonPromiseObj_(f):this.fulfill_(f)}};e.prototype.resolveToNonPromiseObj_=function(f){var h=void 0;try{h=f.then}catch(k){this.reject_(k);return}typeof h=="function"?this.settleSameAsThenable_(h,
f):this.fulfill_(f)};e.prototype.reject_=function(f){this.settle_(2,f)};e.prototype.fulfill_=function(f){this.settle_(1,f)};e.prototype.settle_=function(f,h){if(this.state_!=0)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.state_);this.state_=f;this.result_=h;this.state_===2&&this.scheduleUnhandledRejectionCheck_();this.executeOnSettledCallbacks_()};e.prototype.scheduleUnhandledRejectionCheck_=function(){var f=this;d(function(){if(f.notifyUnhandledRejection_()){var h=
da.console;typeof h!=="undefined"&&h.error(f.result_)}},1)};e.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var f=da.CustomEvent,h=da.Event,k=da.dispatchEvent;if(typeof k==="undefined")return!0;typeof f==="function"?f=new f("unhandledrejection",{cancelable:!0}):typeof h==="function"?f=new h("unhandledrejection",{cancelable:!0}):(f=da.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.result_;return k(f)};
e.prototype.executeOnSettledCallbacks_=function(){if(this.onSettledCallbacks_!=null){for(var f=0;f<this.onSettledCallbacks_.length;++f)g.asyncExecute(this.onSettledCallbacks_[f]);this.onSettledCallbacks_=null}};var g=new b;e.prototype.settleSameAsPromise_=function(f){var h=this.createResolveAndReject_();f.callWhenSettled_(h.resolve,h.reject)};e.prototype.settleSameAsThenable_=function(f,h){var k=this.createResolveAndReject_();try{f.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.then=
function(f,h){function k(t,q){return typeof t=="function"?function(u){try{l(t(u))}catch(B){n(B)}}:q}var l,n,p=new e(function(t,q){l=t;n=q});this.callWhenSettled_(k(f,l),k(h,n));return p};e.prototype.catch=function(f){return this.then(void 0,f)};e.prototype.callWhenSettled_=function(f,h){function k(){switch(l.state_){case 1:f(l.result_);break;case 2:h(l.result_);break;default:throw Error("Unexpected state: "+l.state_);}}var l=this;this.onSettledCallbacks_==null?g.asyncExecute(k):this.onSettledCallbacks_.push(k);
this.isRejectionHandled_=!0};e.resolve=c;e.reject=function(f){return new e(function(h,k){k(f)})};e.race=function(f){return new e(function(h,k){for(var l=w(f),n=l.next();!n.done;n=l.next())c(n.value).callWhenSettled_(h,k)})};e.all=function(f){var h=w(f),k=h.next();return k.done?c([]):new e(function(l,n){function p(u){return function(B){t[u]=B;q--;q==0&&l(t)}}var t=[],q=0;do t.push(void 0),q++,c(k.value).callWhenSettled_(p(t.length-1),n),k=h.next();while(!k.done)})};return e});
r("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});r("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,g=0;g<e;g++){var f=d[g];if(b.call(c,f,g,d)){b=f;break a}}b=void 0}return b}});
r("WeakMap",function(a){function b(){}function c(k){var l=typeof k;return l==="object"&&k!==null||l==="function"}function d(k){if(!x(k,g)){var l=new b;ba(k,g,{value:l})}}function e(k){var l=Object[k];l&&(Object[k]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return l(n)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),n=new a([[k,2],[l,3]]);if(n.get(k)!=2||n.get(l)!=3)return!1;n.delete(k);n.set(l,4);return!n.has(k)&&n.get(l)==4}catch(p){return!1}}())return a;
var g="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var f=0,h=function(k){this.id_=(f+=Math.random()+1).toString();if(k){k=w(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};h.prototype.set=function(k,l){if(!c(k))throw Error("Invalid WeakMap key");d(k);if(!x(k,g))throw Error("WeakMap key fail: "+k);k[g][this.id_]=l;return this};h.prototype.get=function(k){return c(k)&&x(k,g)?k[g][this.id_]:void 0};h.prototype.has=function(k){return c(k)&&x(k,g)&&x(k[g],
this.id_)};h.prototype.delete=function(k){return c(k)&&x(k,g)&&x(k[g],this.id_)?delete k[g][this.id_]:!1};return h});
r("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(w([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var l=k.entries(),n=l.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=l.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!l.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]=
g();this.size=0;if(h){h=w(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.entry?l.entry.value=k:(l.entry={next:this[1],previous:this[1].previous,head:this[1],key:h,value:k},l.list.push(l.entry),this[1].previous.next=l.entry,this[1].previous=l.entry,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],
h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].previous=g();this.size=0};c.prototype.has=function(h){return!!d(this,h).entry};c.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,
function(h){return h.value})};c.prototype.forEach=function(h,k){for(var l=this.entries(),n;!(n=l.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var l=k&&typeof k;l=="object"||l=="function"?b.has(k)?l=b.get(k):(l=""+ ++f,b.set(k,l)):l="p_"+k;var n=h[0][l];if(n&&x(h[0],l))for(h=0;h<n.length;h++){var p=n[h];if(k!==k&&p.key!==p.key||k===p.key)return{id:l,list:n,index:h,entry:p}}return{id:l,list:n,index:-1,entry:void 0}},e=function(h,
k){var l=h[1];return ea(function(){if(l){for(;l.head!=h[1];)l=l.previous;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})},g=function(){var h={};return h.previous=h.next=h.head=h},f=0;return c});r("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)x(b,d)&&c.push(b[d]);return c}});r("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
r("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var g=d[c];if(g===b||Object.is(g,b))return!0}return!1}});var Aa=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
r("String.prototype.includes",function(a){return a?a:function(b,c){return Aa(this,b,"includes").indexOf(b,c||0)!==-1}});r("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});r("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});r("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});r("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
r("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});r("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});
r("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],g=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof g=="function"){b=g.call(b);for(var f=0;!(g=b.next()).done;)e.push(c.call(d,g.value,f++))}else for(g=b.length,f=0;f<g;f++)e.push(c.call(d,b[f],f));return e}});r("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)x(b,d)&&c.push([d,b[d]]);return c}});
r("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Aa(this,b,"startsWith");b+="";var e=d.length,g=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var f=0;f<g&&c<e;)if(d[c++]!=b[f++])return!1;return f>=g}});var Ba=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var g=c++;return{value:b(g,a[g]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};
r("Array.prototype.entries",function(a){return a?a:function(){return Ba(this,function(b,c){return[b,c]})}});r("Array.prototype.keys",function(a){return a?a:function(){return Ba(this,function(b){return b})}});r("Array.prototype.values",function(a){return a?a:function(){return Ba(this,function(b,c){return c})}});
r("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Ca=Ca||{},z=this||self,Da=function(a,b,c){a=a.split(".");c=c||z;a[0]in c||typeof c.execScript=="undefined"||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b},Ea=function(a,b){var c=A("CLOSURE_FLAGS");a=c&&c[a];return a!=null?a:b},A=function(a,b){a=a.split(".");b=b||z;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b},Fa=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":
b:"null"},Ha=function(a){var b=Fa(a);return b=="array"||b=="object"&&typeof a.length=="number"},Ia=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"},Ja=function(a,b,c){return a.call.apply(a.bind,arguments)},Ka=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},
C=function(a,b,c){C=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?Ja:Ka;return C.apply(null,arguments)},D=function(a,b){function c(){}c.prototype=b.prototype;a.superClass_=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.base=function(d,e,g){for(var f=Array(arguments.length-2),h=2;h<arguments.length;h++)f[h-2]=arguments[h];return b.prototype[e].apply(d,f)}},La=function(a){return a};function E(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,E);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)}D(E,Error);E.prototype.name="CustomError";var Ma;function Na(a){z.setTimeout(function(){throw a;},0)};var Oa=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};var Pa=Ea(610401301,!1),Qa=Ea(645172343,Ea(1,!0));function Ra(){var a=z.navigator;return a&&(a=a.userAgent)?a:""}var Sa,Ta=z.navigator;Sa=Ta?Ta.userAgentData||null:null;function Ua(a){return Pa?Sa?Sa.brands.some(function(b){return(b=b.brand)&&b.indexOf(a)!=-1}):!1:!1}function F(a){return Ra().indexOf(a)!=-1};function Va(){return Pa?!!Sa&&Sa.brands.length>0:!1}function Wa(){return Va()?Ua("Chromium"):(F("Chrome")||F("CriOS"))&&!(Va()?0:F("Edge"))||F("Silk")};var Xa=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},Ya=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)},Za=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,
b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function $a(a,b){b=Xa(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c}function ab(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};var bb=Va()?!1:F("Trident")||F("MSIE"),cb=Ra().toLowerCase().indexOf("webkit")!=-1&&!F("Edge"),db=cb&&F("Mobile");!F("Android")||Wa();Wa();F("Safari")&&(Wa()||(Va()?0:F("Coast"))||(Va()?0:F("Opera"))||(Va()?0:F("Edge"))||(Va()?Ua("Microsoft Edge"):F("Edg/"))||Va()&&Ua("Opera"));var eb={},fb=null,gb=function(a,b){b===void 0&&(b=0);if(!fb){fb={};for(var c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),d=["+/=","+/","-_=","-_.","-_"],e=0;e<5;e++){var g=c.concat(d[e].split(""));eb[e]=g;for(var f=0;f<g.length;f++){var h=g[f];fb[h]===void 0&&(fb[h]=f)}}}b=eb[b];c=Array(Math.floor(a.length/3));d=b[64]||"";for(e=g=0;g<a.length-2;g+=3){var k=a[g],l=a[g+1];h=a[g+2];f=b[k>>2];k=b[(k&3)<<4|l>>4];l=b[(l&15)<<2|h>>6];h=b[h&63];c[e++]=""+f+k+l+h}f=0;h=d;switch(a.length-
g){case 2:f=a[g+1],h=b[(f&15)<<2]||d;case 1:a=a[g],c[e]=""+b[a>>2]+b[(a&3)<<4|f>>4]+h+d}return c.join("")},hb=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return gb(b,2)};var ib=typeof Uint8Array!=="undefined",jb=!bb&&typeof btoa==="function";function kb(){return typeof BigInt==="function"};function lb(a){return Array.prototype.slice.call(a)};var mb=typeof Symbol==="function"&&typeof Symbol()==="symbol";function ob(a){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?Symbol():a}var pb=ob(),qb=ob("2ex"),rb=ob("0dg"),sb=ob("64big");Math.max.apply(Math,fa(Object.values({IS_REPEATED_FIELD:1,IS_IMMUTABLE_ARRAY:2,IS_API_FORMATTED:4,ONLY_MUTABLE_VALUES:8,ONLY_IMMUTABLE_VALUES_IF_OWNED:16,MUTABLE_REFERENCES_ARE_OWNED:32,CONSTRUCTED:64,TRANSFERRED:128,HAS_SPARSE_OBJECT:256,HAS_MESSAGE_ID:512,IS_IMMUTABLE_JS_REPEATED_FIELD_COERCED_FROM_WIRE:1024,FROZEN_ARRAY:2048,STRING_FORMATTED:4096,GBIGINT_FORMATTED:8192})));
var tb=mb?function(a,b){a[pb]|=b}:function(a,b){a.internalArrayState!==void 0?a.internalArrayState|=b:Object.defineProperties(a,{internalArrayState:{value:b,configurable:!0,writable:!0,enumerable:!1}})},ub=mb?function(a){return a[pb]|0}:function(a){return a.internalArrayState|0},G=mb?function(a){return a[pb]}:function(a){return a.internalArrayState},H=mb?function(a,b){a[pb]=b}:function(a,b){a.internalArrayState!==void 0?a.internalArrayState=b:Object.defineProperties(a,{internalArrayState:{value:b,
configurable:!0,writable:!0,enumerable:!1}})};function vb(a,b){H(b,(a|0)&-14591)}function wb(a,b){H(b,(a|34)&-14557)};var xb={},yb={};function zb(a){return!(!a||typeof a!=="object"||a.mapPrototypeMarker!==yb)}function Ab(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object}function Bb(a){return!Array.isArray(a)||a.length?!1:ub(a)&1?!0:!1}var Cb,Db=[];H(Db,55);Cb=Object.freeze(Db);function Eb(a){if(a&2)throw Error();}var Fb=Object.freeze({});Object.freeze({});var Gb=Object.freeze({});var I=0,J=0;function Hb(a){var b=a>>>0;I=b;J=(a-b)/4294967296>>>0}function Ib(a){if(a<0){Hb(0-a);var b=w(Jb(I,J));a=b.next().value;b=b.next().value;I=a>>>0;J=b>>>0}else Hb(a)}function Kb(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else kb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+Lb(c)+Lb(a));return c}
function Lb(a){a=String(a);return"0000000".slice(a.length)+a}function Mb(){var a=I,b=J;b&2147483648?kb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=w(Jb(a,b)),a=b.next().value,b=b.next().value,a="-"+Kb(a,b)):a=Kb(a,b);return a}function Jb(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var Nb=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var Ob;function Pb(){var a=Error();Nb(a,"incident");Na(a)}function Qb(a){a=Error(a);Nb(a,"warning");return a}function Rb(a,b){if(b!=null){if(a==null){var c;a=(c=Ob)!=null?c:Ob={}}else a=a.constructor;c=a[b]||0;c>=4||(a[b]=c+1,Pb())}};function Sb(a){a.isGuard_doNotManuallySetPrettyPlease=!0;return a};var Tb=Sb(function(a){return typeof a==="number"}),Ub=Sb(function(a){return typeof a==="string"}),Vb=Sb(function(a){return typeof a==="boolean"});var Wb=typeof z.BigInt==="function"&&typeof z.BigInt(0)==="bigint";function Xb(a){var b=a;if(Ub(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Tb(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Wb?BigInt(a):a=Vb(a)?a?"1":"0":Ub(a)?a.trim()||"0":String(a)}var cc=Sb(function(a){return Wb?a>=Yb&&a<=Zb:a[0]==="-"?$b(a,ac):$b(a,bc)}),ac=Number.MIN_SAFE_INTEGER.toString(),Yb=Wb?BigInt(Number.MIN_SAFE_INTEGER):void 0,bc=Number.MAX_SAFE_INTEGER.toString(),Zb=Wb?BigInt(Number.MAX_SAFE_INTEGER):void 0;
function $b(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};function dc(a){return a.displayName||a.name||"unknown type name"}var ec=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function fc(a){var b=typeof a;switch(b){case "bigint":return!0;case "number":return Number.isFinite(a)}return b!=="string"?!1:ec.test(a)}function gc(a){if(!Number.isFinite(a))throw Qb("enum");return a|0}function hc(a){return a==null?a:Number.isFinite(a)?a|0:void 0}function ic(a){if(typeof a!=="number")throw Qb("int32");if(!Number.isFinite(a))throw Qb("int32");return a|0}
function jc(a){var b=0;b=b===void 0?0:b;if(!fc(a))throw Qb("int64");var c=typeof a;switch(b){case 4096:switch(c){case "string":return kc(a);case "bigint":return String(BigInt.asIntN(64,a));default:return fc(a),a=Math.trunc(a),Number.isSafeInteger(a)?a=String(a):(b=String(a),lc(b)?a=b:(Ib(a),a=Mb())),a}case 8192:switch(c){case "string":return b=Math.trunc(Number(a)),Number.isSafeInteger(b)?a=Xb(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=kb()?Xb(BigInt.asIntN(64,BigInt(a))):Xb(mc(a))),a;case "bigint":return Xb(BigInt.asIntN(64,
a));default:return Xb(nc(a))}case 0:switch(c){case "string":return kc(a);case "bigint":return Xb(BigInt.asIntN(64,a));default:return nc(a)}default:throw Error("Unknown format requested type for int64");}}function lc(a){return a[0]==="-"?a.length<20?!0:a.length===20&&Number(a.substring(0,7))>-922337:a.length<19?!0:a.length===19&&Number(a.substring(0,6))<922337}
function mc(a){if(lc(a))return a;if(a.length<16)Ib(Number(a));else if(kb())a=BigInt(a),I=Number(a&BigInt(4294967295))>>>0,J=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");J=I=0;for(var c=a.length,d=0+b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),J*=1E6,I=I*1E6+d,I>=4294967296&&(J+=Math.trunc(I/4294967296),J>>>=0,I>>>=0);b&&(b=w(Jb(I,J)),a=b.next().value,b=b.next().value,I=a,J=b)}return Mb()}
function nc(a){fc(a);a=Math.trunc(a);if(!Number.isSafeInteger(a)){Ib(a);var b=I,c=J;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=c*4294967296+(b>>>0);a=a?-b:b}return a}function kc(a){fc(a);var b=Math.trunc(Number(a));if(Number.isSafeInteger(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return mc(a)}function oc(a){if(typeof a!=="string")throw Error();return a}function K(a){if(a!=null&&typeof a!=="string")throw Error();return a}
function pc(a){return a==null||typeof a==="string"?a:void 0};var qc;function rc(a,b){qc=b;a=new a(b);qc=void 0;return a}
function L(a,b,c){a==null&&(a=qc);qc=void 0;if(a==null){var d=96;c?(a=[c],d|=512):a=[];b&&(d=d&-16760833|(b&1023)<<14)}else{if(!Array.isArray(a))throw Error("narr");d=ub(a);if(d&2048)throw Error("farr");if(d&64)return a;d|=64;if(c&&(d|=512,c!==a[0]))throw Error("mid");a:{c=a;var e=c.length;if(e){var g=e-1;if(Ab(c[g])){d|=256;b=g-(+!!(d&512)-1);if(b>=1024)throw Error("pvtlmt");d=d&-16760833|(b&1023)<<14;break a}}if(b){b=Math.max(b,e-(+!!(d&512)-1));if(b>1024)throw Error("spvt");d=d&-16760833|(b&1023)<<
14}}}H(a,d);return a};function sc(a,b){return tc(b)}function tc(a){switch(typeof a){case "number":return isFinite(a)?a:String(a);case "boolean":return a?1:0;case "object":if(a)if(Array.isArray(a)){if(Bb(a))return}else if(ib&&a!=null&&a instanceof Uint8Array){if(jb){for(var b="",c=0,d=a.length-10240;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);a=btoa(b)}else a=gb(a);return a}}return a};function uc(a,b,c){a=lb(a);var d=a.length,e=b&256?a[d-1]:void 0;d+=e?-1:0;for(b=b&512?1:0;b<d;b++)a[b]=c(a[b]);if(e){b=a[b]={};for(var g in e)b[g]=c(e[g])}return a}function vc(a,b,c,d,e){if(a!=null){if(Array.isArray(a))a=Bb(a)?void 0:e&&ub(a)&2?a:wc(a,b,c,d!==void 0,e);else if(Ab(a)){var g={},f;for(f in a)g[f]=vc(a[f],b,c,d,e);a=g}else a=b(a,d);return a}}function wc(a,b,c,d,e){var g=d||c?ub(a):0;d=d?!!(g&32):void 0;a=lb(a);for(var f=0;f<a.length;f++)a[f]=vc(a[f],b,c,d,e);c&&c(g,a);return a}
function xc(a){return a.messagePrototypeMarker===xb?a.toJSON():tc(a)};function yc(a,b,c){c=c===void 0?wb:c;if(a!=null){if(ib&&a instanceof Uint8Array)return b?a:new Uint8Array(a);if(Array.isArray(a)){var d=ub(a);if(d&2)return a;b&&(b=d===0||!!(d&32)&&!(d&64||!(d&16)));return b?(H(a,(d|34)&-12293),a):wc(a,yc,d&4?wb:c,!0,!0)}a.messagePrototypeMarker===xb&&(c=a.internalArray_,d=G(c),a=d&2?a:rc(a.constructor,zc(c,d,!0)));return a}}function zc(a,b,c){var d=c||b&2?wb:vb,e=!!(b&32);a=uc(a,b,function(g){return yc(g,e,d)});tb(a,32|(c?2:0));return a};var Ec=function(a){var b=Ac(a);if(b)return b;if(Math.random()>.01)return a;if(Bc===void 0)if(typeof Proxy!=="function")Bc=null;else try{Bc=Proxy.toString().indexOf("[native code]")!==-1?Proxy:null}catch(c){Bc=null}b=Bc;if(!b)return a;b=new b(a,{set:function(c,d,e){Cc();c[d]=e;return!0}});Dc(a,b);return b};function Cc(){Pb()}var Fc=void 0,Gc=void 0,Ac=function(a){var b;return(b=Fc)==null?void 0:b.get(a)};function Dc(a,b){(Fc||(Fc=new WeakMap)).set(a,b);(Gc||(Gc=new WeakMap)).set(b,a)}var Bc=void 0;function Hc(a,b,c,d){if(!(4&b))return!0;if(c==null)return!1;!d&&c===0&&(4096&b||8192&b)&&(a.constructor[rb]=(a.constructor[rb]|0)+1)<5&&Pb();return c===0?!1:!(c&b)}var Jc=function(a,b){a=a.internalArray_;return Ic(a,G(a),b)};function Kc(a,b,c,d){b=d+(+!!(b&512)-1);if(!(b<0||b>=a.length||b>=c))return a[b]}
var Ic=function(a,b,c,d){if(c===-1)return null;var e=b>>14&1023||536870912;if(c>=e){if(b&256)return a[a.length-1][c]}else{var g=a.length;return d&&b&256&&(d=a[g-1][c],d!=null)?(Kc(a,b,e,c)&&Rb(void 0,qb),d):Kc(a,b,e,c)}},Mc=function(a,b,c){var d=a.internalArray_,e=G(d);Eb(e);Lc(d,e,b,c);return a};
function Lc(a,b,c,d,e){var g=b>>14&1023||536870912;if(c>=g||e&&!Qa){var f=b;if(b&256)e=a[a.length-1];else{if(d==null)return f;e=a[g+(+!!(b&512)-1)]={};f|=256}e[c]=d;c<g&&(a[c+(+!!(b&512)-1)]=void 0);f!==b&&H(a,f);return f}a[c+(+!!(b&512)-1)]=d;b&256&&(a=a[a.length-1],c in a&&delete a[c]);return b}var Nc=function(a){return void 0===Fb?2:a?4:5};
function Oc(a,b,c,d,e){var g=a.internalArray_,f=G(g),h=2&f?1:d;e=!!e;d=Ic(g,f,b);d=Array.isArray(d)?d:Cb;var k=ub(d);if(Hc(a,k,void 0,e)){if(4&k||Object.isFrozen(d))d=lb(d),k=Pc(k,f),f=Lc(g,f,b,d);for(var l=a=0;a<d.length;a++){var n=c(d[a]);n!=null&&(d[l++]=n)}l<a&&(d.length=l);k===0&&(k=Pc(k,f));k|=21;k&=-12289;H(d,k);2&k&&Object.freeze(d)}var p;h===1||h===4&&32&k?Qc(k)||(e=k,k|=2,k!==e&&H(d,k),Object.freeze(d)):(c=h!==5?!1:!!(32&k)||Qc(k)||!!Ac(d),(h===2||c)&&Qc(k)&&(d=lb(d),k=Pc(k,f),k=Rc(k,f,
e),H(d,k),f=Lc(g,f,b,d)),Qc(k)||(b=k,k=Rc(k,f,e),k!==b&&H(d,k)),c&&(p=Ec(d)));return p||d}function Qc(a){return!!(2&a)&&!!(4&a)||!!(2048&a)}
function Sc(a,b,c,d){var e=a.internalArray_,g=G(e);Eb(g);if(c==null)return Lc(e,g,b),a;var f=c,h;c=((h=Gc)==null?void 0:h.get(f))||f;if(!Array.isArray(c))throw Qb();h=f=ub(c);var k=!!(2&f)||Object.isFrozen(c),l=!k&&(void 0===Gb||!1);if(Hc(a,f))for(f=21,k&&(c=lb(c),h=0,f=Pc(f,g),f=Rc(f,g,!0)),k=0;k<c.length;k++)c[k]=d(c[k]);l&&(c=lb(c),h=0,f=Pc(f,g),f=Rc(f,g,!0));f!==h&&H(c,f);Lc(e,g,b,c);return a}
function M(a,b,c,d){var e=a.internalArray_,g=G(e);Eb(g);Lc(e,g,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}function Tc(a,b,c,d){a=a.internalArray_;var e=G(a),g=Ic(a,e,c,d);if(g!=null&&typeof g==="object"&&g.messagePrototypeMarker===xb)b=g;else if(Array.isArray(g)){var f=ub(g),h=f;h===0&&(h|=e&32);h|=e&2;h!==f&&H(g,h);b=new b(g)}else b=void 0;b!==g&&b!=null&&Lc(a,e,c,b,d);return b}
var N=function(a,b,c){var d=d===void 0?!1:d;b=Tc(a,b,c,d);if(b==null)return b;a=a.internalArray_;var e=G(a);if(!(e&2)){var g=b;var f=g.internalArray_,h=G(f);g=h&2?rc(g.constructor,zc(f,h,!1)):g;g!==b&&(b=g,Lc(a,e,c,b,d))}return b},O=function(a,b,c,d){if(d!=null){if(!(d instanceof b))throw Error("Expected instanceof "+dc(b)+" but got "+(d&&dc(d.constructor)));}else d=void 0;return Mc(a,c,d)};function Pc(a,b){a=2&b?a|2:a&-3;return(a|32)&-2049}function Rc(a,b,c){32&b&&c||(a&=-33);return a}
var P=function(a,b){var c=c===void 0?!1:c;a=Jc(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c},Uc=function(a){var b=b===void 0?0:b;a=Jc(a,1);a!=null&&(typeof a==="bigint"?cc(a)?a=Number(a):(a=BigInt.asIntN(64,a),a=cc(a)?Number(a):String(a)):a=fc(a)?typeof a==="number"?nc(a):kc(a):void 0);return a!=null?a:b},Vc=function(a,b){var c=c===void 0?"":c;a=pc(Jc(a,b));return a!=null?a:c},Wc=function(a,b,c){a=Oc(a,b,pc,3,!0);if(typeof c!=="number"||c<0||c>=a.length)throw Error();
return a[c]},Q=function(a,b,c){if(c!=null&&typeof c!=="boolean")throw Error("Expected boolean but got "+Fa(c)+": "+c);return M(a,b,c,!1)};var Xc,R=function(a,b,c){this.internalArray_=L(a,b,c)};R.prototype.toJSON=function(){return Yc(this)};var Zc=function(a){try{return Xc=!0,JSON.stringify(Yc(a),sc)}finally{Xc=!1}},$c=function(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");tb(b,32);return rc(a,b)};m=R.prototype;
m.getExtension=function(a){var b=a.ctor?a.isRepeated?a.getExtensionFn(this,a.ctor,a.fieldIndex,Nc(!0),!0):a.getExtensionFn(this,a.ctor,a.fieldIndex,!0):a.isRepeated?a.getExtensionFn(this,a.fieldIndex,Nc(!0),!0):a.getExtensionFn(this,a.fieldIndex,a.defaultValue,!0);return a.isDelegating&&b==null?a.defaultValue:b};
m.hasExtension=function(a){a.ctor?a=Tc(this,a.ctor,a.fieldIndex,!0)!==void 0:(a=a.ctor?a.getExtensionFn(this,a.ctor,a.fieldIndex,!0):a.getExtensionFn(this,a.fieldIndex,null,!0),a=(a===null?void 0:a)!==void 0);return a};m.clone=function(){var a=this.internalArray_;return rc(this.constructor,zc(a,G(a),!1))};m.messagePrototypeMarker=xb;m.toString=function(){try{return Xc=!0,Yc(this).toString()}finally{Xc=!1}};
function Yc(a){a=Xc?a.internalArray_:wc(a.internalArray_,xc,void 0,void 0,!1);var b=!Xc,c=a.length;if(c){var d=a[c-1],e=Ab(d);e?c--:d=void 0;var g=a;if(e){b:{var f=d;var h={};e=!1;if(f)for(var k in f)if(isNaN(+k))h[k]=f[k];else{var l=f[k];Array.isArray(l)&&(Bb(l)||zb(l)&&l.size===0)&&(l=null);l==null&&(e=!0);l!=null&&(h[k]=l)}if(e){for(var n in h)break b;h=null}else h=f}f=h==null?d!=null:h!==d}for(;c>0;c--){k=g[c-1];if(!(k==null||Bb(k)||zb(k)&&k.size===0))break;var p=!0}if(g!==a||f||p){if(!b)g=Array.prototype.slice.call(g,
0,c);else if(p||f||h)g.length=c;h&&g.push(h)}p=g}else p=a;return p};var ad=function(){return!0},bd=function(){};function cd(a,b){for(var c in a)b.call(void 0,a[c],c,a)}function dd(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}var ed="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function fd(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var g=0;g<ed.length;g++)c=ed[g],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var id=function(a,b){this.stringConstValueWithSecurityContract__googStringSecurityPrivate_=a===gd&&b||"";this.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_=hd};id.prototype.toString=function(){return this.stringConstValueWithSecurityContract__googStringSecurityPrivate_};
var jd=function(a){return a instanceof id&&a.constructor===id&&a.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_===hd?a.stringConstValueWithSecurityContract__googStringSecurityPrivate_:"type_error:Const"},S=function(a){return new id(gd,a)},hd={},gd={};var kd;var ld=function(a){this.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_=a};ld.prototype.toString=function(){return this.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_+""};
var md=function(a){return a instanceof ld&&a.constructor===ld?a.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_:"type_error:TrustedResourceUrl"},qd=function(a,b){var c=jd(a);if(!nd.test(c))throw Error("Invalid TrustedResourceUrl format: "+c);a=c.replace(od,function(d,e){if(!Object.prototype.hasOwnProperty.call(b,e))throw Error('Found marker, "'+e+'", in format string, "'+c+'", but no valid label mapping found in args: '+JSON.stringify(b));d=b[e];return d instanceof id?jd(d):encodeURIComponent(String(d))});
return pd(a)},od=/%{(\w+)}/g,nd=RegExp("^((https:)?//[0-9a-z.:[\\]-]+/|/[^/\\\\]|[^:/\\\\%]+/|[^:/\\\\%]*[?#]|about:blank#)","i"),rd={},pd=function(a){if(kd===void 0){var b=null;var c=z.trustedTypes;if(c&&c.createPolicy)try{b=c.createPolicy("uf-la#html",{createHTML:La,createScript:La,createScriptURL:La})}catch(d){z.console&&z.console.error(d.message)}kd=b}a=(b=kd)?b.createScriptURL(a):a;return new ld(a,rd)};/*

 SPDX-License-Identifier: Apache-2.0
*/
var sd=function(a){this.privateDoNotAccessOrElseWrappedUrl=a};sd.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedUrl};var td=new sd("about:invalid#zClosurez");function ud(a){if(a instanceof sd)return a.privateDoNotAccessOrElseWrappedUrl;throw Error("");};var vd=function(a){this.isValid=a};function wd(a){return new vd(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var xd=[wd("data"),wd("http"),wd("https"),wd("mailto"),wd("ftp"),new vd(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function yd(a){var b=b===void 0?xd:b;a:if(b=b===void 0?xd:b,!(a instanceof sd)){for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof vd&&d.isValid(a)){a=new sd(a);break a}}a=void 0}return a||td}var zd=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function Ad(a){a=a instanceof sd?ud(a):zd.test(a)?a:void 0;return a};function T(a){var b=za.apply(1,arguments);if(b.length===0)return pd(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return pd(c)};var Cd=function(a,b){a:{var c=(a.ownerDocument&&a.ownerDocument.defaultView||z).document;if(c.querySelector&&(c=c.querySelector("script[nonce]"))&&(c=c.nonce||c.getAttribute("nonce"))&&Bd.test(c))break a;c=""}c&&a.setAttribute("nonce",c);a.src=md(b)},Bd=/^[\w+/_-]+[=]{0,2}$/;var Dd="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Ed(a,b){b=Ad(b);b!==void 0&&a.open(b,void 0,void 0)}function Fd(a){var b,c;return(a=(c=(b=a.document).querySelector)==null?void 0:c.call(b,"script[nonce]"))?a.nonce||a.getAttribute("nonce")||"":""};function Id(a,b){a.src=md(b);(b=Fd(a.ownerDocument&&a.ownerDocument.defaultView||window))&&a.setAttribute("nonce",b)};var Ld=function(a){return a?new Jd(Kd(a)):Ma||(Ma=new Jd)},Nd=function(a,b){cd(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:Md.hasOwnProperty(d)?a.setAttribute(Md[d],c):d.lastIndexOf("aria-",0)==0||d.lastIndexOf("data-",0)==0?a.setAttribute(d,c):a[d]=c})},Md={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",
valign:"vAlign",width:"width"},Od=function(a,b,c){function d(h){h&&b.appendChild(typeof h==="string"?a.createTextNode(h):h)}for(var e=1;e<c.length;e++){var g=c[e];if(!Ha(g)||Ia(g)&&g.nodeType>0)d(g);else{a:{if(g&&typeof g.length=="number"){if(Ia(g)){var f=typeof g.item=="function"||typeof g.item=="string";break a}if(typeof g==="function"){f=typeof g.item=="function";break a}}f=!1}Ya(f?ab(g):g,d)}}},Pd=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)},
Qd=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null},Kd=function(a){return a.nodeType==9?a:a.ownerDocument||a.document},Jd=function(a){this.document_=a||z.document||document};m=Jd.prototype;m.getElementsByTagName=function(a,b){return(b||this.document_).getElementsByTagName(String(a))};m.createElement=function(a){return Pd(this.document_,a)};m.createTextNode=function(a){return this.document_.createTextNode(String(a))};m.getWindow=function(){return this.document_.defaultView};
m.appendChild=function(a,b){a.appendChild(b)};m.append=function(a,b){Od(Kd(a),a,arguments)};m.canHaveChildren=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
m.removeNode=Qd;m.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};var Rd=function(a,b){this.limit_=100;this.create_=a;this.reset_=b;this.occupants_=0;this.head_=null};Rd.prototype.get=function(){if(this.occupants_>0){this.occupants_--;var a=this.head_;this.head_=a.next;a.next=null}else a=this.create_();return a};Rd.prototype.put=function(a){this.reset_(a);this.occupants_<this.limit_&&(this.occupants_++,a.next=this.head_,this.head_=a)};var Sd,Td=function(){var a=z.MessageChannel;typeof a==="undefined"&&typeof window!=="undefined"&&window.postMessage&&window.addEventListener&&!F("Presto")&&(a=function(){var e=Pd(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var g=e.contentWindow;e=g.document;e.open();e.close();var f="callImmediate"+Math.random(),h=g.location.protocol=="file:"?"*":g.location.protocol+"//"+g.location.host;e=C(function(k){if((h=="*"||k.origin==h)&&k.data==f)this.port1.onmessage()},
this);g.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){g.postMessage(f,h)}}});if(typeof a!=="undefined"){var b=new a,c={},d=c;b.port1.onmessage=function(){if(c.next!==void 0){c=c.next;var e=c.cb;c.cb=null;e()}};return function(e){d.next={cb:e};d=d.next;b.port2.postMessage(0)}}return function(e){z.setTimeout(e,0)}};var Ud=function(){this.workTail_=this.workHead_=null};Ud.prototype.add=function(a,b){var c=Vd.get();c.set(a,b);this.workTail_?this.workTail_.next=c:this.workHead_=c;this.workTail_=c};Ud.prototype.remove=function(){var a=null;this.workHead_&&(a=this.workHead_,this.workHead_=this.workHead_.next,this.workHead_||(this.workTail_=null),a.next=null);return a};var Vd=new Rd(function(){return new Wd},function(a){return a.reset()}),Wd=function(){this.next=this.scope=this.fn=null};
Wd.prototype.set=function(a,b){this.fn=a;this.scope=b;this.next=null};Wd.prototype.reset=function(){this.next=this.scope=this.fn=null};var Xd,Yd=!1,Zd=new Ud,ae=function(a,b){Xd||$d();Yd||(Xd(),Yd=!0);Zd.add(a,b)},$d=function(){if(z.Promise&&z.Promise.resolve){var a=z.Promise.resolve(void 0);Xd=function(){a.then(be)}}else Xd=function(){var b=be;typeof z.setImmediate!=="function"||z.Window&&z.Window.prototype&&z.Window.prototype.setImmediate==z.setImmediate?(Sd||(Sd=Td()),Sd(b)):z.setImmediate(b)}},be=function(){for(var a;a=Zd.remove();){try{a.fn.call(a.scope)}catch(b){Na(b)}Vd.put(a)}Yd=!1};var ce=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};var U=function(a){this.state_=0;this.result_=void 0;this.callbackEntriesTail_=this.callbackEntries_=this.parent_=null;this.hadUnhandledRejection_=this.executing_=!1;if(a!=bd)try{var b=this;a.call(void 0,function(c){de(b,2,c)},function(c){de(b,3,c)})}catch(c){de(this,3,c)}},ee=function(){this.next=this.context=this.onRejected=this.onFulfilled=this.child=null;this.always=!1};ee.prototype.reset=function(){this.context=this.onRejected=this.onFulfilled=this.child=null;this.always=!1};
var fe=new Rd(function(){return new ee},function(a){a.reset()}),ge=function(a,b,c){var d=fe.get();d.onFulfilled=a;d.onRejected=b;d.context=c;return d},he=function(a){return new U(function(b,c){c(a)})},je=function(){var a,b,c=new U(function(d,e){a=d;b=e});return new ie(c,a,b)};U.prototype.then=function(a,b,c){return ke(this,typeof a==="function"?a:null,typeof b==="function"?b:null,c)};U.prototype.$goog_Thenable=!0;U.prototype.thenCatch=function(a,b){return ke(this,null,a,b)};U.prototype.catch=U.prototype.thenCatch;
U.prototype.cancel=function(a){if(this.state_==0){var b=new le(a);ae(function(){me(this,b)},this)}};
var me=function(a,b){if(a.state_==0)if(a.parent_){var c=a.parent_;if(c.callbackEntries_){for(var d=0,e=null,g=null,f=c.callbackEntries_;f&&(f.always||(d++,f.child==a&&(e=f),!(e&&d>1)));f=f.next)e||(g=f);e&&(c.state_==0&&d==1?me(c,b):(g?(d=g,d.next==c.callbackEntriesTail_&&(c.callbackEntriesTail_=d),d.next=d.next.next):ne(c),oe(c,e,3,b)))}a.parent_=null}else de(a,3,b)},qe=function(a,b){a.callbackEntries_||a.state_!=2&&a.state_!=3||pe(a);a.callbackEntriesTail_?a.callbackEntriesTail_.next=b:a.callbackEntries_=
b;a.callbackEntriesTail_=b},ke=function(a,b,c,d){var e=ge(null,null,null);e.child=new U(function(g,f){e.onFulfilled=b?function(h){try{var k=b.call(d,h);g(k)}catch(l){f(l)}}:g;e.onRejected=c?function(h){try{var k=c.call(d,h);k===void 0&&h instanceof le?f(h):g(k)}catch(l){f(l)}}:f});e.child.parent_=a;qe(a,e);return e.child};U.prototype.unblockAndFulfill_=function(a){this.state_=0;de(this,2,a)};U.prototype.unblockAndReject_=function(a){this.state_=0;de(this,3,a)};
var de=function(a,b,c){if(a.state_==0){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.state_=1;a:{var d=c,e=a.unblockAndFulfill_,g=a.unblockAndReject_;if(d instanceof U){qe(d,ge(e||bd,g||null,a));var f=!0}else if(ce(d))d.then(e,g,a),f=!0;else{if(Ia(d))try{var h=d.then;if(typeof h==="function"){re(d,h,e,g,a);f=!0;break a}}catch(k){g.call(a,k);f=!0;break a}f=!1}}f||(a.result_=c,a.state_=b,a.parent_=null,pe(a),b!=3||c instanceof le||se(a,c))}},re=function(a,b,c,d,e){var g=!1,f=function(k){g||
(g=!0,c.call(e,k))},h=function(k){g||(g=!0,d.call(e,k))};try{b.call(a,f,h)}catch(k){h(k)}},pe=function(a){a.executing_||(a.executing_=!0,ae(a.executeCallbacks_,a))},ne=function(a){var b=null;a.callbackEntries_&&(b=a.callbackEntries_,a.callbackEntries_=b.next,b.next=null);a.callbackEntries_||(a.callbackEntriesTail_=null);return b};U.prototype.executeCallbacks_=function(){for(var a;a=ne(this);)oe(this,a,this.state_,this.result_);this.executing_=!1};
var oe=function(a,b,c,d){if(c==3&&b.onRejected&&!b.always)for(;a&&a.hadUnhandledRejection_;a=a.parent_)a.hadUnhandledRejection_=!1;if(b.child)b.child.parent_=null,te(b,c,d);else try{b.always?b.onFulfilled.call(b.context):te(b,c,d)}catch(e){ue.call(null,e)}fe.put(b)},te=function(a,b,c){b==2?a.onFulfilled.call(a.context,c):a.onRejected&&a.onRejected.call(a.context,c)},se=function(a,b){a.hadUnhandledRejection_=!0;ae(function(){a.hadUnhandledRejection_&&ue.call(null,b)})},ue=Na,le=function(a){E.call(this,
a)};D(le,E);le.prototype.name="cancel";var ie=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
var we=function(a){var b=ve;this.sequence_=[];this.onCancelFunction_=b;this.defaultScope_=a||null;this.hadError_=this.fired_=!1;this.result_=void 0;this.silentlyCanceled_=this.blocking_=this.blocked_=!1;this.unhandledErrorId_=0;this.parent_=null;this.branches_=0};
we.prototype.cancel=function(a){if(this.fired_)this.result_ instanceof we&&this.result_.cancel();else{if(this.parent_){var b=this.parent_;delete this.parent_;a?b.cancel(a):(b.branches_--,b.branches_<=0&&b.cancel())}this.onCancelFunction_?this.onCancelFunction_.call(this.defaultScope_,this):this.silentlyCanceled_=!0;this.fired_||(a=new xe(this),ye(this),ze(this,!1,a))}};we.prototype.continue_=function(a,b){this.blocked_=!1;ze(this,a,b)};
var ze=function(a,b,c){a.fired_=!0;a.result_=c;a.hadError_=!b;Ae(a)},ye=function(a){if(a.fired_){if(!a.silentlyCanceled_)throw new Be(a);a.silentlyCanceled_=!1}};we.prototype.callback=function(a){ye(this);ze(this,!0,a)};we.prototype.addCallback=function(a,b){return Ce(this,a,null,b)};var Ce=function(a,b,c,d){a.sequence_.push([b,c,d]);a.fired_&&Ae(a);return a};
we.prototype.then=function(a,b,c){var d,e,g=new U(function(f,h){e=f;d=h});Ce(this,e,function(f){f instanceof xe?g.cancel():d(f);return De},this);return g.then(a,b,c)};we.prototype.$goog_Thenable=!0;
var Ee=function(a){return Za(a.sequence_,function(b){return typeof b[1]==="function"})},De={},Ae=function(a){if(a.unhandledErrorId_&&a.fired_&&Ee(a)){var b=a.unhandledErrorId_,c=Fe[b];c&&(z.clearTimeout(c.id_),delete Fe[b]);a.unhandledErrorId_=0}a.parent_&&(a.parent_.branches_--,delete a.parent_);b=a.result_;for(var d=c=!1;a.sequence_.length&&!a.blocked_;){var e=a.sequence_.shift(),g=e[0],f=e[1];e=e[2];if(g=a.hadError_?f:g)try{var h=g.call(e||a.defaultScope_,b);h===De&&(h=void 0);h!==void 0&&(a.hadError_=
a.hadError_&&(h==b||h instanceof Error),a.result_=b=h);if(ce(b)||typeof z.Promise==="function"&&b instanceof z.Promise)d=!0,a.blocked_=!0}catch(k){b=k,a.hadError_=!0,Ee(a)||(c=!0)}}a.result_=b;d&&(h=C(a.continue_,a,!0),d=C(a.continue_,a,!1),b instanceof we?(Ce(b,h,d),b.blocking_=!0):b.then(h,d));c&&(b=new Ge(b),Fe[b.id_]=b,a.unhandledErrorId_=b.id_)},Be=function(){E.call(this)};D(Be,E);Be.prototype.message="Deferred has already fired";Be.prototype.name="AlreadyCalledError";var xe=function(){E.call(this)};
D(xe,E);xe.prototype.message="Deferred was canceled";xe.prototype.name="CanceledError";var Ge=function(a){this.id_=z.setTimeout(C(this.throwError,this),0);this.error_=a};Ge.prototype.throwError=function(){delete Fe[this.id_];throw this.error_;};var Fe={};var Le=function(){var a=T(He),b={},c=b.document||document,d=md(a).toString(),e=(new Jd(c)).createElement("SCRIPT"),g={script_:e,timeout_:void 0},f=new we(g),h=null,k=b.timeout!=null?b.timeout:5E3;k>0&&(h=window.setTimeout(function(){Ie(e,!0);var l=new Je(1,"Timeout reached for loading script "+d);ye(f);ze(f,!1,l)},k),g.timeout_=h);e.onload=e.onreadystatechange=function(){e.readyState&&e.readyState!="loaded"&&e.readyState!="complete"||(Ie(e,b.cleanupWhenDone||!1,h),f.callback(null))};e.onerror=function(){Ie(e,
!0,h);var l=new Je(0,"Error while loading script "+d);ye(f);ze(f,!1,l)};g=b.attributes||{};fd(g,{type:"text/javascript",charset:"UTF-8"});Nd(e,g);Cd(e,a);Ke(c).appendChild(e);return f},Ke=function(a){var b;return(b=(a||document).getElementsByTagName("HEAD"))&&b.length!==0?b[0]:a.documentElement},ve=function(){if(this&&this.script_){var a=this.script_;a&&a.tagName=="SCRIPT"&&Ie(a,!0,this.timeout_)}},Ie=function(a,b,c){c!=null&&z.clearTimeout(c);a.onload=function(){};a.onerror=function(){};a.onreadystatechange=
function(){};b&&window.setTimeout(function(){Qd(a)},0)},Je=function(a,b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+b);E.call(this,c);this.code=a};D(Je,E);var He=v(["https://apis.google.com/js/client.js"]),Me=function(a){var b=a.serverUrl;var c=a.apiKey;a=a.authUser===void 0?0:a.authUser;this.serverUrl_=b;this.apiKey_=c;this.authUser_=a};Me.prototype.get=function(a,b){return Ne(this,"GET",a,null,b)};
var Ne=function(a,b,c,d,e){var g,f,h,k,l;return ya(function(n){if(n.nextAddress==1)return ra(n,Oe(),2);g=A("gapi.client");f=A("gapi.config");h=Pe();k=g.getToken();g.setToken(null);f.update("googleapis.config/auth/useFirstPartyAuth",!0);f.update("googleapis.config/auth/useFirstPartyAuthV2",!0);f.update("client/xd4",!1);f.update("client/cors",!1);f.update("client/apiKey",a.apiKey_);l=g.request({root:a.serverUrl_,path:c,method:b,body:d?Zc(d):void 0,headers:{"Content-Type":"application/json+protobuf",
"X-Goog-Api-Key":a.apiKey_,"X-Goog-AuthUser":a.authUser_}}).then(function(p){try{return $c(e,p.body)}catch(t){}});Qe(h);g.setToken(k);return n.return(l)})},Pe=function(){var a=A("gapi.config"),b={};b["googleapis.config/auth/useFirstPartyAuth"]=a.get("googleapis.config/auth/useFirstPartyAuth");b["googleapis.config/auth/useFirstPartyAuthV2"]=a.get("googleapis.config/auth/useFirstPartyAuthV2");b["client/xd4"]=a.get("client/xd4");b["client/cors"]=a.get("client/cors");b["client/apiKey"]=a.get("client/apiKey");
return b},Qe=function(a){for(var b=A("gapi.config"),c=w(Object.keys(a)),d=c.next();!d.done;d=c.next())d=d.value,b.update(d,a[d])},Oe=function(){return A("gapi.load")?Re():Le().then(function(){return Re()},function(a){return he("Failed initializing gapi.\nGapi error: "+a)})},Re=function(){var a=je(),b=A("gapi.client");if(b)a.resolve(b);else try{A("gapi.load")("client",{callback:function(){a.resolve(A("gapi.client"))}})}catch(c){a.reject("Failed loading gapi library: client")}return a.promise};var V=function(a){this.internalArray_=L(a)};y(V,R);V.prototype.getSeconds=function(){Rb(this,sb);return Uc(this)};V.prototype.setSeconds=function(a){a=a==null?a:jc(a);return M(this,1,a,"0")};var Se=function(){var a=(new V).setSeconds((new Date).getTimezoneOffset()*-60);return M(a,2,ic(0),0)};var Te=function(a){this.internalArray_=L(a)};y(Te,R);Te.prototype.getTimezoneOffset=function(){return N(this,V,3)};Te.prototype.setTimezoneOffset=function(a){return O(this,V,3,a)};var Ue=function(a){this.internalArray_=L(a)};y(Ue,R);Ue.prototype.setPlatform=function(a){return M(this,1,a==null?a:gc(a),0)};Ue.prototype.setSupportedCapabilityList=function(a){return Sc(this,3,a,gc)};Ue.prototype.setLibraryVersionInt=function(a){return M(this,4,a==null?a:ic(a),0)};var Ve=function(a){this.internalArray_=L(a)};y(Ve,R);Ve.prototype.setDeviceInfo=function(a){return O(this,Te,1,a)};Ve.prototype.setLibraryInfo=function(a){return O(this,Ue,2,a)};var We=function(a){this.internalArray_=L(a)};y(We,R);We.prototype.getAllowedCompletionStyleList=function(){return Oc(this,4,hc,Nc())};var Xe=function(a){this.internalArray_=L(a)};y(Xe,R);var Ye=function(a){this.internalArray_=L(a)};y(Ye,R);Ye.prototype.getPromptDelay=function(){return N(this,Xe,1)};Ye.prototype.getAllowedPromptStyleList=function(){return Oc(this,4,hc,Nc())};var Ze=function(a){this.internalArray_=L(a)};y(Ze,R);Ze.prototype.getLanguage=function(){return Vc(this,8)};Ze.prototype.getCompletion=function(){return N(this,We,2)};Ze.prototype.getDisplaySettings=function(){return N(this,Ye,3)};var $e=function(a){this.internalArray_=L(a)};y($e,R);$e.prototype.setIsScheduledSurvey=function(a){return Q(this,1,a)};var af=function(a){this.internalArray_=L(a)};y(af,R);var bf=function(a){this.internalArray_=L(a)};y(bf,R);m=bf.prototype;m.setTriggerId=function(a){return M(this,1,K(a),"")};m.setLanguageList=function(a){return Sc(this,2,a,oc)};m.getLanguage=function(){return Wc(this,2)};m.setTestingMode=function(a){return Q(this,3,a)};m.getSurveyId=function(){return Vc(this,4)};m.setSurveyId=function(a){Mc(this,4,K(a))};var cf=function(a){this.internalArray_=L(a)};y(cf,R);cf.prototype.setTriggerContext=function(a){return O(this,bf,1,a)};cf.prototype.setClientContext=function(a){return O(this,Ve,2,a)};cf.prototype.setScheduledSurveyContext=function(a){O(this,$e,3,a)};var df=function(a){this.internalArray_=L(a)};y(df,R);m=df.prototype;m.getSession=function(){return N(this,af,1)};m.getSurveyPayload=function(){return N(this,Ze,2)};m.getError=function(a){return Wc(this,4,a)};m.getSurveyId=function(){return Vc(this,5)};m.setSurveyId=function(a){M(this,5,K(a),"")};var ef=function(){this.actions_={}};ef.prototype.register=function(a,b,c){this.actions_[a]={callback:b,isApplicable:c||ad}};ef.prototype.execute=function(a,b){(a=this.actions_[a])&&a.isApplicable()&&a.callback.apply(null,b||[])};ef.prototype.isApplicable=function(a){a=this.actions_[a];return!!a&&a.isApplicable()};ef.prototype.register=ef.prototype.register;var ff=function(a){this.internalArray_=L(a)};y(ff,R);var gf=function(a){this.internalArray_=L(a)};y(gf,R);gf.prototype.setDirection=function(a){return M(this,8,K(a),"")};var hf=function(a){return function(b){return $c(a,b)}}(gf);function jf(a){if(!a)return null;a=pc(Jc(a,4));return a===null||a===void 0?null:pd(a)};var kf=v([""]),lf=v(["https://www.google.com/tools/feedback/help_panel_binary.js"]);
function mf(a,b,c,d){var e=a.helpCenterPath.startsWith("/")?a.helpCenterPath.substring(1):a.helpCenterPath,g=c.document,f=a.nonce,h=hf(b);h=N(h,ff,10)?jf(N(h,ff,10))||T(kf):T(lf);var k=Ld(g).createElement("SCRIPT");f&&k.setAttribute("nonce",f);k.onload=function(){c.startHelpCard({apiKey:"",context:a.helpCenterContext,directToGetHelp:!1,enableSendFeedback:!1,helpApiData:{helpApiConfig:a,productWindow:c},helpcenter:e,helpPanelStartTimeMs:a.helpPanelStartTimeMs,helpPanelTheme:a.helpPanelTheme,locale:a.locale,
nd4cSettingsIsEnabled:!1,onOpenHelpPanelCallback:d,serverData:b})};Id(k,h);g.body.appendChild(k)};var nf=v(["https://www.google.com/tools/feedback/"]),of=v(["http://localhost.corp.google.com/inapp/"]),pf=v(["http://localhost.proxy.googlers.com/inapp/"]),qf=v(["https://asx-frontend-autopush.corp.google.com/inapp/"]),rf=v(["https://asx-frontend-autopush.corp.google.com/tools/feedback/"]),sf=v(["https://asx-frontend-autopush.corp.google.co.uk/inapp/"]),tf=v(["https://asx-frontend-autopush.corp.google.co.uk/tools/feedback/"]),uf=v(["https://asx-frontend-autopush.corp.google.de/inapp/"]),vf=v(["https://asx-frontend-autopush.corp.google.de/tools/feedback/"]),
wf=v(["https://asx-frontend-autopush.corp.youtube.com/tools/feedback/"]),xf=v(["https://asx-frontend-autopush.corp.youtube.com/inapp/"]),yf=v(["https://asx-help-frontend-autopush.corp.youtube.com/tools/feedback/"]),zf=v(["https://asx-help-frontend-autopush.corp.youtube.com/inapp/"]),Af=v(["https://asx-frontend-staging.corp.google.com/inapp/"]),Bf=v(["https://asx-frontend-staging.corp.google.com/tools/feedback/"]),Cf=v(["https://support.google.com/inapp/"]),Df=v(["https://sandbox.google.com/inapp/"]),
Ef=v(["https://sandbox.google.com/tools/feedback/"]),Ff=v(["https://www.google.cn/tools/feedback/"]),Gf=v(["https://help.youtube.com/tools/feedback/"]),Hf=v(["https://asx-frontend-staging.corp.google.com/inapp/"]),If=v(["https://asx-frontend-staging.corp.google.com/tools/feedback/"]),Jf=v(["https://localhost.corp.google.com/inapp/"]),Kf=v(["https://localhost.proxy.googlers.com/inapp/"]),Lf=T(nf),Mf=[T(of),T(pf)],Nf=[T(qf),T(rf),T(sf),T(tf),T(uf),T(vf),T(wf),T(xf),T(yf),T(zf)],Of=[T(Af),T(Bf)],Pf=
[Lf,T(Cf),T(Df),T(Ef),T(Ff),T(Gf),T(Hf),T(If),T(Jf),T(Kf)];fa(Mf);fa(Nf);fa(Of);fa(Pf);var Qf=function(){this.disposed_=this.disposed_;this.onDisposeCallbacks_=this.onDisposeCallbacks_};Qf.prototype.disposed_=!1;Qf.prototype.isDisposed=function(){return this.disposed_};Qf.prototype.dispose=function(){this.disposed_||(this.disposed_=!0,this.disposeInternal())};Qf.prototype[Symbol.dispose]=function(){this.dispose()};Qf.prototype.disposeInternal=function(){if(this.onDisposeCallbacks_)for(;this.onDisposeCallbacks_.length;)this.onDisposeCallbacks_.shift()()};var Rf=function(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.propagationStopped_=!1};Rf.prototype.stopPropagation=function(){this.propagationStopped_=!0};Rf.prototype.preventDefault=function(){this.defaultPrevented=!0};var Sf=function(){if(!z.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};z.addEventListener("test",c,b);z.removeEventListener("test",c,b)}catch(d){}return a}();var Tf=function(a,b){Rf.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.event_=null;a&&this.init(a,b)};D(Tf,Rf);
Tf.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=cb||a.offsetX!==void 0?a.offsetX:a.layerX,this.offsetY=
cb||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.timeStamp=
a.timeStamp;this.event_=a;a.defaultPrevented&&Tf.superClass_.preventDefault.call(this)};Tf.prototype.stopPropagation=function(){Tf.superClass_.stopPropagation.call(this);this.event_.stopPropagation?this.event_.stopPropagation():this.event_.cancelBubble=!0};Tf.prototype.preventDefault=function(){Tf.superClass_.preventDefault.call(this);var a=this.event_;a.preventDefault?a.preventDefault():a.returnValue=!1};var Uf="closure_listenable_"+(Math.random()*1E6|0);var Zf=0;var $f=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.handler=e;this.key=++Zf;this.removed=this.callOnce=!1},ag=function(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.handler=null};var bg=function(a){this.src=a;this.listeners={};this.typeCount_=0};bg.prototype.add=function(a,b,c,d,e){var g=a.toString();a=this.listeners[g];a||(a=this.listeners[g]=[],this.typeCount_++);var f=cg(a,b,d,e);f>-1?(b=a[f],c||(b.callOnce=!1)):(b=new $f(b,this.src,g,!!d,e),b.callOnce=c,a.push(b));return b};
bg.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.listeners))return!1;var e=this.listeners[a];b=cg(e,b,c,d);return b>-1?(ag(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.listeners[a],this.typeCount_--),!0):!1};var dg=function(a,b){var c=b.type;c in a.listeners&&$a(a.listeners[c],b)&&(ag(b),a.listeners[c].length==0&&(delete a.listeners[c],a.typeCount_--))};
bg.prototype.getListener=function(a,b,c,d){a=this.listeners[a.toString()];var e=-1;a&&(e=cg(a,b,c,d));return e>-1?a[e]:null};bg.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return dd(this.listeners,function(g){for(var f=0;f<g.length;++f)if(!(c&&g[f].type!=d||e&&g[f].capture!=b))return!0;return!1})};var cg=function(a,b,c,d){for(var e=0;e<a.length;++e){var g=a[e];if(!g.removed&&g.listener==b&&g.capture==!!c&&g.handler==d)return e}return-1};var eg="closure_lm_"+(Math.random()*1E6|0),fg={},gg=0,ig=function(a,b,c,d,e){if(d&&d.once)hg(a,b,c,d,e);else if(Array.isArray(b))for(var g=0;g<b.length;g++)ig(a,b[g],c,d,e);else c=jg(c),a&&a[Uf]?a.listen(b,c,Ia(d)?!!d.capture:!!d,e):kg(a,b,c,!1,d,e)},kg=function(a,b,c,d,e,g){if(!b)throw Error("Invalid event type");var f=Ia(e)?!!e.capture:!!e,h=lg(a);h||(a[eg]=h=new bg(a));c=h.add(b,c,d,f,g);if(!c.proxy){d=mg();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Sf||(e=f),e===void 0&&(e=!1),a.addEventListener(b.toString(),
d,e);else if(a.attachEvent)a.attachEvent(ng(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");gg++}},mg=function(){var a=og,b=function(c){return a.call(b.src,b.listener,c)};return b},hg=function(a,b,c,d,e){if(Array.isArray(b))for(var g=0;g<b.length;g++)hg(a,b[g],c,d,e);else c=jg(c),a&&a[Uf]?a.eventTargetListeners_.add(String(b),c,!0,Ia(d)?!!d.capture:!!d,e):kg(a,b,c,!0,d,e)},pg=function(a,b,c,d,e){if(Array.isArray(b))for(var g=
0;g<b.length;g++)pg(a,b[g],c,d,e);else d=Ia(d)?!!d.capture:!!d,c=jg(c),a&&a[Uf]?a.eventTargetListeners_.remove(String(b),c,d,e):a&&(a=lg(a))&&(b=a.getListener(b,c,d,e))&&qg(b)},qg=function(a){if(typeof a!=="number"&&a&&!a.removed){var b=a.src;if(b&&b[Uf])dg(b.eventTargetListeners_,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(ng(c),d):b.addListener&&b.removeListener&&b.removeListener(d);gg--;(c=lg(b))?(dg(c,a),c.typeCount_==0&&
(c.src=null,b[eg]=null)):ag(a)}}},ng=function(a){return a in fg?fg[a]:fg[a]="on"+a},og=function(a,b){if(a.removed)a=!0;else{b=new Tf(b,this);var c=a.listener,d=a.handler||a.src;a.callOnce&&qg(a);a=c.call(d,b)}return a},lg=function(a){a=a[eg];return a instanceof bg?a:null},rg="__closure_events_fn_"+(Math.random()*1E9>>>0),jg=function(a){if(typeof a==="function")return a;a[rg]||(a[rg]=function(b){return a.handleEvent(b)});return a[rg]};var sg=function(){Qf.call(this);this.eventTargetListeners_=new bg(this);this.actualEventTarget_=this;this.parentEventTarget_=null};D(sg,Qf);sg.prototype[Uf]=!0;m=sg.prototype;m.addEventListener=function(a,b,c,d){ig(this,a,b,c,d)};m.removeEventListener=function(a,b,c,d){pg(this,a,b,c,d)};
m.dispatchEvent=function(a){var b=this.parentEventTarget_;if(b){var c=[];for(var d=1;b;b=b.parentEventTarget_)c.push(b),++d}b=this.actualEventTarget_;d=a.type||a;if(typeof a==="string")a=new Rf(a,b);else if(a instanceof Rf)a.target=a.target||b;else{var e=a;a=new Rf(d,b);fd(a,e)}e=!0;if(c)for(var g=c.length-1;!a.propagationStopped_&&g>=0;g--){var f=a.currentTarget=c[g];e=tg(f,d,!0,a)&&e}a.propagationStopped_||(f=a.currentTarget=b,e=tg(f,d,!0,a)&&e,a.propagationStopped_||(e=tg(f,d,!1,a)&&e));if(c)for(g=
0;!a.propagationStopped_&&g<c.length;g++)f=a.currentTarget=c[g],e=tg(f,d,!1,a)&&e;return e};m.disposeInternal=function(){sg.superClass_.disposeInternal.call(this);if(this.eventTargetListeners_){var a=this.eventTargetListeners_,b=0,c;for(c in a.listeners){for(var d=a.listeners[c],e=0;e<d.length;e++)++b,ag(d[e]);delete a.listeners[c];a.typeCount_--}}this.parentEventTarget_=null};m.listen=function(a,b,c,d){return this.eventTargetListeners_.add(String(a),b,!1,c,d)};
var tg=function(a,b,c,d){b=a.eventTargetListeners_.listeners[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,g=0;g<b.length;++g){var f=b[g];if(f&&!f.removed&&f.capture==c){var h=f.listener,k=f.handler||f.src;f.callOnce&&dg(a.eventTargetListeners_,f);e=h.call(k,d)!==!1&&e}}return e&&!d.defaultPrevented};sg.prototype.getListener=function(a,b,c,d){return this.eventTargetListeners_.getListener(String(a),b,c,d)};
sg.prototype.hasListener=function(a,b){return this.eventTargetListeners_.hasListener(a!==void 0?String(a):void 0,b)};var ug=function(a){try{return z.JSON.parse(a)}catch(b){}a=String(a);if(/^\s*$/.test(a)?0:/^[\],:{}\s\u2028\u2029]*$/.test(a.replace(/\\["\\\/bfnrtu]/g,"@").replace(/(?:"[^"\\\n\r\u2028\u2029\x00-\x08\x0a-\x1f]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)[\s\u2028\u2029]*(?=:|,|]|}|$)/g,"]").replace(/(?:^|:|,)(?:[\s\u2028\u2029]*\[)+/g,"")))try{return eval("("+a+")")}catch(b){}throw Error("Invalid JSON string: "+a);};var vg=function(){};vg.prototype.cachedOptions_=null;var wg=function(a){var b;(b=a.cachedOptions_)||(b=a.cachedOptions_={});return b};var xg,yg=function(){};D(yg,vg);xg=new yg;var zg=function(a,b,c){if(typeof a==="function")c&&(a=C(a,c));else if(a&&typeof a.handleEvent=="function")a=C(a.handleEvent,a);else throw Error("Invalid listener argument");return Number(b)>2147483647?-1:z.setTimeout(a,b||0)};var Ag=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Bg=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var g=a[c].substring(0,d);e=a[c].substring(d+1)}else g=a[c];b(g,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};var W=function(a){sg.call(this);this.headers=new Map;this.xmlHttpFactory_=a||null;this.active_=!1;this.xhrOptions_=this.xhr_=null;this.lastUri_="";this.inAbort_=this.inOpen_=this.inSend_=this.errorDispatched_=!1;this.timeoutInterval_=0;this.timeoutId_=null;this.responseType_="";this.progressEventsEnabled_=this.withCredentials_=!1;this.attributionReportingOptions_=this.trustToken_=null};D(W,sg);
var Cg=/^https?$/i,Dg=["POST","PUT"],Eg=[],Fg=function(a,b,c,d,e,g){var f=new W;Eg.push(f);b&&f.listen("complete",b);f.eventTargetListeners_.add("ready",f.cleanupSend_,!0,void 0,void 0);g&&(f.timeoutInterval_=Math.max(0,g));f.withCredentials_=!0;f.send(a,c,d,e)};m=W.prototype;m.cleanupSend_=function(){this.dispose();$a(Eg,this)};m.setTrustToken=function(a){this.trustToken_=a};m.setAttributionReporting=function(a){this.attributionReportingOptions_=a};
m.send=function(a,b,c,d){if(this.xhr_)throw Error("[goog.net.XhrIo] Object is active with another request="+this.lastUri_+"; newUri="+a);b=b?b.toUpperCase():"GET";this.lastUri_=a;this.errorDispatched_=!1;this.active_=!0;this.xhr_=new XMLHttpRequest;this.xhrOptions_=this.xmlHttpFactory_?wg(this.xmlHttpFactory_):wg(xg);this.xhr_.onreadystatechange=C(this.onReadyStateChange_,this);this.progressEventsEnabled_&&"onprogress"in this.xhr_&&(this.xhr_.onprogress=C(function(f){this.onProgressHandler_(f,!0)},
this),this.xhr_.upload&&(this.xhr_.upload.onprogress=C(this.onProgressHandler_,this)));try{this.inOpen_=!0,this.xhr_.open(b,String(a),!0),this.inOpen_=!1}catch(f){this.error_(5,f);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get==="function"){e=w(d.keys());for(var g=e.next();!g.done;g=e.next())g=g.value,c.set(g,d.get(g))}else throw Error("Unknown input type for opt_headers: "+String(d));
d=Array.from(c.keys()).find(function(f){return"content-type"==f.toLowerCase()});e=z.FormData&&a instanceof z.FormData;!(Xa(Dg,b)>=0)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=w(c);for(d=b.next();!d.done;d=b.next())c=w(d.value),d=c.next().value,c=c.next().value,this.xhr_.setRequestHeader(d,c);this.responseType_&&(this.xhr_.responseType=this.responseType_);"withCredentials"in this.xhr_&&this.xhr_.withCredentials!==this.withCredentials_&&(this.xhr_.withCredentials=
this.withCredentials_);if("setTrustToken"in this.xhr_&&this.trustToken_)try{this.xhr_.setTrustToken(this.trustToken_)}catch(f){}if("setAttributionReporting"in this.xhr_&&this.attributionReportingOptions_)try{this.xhr_.setAttributionReporting(this.attributionReportingOptions_)}catch(f){}try{Gg(this),this.timeoutInterval_>0&&(this.timeoutId_=zg(this.timeout_,this.timeoutInterval_,this)),this.inSend_=!0,this.xhr_.send(a),this.inSend_=!1}catch(f){this.error_(5,f)}};
m.timeout_=function(){typeof Ca!="undefined"&&this.xhr_&&(this.dispatchEvent("timeout"),this.abort(8))};m.error_=function(){this.active_=!1;this.xhr_&&(this.inAbort_=!0,this.xhr_.abort(),this.inAbort_=!1);Hg(this);Ig(this)};var Hg=function(a){a.errorDispatched_||(a.errorDispatched_=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};
W.prototype.abort=function(){this.xhr_&&this.active_&&(this.active_=!1,this.inAbort_=!0,this.xhr_.abort(),this.inAbort_=!1,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Ig(this))};W.prototype.disposeInternal=function(){this.xhr_&&(this.active_&&(this.active_=!1,this.inAbort_=!0,this.xhr_.abort(),this.inAbort_=!1),Ig(this,!0));W.superClass_.disposeInternal.call(this)};
W.prototype.onReadyStateChange_=function(){if(!this.isDisposed())if(this.inOpen_||this.inSend_||this.inAbort_)Jg(this);else this.onReadyStateChangeEntryPoint_()};W.prototype.onReadyStateChangeEntryPoint_=function(){Jg(this)};
var Jg=function(a){if(a.active_&&typeof Ca!="undefined"&&(!a.xhrOptions_[1]||Kg(a)!=4||Lg(a)!=2))if(a.inSend_&&Kg(a)==4)zg(a.onReadyStateChange_,0,a);else if(a.dispatchEvent("readystatechange"),Kg(a)==4){a.active_=!1;try{var b=Lg(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}var d;if(!(d=c)){var e;if(e=b===0){var g=String(a.lastUri_).match(Ag)[1]||null;!g&&z.self&&z.self.location&&(g=z.self.location.protocol.slice(0,-1));e=!Cg.test(g?
g.toLowerCase():"")}d=e}d?(a.dispatchEvent("complete"),a.dispatchEvent("success")):Hg(a)}finally{Ig(a)}}};W.prototype.onProgressHandler_=function(a,b){this.dispatchEvent(Mg(a,"progress"));this.dispatchEvent(Mg(a,b?"downloadprogress":"uploadprogress"))};
var Mg=function(a,b){return{type:b,lengthComputable:a.lengthComputable,loaded:a.loaded,total:a.total}},Ig=function(a,b){if(a.xhr_){Gg(a);var c=a.xhr_,d=a.xhrOptions_[0]?function(){}:null;a.xhr_=null;a.xhrOptions_=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=d}catch(e){}}},Gg=function(a){a.timeoutId_&&(z.clearTimeout(a.timeoutId_),a.timeoutId_=null)};W.prototype.isActive=function(){return!!this.xhr_};
var Kg=function(a){return a.xhr_?a.xhr_.readyState:0},Lg=function(a){try{return Kg(a)>2?a.xhr_.status:-1}catch(b){return-1}},Ng=function(a){if(a.xhr_){a=a.xhr_.responseText;a.indexOf(")]}'\n")==0&&(a=a.substring(5));a:{if(z.JSON)try{var b=z.JSON.parse(a);break a}catch(c){}b=ug(a)}return b}};W.prototype.getResponseHeader=function(a){if(this.xhr_&&Kg(this)==4)return a=this.xhr_.getResponseHeader(a),a===null?void 0:a};
W.prototype.getAllResponseHeaders=function(){return this.xhr_&&Kg(this)>=2?this.xhr_.getAllResponseHeaders()||"":""};var Og=v([""]),Pg=v(["https://www.google.com/tools/feedback/help_panel_binary.js"]);function Qg(a,b,c,d,e,g){return ya(function(f){return f.return(new Promise(function(h){Fg(""+a+"/repeater_help_panel?locale="+b+"&helpContext="+c+"&productId="+d+"&helpcenter="+e+"&openingMode="+g,function(k){k=k.target;var l=null;try{l=$c(gf,JSON.stringify(Ng(k)))}catch(n){}h(l)})}))})}
function Rg(a,b,c,d){var e=a.helpCenterPath.startsWith("/")?a.helpCenterPath.substring(1):a.helpCenterPath,g=hf(b),f=a.helpPanelMode||0,h=a.fixedHelpPanelContainer,k=a.customHelpPanelContainer;h&&f!==1?h=void 0:f!==1||h&&!P(g,5)||(f=0,h=void 0);k&&f!==2?f=2:f!==2||k&&!a.anchor||(f=0,k=void 0);var l=a.minimizeMode;f!==2||l&&l!==0?f===1&&(l=2):l=2;var n=a.openingMode;if(a.directToGetHelp)n=2;else if(a.supportContentUrl||a.defaultHelpArticleId)n=3;var p=c.document,t=a.nonce,q=N(g,ff,10)?jf(N(g,ff,10))||
T(Og):T(Pg),u=A("document.location.href",c);a.helpCenterContext||a.context||!u||(a.context=u.substring(0,1200));u=!0;if(d){var B=JSON.stringify(d);(u=B.length<=1200)&&(a.psdJson=B)}u||(d={invalidPsd:!0});u=Ld(p).createElement("SCRIPT");t&&u.setAttribute("nonce",t);u.onload=function(){c.startHelpPanel({helpcenter:e,apiKey:"testpage",channel:a.channel,context:a.context||a.helpCenterContext||c.location.href,defaultHelpArticleFragment:a.defaultHelpArticleFragment,defaultHelpArticleId:a.defaultHelpArticleId,
defaultHelpArticleHelpcenterPath:a.defaultHelpArticleHelpcenterPath,directToGetHelp:a.directToGetHelp||!1,openToHelpGuideEntryButtonId:a.openToHelpGuideEntryButtonId,enableHelpGuideMaximize:a.enableHelpGuideMaximize,enableHelpGuideConversationalAi:a.enableHelpGuideConversationalAi,enableHelpGuideHumanChat:a.enableHelpGuideHumanChat,internalHelpCenter:Vc(g,12),enableSendFeedback:a.enableSendFeedback||!1,helpPanelTheme:a.helpPanelTheme,locale:a.locale,nd4cSettingsIsEnabled:a.nd4cSettingsIsEnabled||
!1,nd4cSettingsCountryCode:a.nd4cSettingsCountryCode||"",serverData:b,supportContentUrl:a.supportContentUrl,symptom:a.symptom,helpApiData:{helpApiConfig:a,frdProductData:a.frdProductData,productData:d,productWindow:c},helpPanelMode:f,onPromotedProductLinkClickCallback:a.onPromotedProductLinkClickCallback,fixedHelpPanelContainer:h,customHelpPanelContainer:k,openingMode:n,onMinimizeCallback:a.onMinimizeCallback,onGseEventCallback:a.onGseEventCallback,minimizeMode:l||0,helpFlowSessionId:a.helpFlowSessionId||
a.supportVisitId,helpGuideHelpCenterEmbedEntryPoint:a.helpGuideHelpCenterEmbedEntryPoint,helpGuideCommonEmbedEntryPoint:a.helpGuideCommonEmbedEntryPoint,helpGuideStartingFlow:a.helpGuideStartingFlow,gseSessionOptions:a.gseSessionOptions,helpPanelStartTimeMs:a.helpPanelStartTimeMs,disableEndUserCredentials:a.disableEndUserCredentials,gsePageUrl:a.gsePageUrl,mendelIds:a.mendelIds,productDeepLinkRegex:a.productDeepLinkRegex,onProductDeepLinkClickCallback:a.onProductDeepLinkClickCallback,supportJourneyId:a.supportJourneyId})};
Id(u,q);p.body.appendChild(u)};for(var Sg={en:["en-us"],ar:["ar-eg"],zh:["zh-cn","zh-hans","zh-hans-cn"],"zh-tw":["zh-hant","zh-hant-tw"],nl:["nl-nl"],"en-gb":[],fr:["fr-fr"],de:["de-de"],it:["it-it"],ja:["ja-jp"],ko:["ko-kr"],pl:["pl-pl"],pt:["pt-br"],ru:["ru-ru"],es:["es-es"],th:["th-th"],tr:["tr-tr"],"es-419":[],bg:["bg-bg"],ca:["ca-es"],hr:["hr-hr"],cs:["cs-cz"],da:["da-dk"],fil:["fil-ph","tl","tl-ph"],fi:["fi-fi"],el:["el-gr"],iw:["he","he-il","iw-il"],hi:["hi-in"],hu:["hu-hu"],id:["id-id","in","in-id"],lv:["lv-lv"],lt:["lt-lt"],
no:["no-no","nb","nb-no"],"pt-pt":[],ro:["ro-ro","mo"],sr:["sr-rs","sr-cyrl-rs"],sk:["sk-sk"],sl:["sl-sl"],sv:["sv-se"],uk:["uk-ua"],vi:["vi-vn"],fa:["fa-ir"],af:["af-za"],bn:["bn-in"],et:["et-ee"],is:["is-is"],ms:["ms-my"],mr:["mr-in"],sw:["sw-tz"],ta:["ta-in"],sq:["sq-al"],hy:["hy-am"],az:["az-az"],my:["my-mm"],ka:["ka-ge"],kk:["kk-kz"],km:["km-kh"],lo:["lo-la"],mk:["mk-mk"],mn:["mn-mn"],ne:["ne-np"],si:["si-lk"],am:["am-et"],gu:["gu-in"],kn:["kn-in"],ml:["ml-in"],te:["te-in"],ur:["ur-pk"],ky:["ky-kg"],
pa:["pa-in"],uz:["uz-uz"],"sr-latn":["sh"],"fr-ca":["fr-ca"]},Tg={},Ug=w(Object.keys(Sg)),Vg=Ug.next();!Vg.done;Vg=Ug.next()){var Wg=Vg.value;Tg[Wg]=Wg;for(var Xg=w(Sg[Wg]),Yg=Xg.next();!Yg.done;Yg=Xg.next())Tg[Yg.value]=Wg};var Zg=function(a){this.internalArray_=L(a)};y(Zg,R);Zg.prototype.getTimezoneOffset=function(){return N(this,V,3)};Zg.prototype.setTimezoneOffset=function(a){return O(this,V,3,a)};var $g=function(a){this.internalArray_=L(a)};y($g,R);$g.prototype.setPlatform=function(a){return M(this,1,a==null?a:gc(a),0)};$g.prototype.setSupportedCapabilityList=function(a){return Sc(this,3,a,gc)};$g.prototype.setLibraryVersionInt=function(a){return M(this,4,a==null?a:ic(a),0)};var ah=function(a){this.internalArray_=L(a)};y(ah,R);ah.prototype.setDeviceInfo=function(a){return O(this,Zg,1,a)};ah.prototype.setLibraryInfo=function(a){return O(this,$g,2,a)};var bh=function(a){this.internalArray_=L(a)};y(bh,R);bh.prototype.getAllowedCompletionStyleList=function(){return Oc(this,4,hc,Nc())};var ch=function(a){this.internalArray_=L(a)};y(ch,R);var dh=function(a){this.internalArray_=L(a)};y(dh,R);dh.prototype.getPromptDelay=function(){return N(this,ch,1)};dh.prototype.getAllowedPromptStyleList=function(){return Oc(this,4,hc,Nc())};var eh=function(a){this.internalArray_=L(a)};y(eh,R);eh.prototype.getLanguage=function(){return Vc(this,8)};eh.prototype.getCompletion=function(){return N(this,bh,2)};eh.prototype.getDisplaySettings=function(){return N(this,dh,3)};var fh=function(a){this.internalArray_=L(a)};y(fh,R);fh.prototype.setIsScheduledSurvey=function(a){return Q(this,1,a)};var gh=function(a){this.internalArray_=L(a)};y(gh,R);var hh=function(a){this.internalArray_=L(a)};y(hh,R);var ih=function(a){var b=new hh;return Q(b,1,a)};var jh=function(a){this.internalArray_=L(a)};y(jh,R);m=jh.prototype;m.setTriggerId=function(a){return M(this,1,K(a),"")};m.setLanguageList=function(a){return Sc(this,2,a,oc)};m.getLanguage=function(){return Wc(this,2)};m.setTestingMode=function(a){return Q(this,3,a)};m.getSurveyId=function(){return Vc(this,4)};m.setSurveyId=function(a){Mc(this,4,K(a))};var kh=function(a){this.internalArray_=L(a)};y(kh,R);var lh=function(a,b){return M(a,1,K(b),"")};kh.prototype.setApiKey=function(a){return M(this,2,K(a),"")};kh.prototype.setPlatform=function(a){return M(this,3,a==null?a:gc(a),0)};var mh=function(a){this.internalArray_=L(a)};y(mh,R);var nh=function(a,b){return O(a,hh,1,b)};var oh=function(a){this.internalArray_=L(a)};y(oh,R);oh.prototype.setTriggerContext=function(a){return O(this,jh,1,a)};oh.prototype.setClientContext=function(a){return O(this,ah,2,a)};oh.prototype.setScheduledSurveyContext=function(a){O(this,fh,3,a)};var ph=function(a){this.internalArray_=L(a)};y(ph,R);m=ph.prototype;m.getSession=function(){return N(this,gh,1)};m.getSurveyPayload=function(){return N(this,eh,2)};m.getError=function(a){return Wc(this,4,a)};m.getSurveyId=function(){return Vc(this,5)};m.setSurveyId=function(a){M(this,5,K(a),"")};var qh=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("URI is missing protocol: "+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&
c!=="moz-extension"&&c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("Invalid URI scheme in origin: "+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};function rh(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=l=0}function b(p){for(var t=f,q=0;q<64;q+=4)t[q/4]=p[q]<<24|p[q+1]<<16|p[q+2]<<8|p[q+3];for(q=16;q<80;q++)p=t[q-3]^t[q-8]^t[q-14]^t[q-16],t[q]=(p<<1|p>>>31)&4294967295;p=e[0];var u=e[1],B=e[2],X=e[3],nb=e[4];for(q=0;q<80;q++){if(q<40)if(q<20){var na=X^u&(B^X);var Ga=1518500249}else na=u^B^X,Ga=1859775393;else q<60?(na=u&B|X&(u|B),Ga=2400959708):(na=u^B^X,Ga=3395469782);na=((p<<5|p>>>27)&4294967295)+
na+nb+Ga+t[q]&4294967295;nb=X;X=B;B=(u<<30|u>>>2)&4294967295;u=p;p=na}e[0]=e[0]+p&4294967295;e[1]=e[1]+u&4294967295;e[2]=e[2]+B&4294967295;e[3]=e[3]+X&4294967295;e[4]=e[4]+nb&4294967295}function c(p,t){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var q=[],u=0,B=p.length;u<B;++u)q.push(p.charCodeAt(u));p=q}t||(t=p.length);q=0;if(l==0)for(;q+64<t;)b(p.slice(q,q+64)),q+=64,n+=64;for(;q<t;)if(g[l++]=p[q++],n++,l==64)for(l=0,b(g);q+64<t;)b(p.slice(q,q+64)),q+=64,n+=64}function d(){var p=
[],t=n*8;l<56?c(h,56-l):c(h,64-(l-56));for(var q=63;q>=56;q--)g[q]=t&255,t>>>=8;b(g);for(q=t=0;q<5;q++)for(var u=24;u>=0;u-=8)p[t++]=e[q]>>u&255;return p}for(var e=[],g=[],f=[],h=[128],k=1;k<64;++k)h[k]=0;var l,n;a();return{reset:a,update:c,digest:d,digestString:function(){for(var p=d(),t="",q=0;q<p.length;q++)t+="0123456789ABCDEF".charAt(Math.floor(p[q]/16))+"0123456789ABCDEF".charAt(p[q]%16);return t}}};var th=function(a,b,c){var d=String(z.location.href);return d&&a&&b?[b,sh(qh(d),a,c||null)].join(" "):null},sh=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],Ya(d,function(h){e.push(h)}),uh(e.join(" "));var g=[],f=[];Ya(c,function(h){f.push(h.key);g.push(h.value)});c=Math.floor((new Date).getTime()/1E3);e=g.length==0?[c,b,a]:[g.join(":"),c,b,a];Ya(d,function(h){e.push(h)});a=uh(e.join(" "));a=[c,a];f.length==0||a.push(f.join(""));return a.join("_")},uh=function(a){var b=
rh();b.update(a);return b.digestString().toLowerCase()};var vh={};var wh=function(){this.document_=document||{cookie:""}};m=wh.prototype;m.isEnabled=function(){if(!z.navigator.cookieEnabled)return!1;if(!this.isEmpty())return!0;this.set("TESTCOOKIESENABLED","1",{maxAge:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
m.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.sameSite;d=c.secure||!1;var g=c.domain||void 0;var f=c.path||void 0;var h=c.maxAge}if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');h===void 0&&(h=-1);this.document_.cookie=a+"="+b+(g?";domain="+g:"")+(f?";path="+f:"")+(h<0?"":h==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+h*1E3)).toUTCString())+(d?";secure":"")+(e!=null?
";samesite="+e:"")};m.get=function(a,b){for(var c=a+"=",d=(this.document_.cookie||"").split(";"),e=0,g;e<d.length;e++){g=Oa(d[e]);if(g.lastIndexOf(c,0)==0)return g.slice(c.length);if(g==a)return""}return b};m.remove=function(a,b,c){var d=this.containsKey(a);this.set(a,"",{maxAge:0,path:b,domain:c});return d};m.getKeys=function(){return xh(this).keys};m.getValues=function(){return xh(this).values};m.isEmpty=function(){return!this.document_.cookie};m.containsKey=function(a){return this.get(a)!==void 0};
m.clear=function(){for(var a=xh(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};var xh=function(a){a=(a.document_.cookie||"").split(";");for(var b=[],c=[],d,e,g=0;g<a.length;g++)e=Oa(a[g]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};var yh=function(a){return!!vh.FPA_SAMESITE_PHASE2_MOD||!(a===void 0||!a)},zh=function(a,b,c,d){(a=z[a])||typeof document==="undefined"||(a=(new wh).get(b));return a?th(a,c,d):null};var Ah=function(a){this.internalArray_=L(a)};y(Ah,R);m=Ah.prototype;m.getEnableSsEngine=function(){return P(this,2)};m.getEnableAwr=function(){return P(this,3)};m.getAlohaAutoGaRollout=function(){return P(this,5)};m.getEnableConfigurator=function(){return P(this,6)};m.getEnableMweb=function(){return P(this,7)};m.getEnableCtlConsentCheckbox=function(){return P(this,8)};m.getEnableIframe=function(){return P(this,9)};m.getEnableScreenshotNudge=function(){return P(this,10)};
m.getEnableWebStartupConfigEndpoint=function(){return P(this,11)};m.getEnableJunkNudge=function(){return P(this,12)};m.getEnableConfiguratorLocale=function(){return P(this,13)};m.getEnableTinyNoPointer=function(){return P(this,14)};m.getEnableSupportSessionLogging=function(){return P(this,15)};m.getEnableFileUploadForScreenshot=function(){return P(this,16)};m.getEnableDirectDeflectionForSingleCategory=function(){return P(this,17)};m.getEnableImageSanitization=function(){return P(this,18)};
m.getEnableAlohaBinarySplit=function(){return P(this,19)};m.getEnableDbFeedbackIntents=function(){return P(this,20)};m.getEnableMarkMandatoryFieldsWithRequired=function(){return P(this,21)};m.getEnableFeedbackCategoryCustomUi=function(){return P(this,22)};m.getEnableRealtimeCtl=function(){return P(this,23)};var Bh=function(a){this.internalArray_=L(a)};y(Bh,R);function Ch(a){return Dh.some(function(b){return b.test(a)})}var Dh=[/https:\/\/sandbox\.google\.com\/tools\/feedback/,/https:\/\/feedback-frontend-qual[a-z0-9.]*\.google\.com\/inapp/,/https:\/\/feedback-frontend-qual[a-z0-9.]*\.google\.com\/tools\/feedback/,/https:\/\/.*\.googleusercontent\.com\/inapp/];var Eh="af am ar-EG ar-JO ar-MA ar-SA ar-XB ar az be bg bn bs ca cs cy da de-AT de-CH de el en en-GB en-AU en-CA en-IE en-IN en-NZ en-SG en-XA en-XC en-ZA es es-419 es-AR es-BO es-CL es-CO es-CR es-DO es-EC es-GT es-HN es-MX es-NI es-PA es-PE es-PR es-PY es-SV es-US es-UY es-VE et eu fa fi fil fr-CA fr-CH fr gl gsw gu he hi hr hu hy id in is it iw ja ka kk km kn ko ky ln lo lt lv mk ml mn mo mr ms my nb ne nl no pa pl pt pt-BR pt-PT ro ru si sk sl sq sr-Latn sr sv sw ta te th tl tr uk ur uz vi zh zh-CN zh-HK zh-TW zu".split(" ");var Fh=v(["https://www.gstatic.com/uservoice/feedback/client/web/","/main_light_binary.js"]),Gh=v(["https://www.gstatic.com/uservoice/feedback/client/web/","/main_binary__",".js"]);
function Hh(a,b){var c;var d=(c=a.formContent)==null?void 0:c.locale;c=d==null?void 0:d.split("-")[0];d=d&&Eh.includes(d)?d:c&&Eh.includes(c)?c:void 0;d=(d!=null?d:"en").replaceAll("-","_").toLowerCase();var e;a=((e=a.initializationData)==null?0:e.useNightlyRelease)?"nightly":"live";var g;return(b==null?0:(g=b.getEnableAlohaBinarySplit)==null?0:g.call(b))?T(Fh,a):T(Gh,a,d)};var Ih,Jh;function Kh(a,b,c,d){if(Ih)return Ih;var e=Hh(a,d);return Ih=b.feedbackV2GlobalObject?Promise.resolve(b.feedbackV2GlobalObject):new Promise(function(g,f){var h=Pd(document,"SCRIPT");Id(h,e);h.onload=function(){b.feedbackV2GlobalObject?g(b.feedbackV2GlobalObject):f("feedbackV2GlobalObject not found on window.")};h.onerror=function(){f("Feedback binary script tag failed to load: "+e.toString())};c.body.appendChild(h)})}
function Lh(a,b,c,d){if(Jh)return Jh;var e=Hh(a,d);return Jh=b.feedbackV2GlobalObject?Promise.resolve(b.feedbackV2GlobalObject):new Promise(function(g,f){var h=Pd(document,"SCRIPT");Id(h,e);h.onload=function(){b.feedbackV2GlobalObject?g(b.feedbackV2GlobalObject):f("feedbackV2GlobalObject not found on window.")};h.onerror=function(){f("Feedback binary script tag failed to load: "+e.toString())};c.body.appendChild(h)})}
function Mh(a,b,c,d,e){e=e===void 0?!0:e;var g,f,h,k,l;return ya(function(n){switch(n.nextAddress){case 1:return g=Date.now(),ra(n,Kh(a,c,d,b),2);case 2:f=n.yieldResult;if(!(e||((k=a.initializationData)==null?0:k.useNightlyRelease)||((l=a.initializationData)==null?0:l.isLocalServer))){h=f.initializeFeedbackClient(a,g,b);n.nextAddress=3;break}return ra(n,f.initializeFeedbackClientAsync(a,g,b),4);case 4:h=n.yieldResult;case 3:return h.initiateAloha(),n.return(h)}})}
function Nh(a,b,c,d){var e,g,f;return ya(function(h){if(h.nextAddress==1)return e=Date.now(),ra(h,Lh(a,c,d.document,b),2);if(h.nextAddress!=3)return g=h.yieldResult,ra(h,g.initializeFeedbackClientAsync(a,e,b,d),3);f=h.yieldResult;f.initiateAloha();return h.return(f)})}
function Oh(a,b,c){var d=!0;d=d===void 0?!0:d;var e,g,f,h,k,l,n,p,t,q;return ya(function(u){e=c||z;if((g=b)==null?0:(h=(f=g).getEnableAlohaBinarySplit)==null?0:h.call(f)){k=e;if(k.isFormOpened)throw l=Error("Form is either loading or already opened"),l.name="DuplicateFormError",l;k.isFormOpened=!0;a.callbacks=a.callbacks||{};n=a.callbacks.onClose||function(){};a.callbacks.onClose=function(B){k.isFormOpened=!1;n(B)};try{return u.return(Nh(a,b,k,e))}catch(B){throw k.isFormOpened=!1,B;}}else{p=e;if(p.isFormOpened)throw t=
Error("Form is either loading or already opened"),t.name="DuplicateFormError",t;p.isFormOpened=!0;a.callbacks=a.callbacks||{};q=a.callbacks.onClose||function(){};a.callbacks.onClose=function(B){p.isFormOpened=!1;q(B)};try{return u.return(Mh(a,b,p,e.document,d))}catch(B){throw p.isFormOpened=!1,B;}}u.nextAddress=0})};function Ph(a,b){return ya(function(c){return c.return(new Promise(function(d){var e=Qh(b!=null?b:"")+"/aloha_form_properties?productId="+a;Fg(e,function(g){g=g.target;var f=null;try{f=$c(Bh,JSON.stringify(Ng(g)))}catch(h){g=new Bh,f=new Ah,f=Q(f,5,!0),f=Q(f,2,!0),f=Q(f,4,!1),f=Q(f,8,!0),f=Q(f,9,!0),f=Q(f,7,!0),f=Q(f,10,!0),f=Q(f,12,!0),f=Q(f,13,!1),f=Q(f,14,!0),f=Q(f,15,!0),f=Q(f,20,!1),f=O(g,Ah,1,f)}d(f)},"GET","",{},2E3)}))})}
function Qh(a){return Ch(a)?a:"https://www.google.com/tools/feedback"};var Rh=function(a,b,c){a.timeOfStartCall=(new Date).getTime();var d=c||z,e=d.document,g=a.nonce||Fd(d);g&&!a.nonce&&(a.nonce=g);if(a.flow=="help"){var f=A("document.location.href",d);!a.helpCenterContext&&f&&(a.helpCenterContext=f.substring(0,1200));f=!0;if(b&&JSON&&JSON.stringify){var h=JSON.stringify(b);(f=h.length<=1200)&&(a.psdJson=h)}f||(b={invalidPsd:!0})}b=[a,b,c];d.GOOGLE_FEEDBACK_START_ARGUMENTS=b;c=a.feedbackServerUri||"//www.google.com/tools/feedback";if(f=d.GOOGLE_FEEDBACK_START)f.apply(d,
b);else{d=c+"/load.js?";for(var k in a)b=a[k],b==null||Ia(b)||(d+=encodeURIComponent(k)+"="+encodeURIComponent(b)+"&");a=Ld(e).createElement("SCRIPT");g&&a.setAttribute("nonce",g);Id(a,pd(d));e.body.appendChild(a)}},Sh=function(a,b,c,d){var e,g;ya(function(f){e=c||z;var h=a.serverEnvironment==="DEV",k=c||z;k=a.nonce||Fd(k);h={integrationKeys:{productId:a.productId,feedbackBucket:a.bucket,triggerId:a.triggerId},callbacks:{onClose:a.callback,onLoad:a.onLoadCallback},formContent:{locale:a.locale,disableScreenshot:a.disableScreenshotting,
productDisplayName:void 0,announcement:void 0,issueCategories:void 0,includeSeveritySelection:void 0,customImageSrc:void 0,thankYouMessage:void 0,userEmail:void 0,defaultFormInputValues:void 0,defaultFormInputValuesString:void 0,abuseLink:a.abuseLink,additionalDataConsent:a.additionalDataConsent},initializationData:{isLocalServer:h,nonce:k,useNightlyRelease:h,feedbackJsUrl:void 0,feedbackCssUrl:void 0,feedbackJsUrlSerialized:void 0,feedbackCssUrlSerialized:void 0,submissionServerUri:a.feedbackServerUri,
colorScheme:a.colorScheme},extraData:{productVersion:a.productVersion,authUser:a.authuser,configuratorId:a.configuratorId,customZIndex:a.customZIndex,tinyNoPointer:a.tinyNoPointer,allowNonLoggedInFeedback:a.allowNonLoggedInFeedback,enableAnonymousFeedback:a.enableAnonymousFeedback}};b&&(k=new Map(Object.entries(b)),h.extraData.productSpecificData=k);g=h;return ra(f,Oh(g,d,e),0)})},Th=function(a,b,c){try{if(a.flow==="help"){var d=a.helpCenterPath.replace(/^\//,"");Ed(c||window,yd("https://support.google.com/"+
d))}else a.flow==="submit"?Rh(a,b,c):Ph(a.productId,a.feedbackServerUri).then(function(e){e=N(e,Ah,1);var g=!db||(e==null?void 0:e.getEnableMweb()),f=!a.tinyNoPointer||(e==null?void 0:e.getEnableTinyNoPointer());!e||e.getAlohaAutoGaRollout()&&g&&f?Sh(a,b,c,e):Rh(a,b,c)},function(e){e&&e.name!=="DuplicateFormError"&&Rh(a,b,c)})}catch(e){Sh(a,b,c,null)}};Da("userfeedback.api.startFeedback",Th);var Uh=function(){};Uh.prototype.next=function(){return Vh};var Vh={done:!0,value:void 0};Uh.prototype.__iterator__=function(){return this};var Wh=function(a){if(a instanceof Uh)return a;if(typeof a.__iterator__=="function")return a.__iterator__(!1);if(Ha(a)){var b=0,c=new Uh;c.next=function(){for(;;){if(b>=a.length)return Vh;if(b in a)return{value:a[b++],done:!1};b++}};return c}throw Error("Not implemented");};var Yh=function(a){this.elements_={};if(a)for(var b=0;b<a.length;b++)this.elements_[Xh(a[b])]=null;for(var c in Object.prototype);},Zh={},Xh=function(a){return a in Zh||String(a).charCodeAt(0)==32?" "+a:a},$h=function(a){return a.charCodeAt(0)==32?a.slice(1):a};m=Yh.prototype;m.add=function(a){this.elements_[Xh(a)]=null};m.clear=function(){this.elements_={}};m.clone=function(){var a=new Yh,b;for(b in this.elements_)a.elements_[b]=null;return a};m.contains=function(a){return Xh(a)in this.elements_};
m.has=function(a){return this.contains(a)};m.forEach=function(a,b){for(var c in this.elements_)a.call(b,$h(c),void 0,this)};m.values=Object.keys?function(){return Object.keys(this.elements_).map($h,this)}:function(){var a=[],b;for(b in this.elements_)a.push($h(b));return a};m.getValues=function(){return this.values()};m.isEmpty=function(){for(var a in this.elements_)return!1;return!0};m.delete=function(a){a=Xh(a);return a in this.elements_?(delete this.elements_[a],!0):!1};m.remove=function(a){return this.delete(a)};
m.__iterator__=function(){return Wh(this.getValues())};var ai=function(a){if(a.getValues&&typeof a.getValues=="function")return a.getValues();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(Ha(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},bi=function(a){if(a.getKeys&&typeof a.getKeys=="function")return a.getKeys();if(!a.getValues||typeof a.getValues!="function"){if(typeof Map!==
"undefined"&&a instanceof Map)return Array.from(a.keys());if(!(typeof Set!=="undefined"&&a instanceof Set)){if(Ha(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},ci=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(Ha(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=bi(a),e=ai(a),g=e.length,f=0;f<g;f++)b.call(c,e[f],d&&d[f],a)};var di=function(a){this.domain_=this.userInfo_=this.scheme_="";this.port_=null;this.fragment_=this.path_="";this.ignoreCase_=this.isReadOnly_=!1;if(a instanceof di){this.ignoreCase_=a.ignoreCase_;ei(this,a.scheme_);var b=a.userInfo_;Y(this);this.userInfo_=b;b=a.domain_;Y(this);this.domain_=b;fi(this,a.port_);b=a.path_;Y(this);this.path_=b;gi(this,a.queryData_.clone());a=a.fragment_;Y(this);this.fragment_=a}else a&&(b=String(a).match(Ag))?(this.ignoreCase_=!1,ei(this,b[1]||"",!0),a=b[2]||"",Y(this),
this.userInfo_=hi(a),a=b[3]||"",Y(this),this.domain_=hi(a,!0),fi(this,b[4]),a=b[5]||"",Y(this),this.path_=hi(a,!0),gi(this,b[6]||"",!0),a=b[7]||"",Y(this),this.fragment_=hi(a)):(this.ignoreCase_=!1,this.queryData_=new ii(null,this.ignoreCase_))};
di.prototype.toString=function(){var a=[],b=this.scheme_;b&&a.push(ji(b,ki,!0),":");var c=this.domain_;if(c||b=="file")a.push("//"),(b=this.userInfo_)&&a.push(ji(b,ki,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.port_,c!=null&&a.push(":",String(c));if(c=this.path_)this.domain_&&c.charAt(0)!="/"&&a.push("/"),a.push(ji(c,c.charAt(0)=="/"?li:mi,!0));(c=this.queryData_.toString())&&a.push("?",c);(c=this.fragment_)&&a.push("#",ji(c,ni));return a.join("")};
di.prototype.resolve=function(a){var b=this.clone(),c=!!a.scheme_;c?ei(b,a.scheme_):c=!!a.userInfo_;if(c){var d=a.userInfo_;Y(b);b.userInfo_=d}else c=!!a.domain_;c?(d=a.domain_,Y(b),b.domain_=d):c=a.port_!=null;d=a.path_;if(c)fi(b,a.port_);else if(c=!!a.path_){if(d.charAt(0)!="/")if(this.domain_&&!this.path_)d="/"+d;else{var e=b.path_.lastIndexOf("/");e!=-1&&(d=b.path_.slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(e.indexOf("./")!=-1||e.indexOf("/.")!=-1){d=e.lastIndexOf("/",0)==0;e=e.split("/");
for(var g=[],f=0;f<e.length;){var h=e[f++];h=="."?d&&f==e.length&&g.push(""):h==".."?((g.length>1||g.length==1&&g[0]!="")&&g.pop(),d&&f==e.length&&g.push("")):(g.push(h),d=!0)}d=g.join("/")}else d=e}c?(Y(b),b.path_=d):c=a.queryData_.toString()!=="";c?gi(b,a.queryData_.clone()):c=!!a.fragment_;c&&(a=a.fragment_,Y(b),b.fragment_=a);return b};di.prototype.clone=function(){return new di(this)};
var ei=function(a,b,c){Y(a);a.scheme_=c?hi(b,!0):b;a.scheme_&&(a.scheme_=a.scheme_.replace(/:$/,""));return a},fi=function(a,b){Y(a);if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("Bad port number "+b);a.port_=b}else a.port_=null},gi=function(a,b,c){Y(a);b instanceof ii?(a.queryData_=b,a.queryData_.setIgnoreCase(a.ignoreCase_)):(c||(b=ji(b,oi)),a.queryData_=new ii(b,a.ignoreCase_))};di.prototype.getQuery=function(){return this.queryData_.toString()};
di.prototype.removeParameter=function(a){Y(this);this.queryData_.remove(a);return this};var Y=function(a){if(a.isReadOnly_)throw Error("Tried to modify a read-only Uri");};di.prototype.setIgnoreCase=function(a){this.ignoreCase_=a;this.queryData_&&this.queryData_.setIgnoreCase(a)};
var hi=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},ji=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,pi),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},pi=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},ki=/[#\/\?@]/g,mi=/[#\?:]/g,li=/[#\?]/g,oi=/[#\?@]/g,ni=/#/g,ii=function(a,b){this.count_=this.keyMap_=null;this.encodedQuery_=a||null;this.ignoreCase_=!!b},qi=function(a){a.keyMap_||(a.keyMap_=
new Map,a.count_=0,a.encodedQuery_&&Bg(a.encodedQuery_,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};m=ii.prototype;m.add=function(a,b){qi(this);this.encodedQuery_=null;a=ri(this,a);var c=this.keyMap_.get(a);c||this.keyMap_.set(a,c=[]);c.push(b);this.count_+=1;return this};m.remove=function(a){qi(this);a=ri(this,a);return this.keyMap_.has(a)?(this.encodedQuery_=null,this.count_-=this.keyMap_.get(a).length,this.keyMap_.delete(a)):!1};
m.clear=function(){this.keyMap_=this.encodedQuery_=null;this.count_=0};m.isEmpty=function(){qi(this);return this.count_==0};m.containsKey=function(a){qi(this);a=ri(this,a);return this.keyMap_.has(a)};m.forEach=function(a,b){qi(this);this.keyMap_.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};
m.getKeys=function(){qi(this);for(var a=Array.from(this.keyMap_.values()),b=Array.from(this.keyMap_.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],g=0;g<e.length;g++)c.push(b[d]);return c};m.getValues=function(a){qi(this);var b=[];if(typeof a==="string")this.containsKey(a)&&(b=b.concat(this.keyMap_.get(ri(this,a))));else{a=Array.from(this.keyMap_.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
m.set=function(a,b){qi(this);this.encodedQuery_=null;a=ri(this,a);this.containsKey(a)&&(this.count_-=this.keyMap_.get(a).length);this.keyMap_.set(a,[b]);this.count_+=1;return this};m.get=function(a,b){if(!a)return b;a=this.getValues(a);return a.length>0?String(a[0]):b};
m.toString=function(){if(this.encodedQuery_)return this.encodedQuery_;if(!this.keyMap_)return"";for(var a=[],b=Array.from(this.keyMap_.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.getValues(d);for(var g=0;g<d.length;g++){var f=e;d[g]!==""&&(f+="="+encodeURIComponent(String(d[g])));a.push(f)}}return this.encodedQuery_=a.join("&")};
m.clone=function(){var a=new ii;a.encodedQuery_=this.encodedQuery_;this.keyMap_&&(a.keyMap_=new Map(this.keyMap_),a.count_=this.count_);return a};var ri=function(a,b){b=String(b);a.ignoreCase_&&(b=b.toLowerCase());return b};
ii.prototype.setIgnoreCase=function(a){a&&!this.ignoreCase_&&(qi(this),this.encodedQuery_=null,this.keyMap_.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.remove(d),b.length>0&&(this.encodedQuery_=null,this.keyMap_.set(ri(this,d),ab(b)),this.count_+=b.length))},this));this.ignoreCase_=a};ii.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)ci(arguments[b],function(c,d){this.add(d,c)},this)};new Yh("head HEAD link LINK style STYLE meta META defs DEFS script SCRIPT html HTML base BASE colgroup COLGROUP col COL wbr WBR content CONTENT slot SLOT".split(" "));new Yh("svg SVG polygon POLYGON g G br BR".split(" "));var si=function(a,b){a=new di(a);return b===void 0||b?ei(a,a.scheme_||location.protocol).toString():ei(a,"http").toString()};var ti=v(["https://feedback.googleusercontent.com/resources/annotator.css"]),ui=v(["https://feedback.googleusercontent.com/resources/render_frame2.html"]);T(ti);T(ui);var vi=function(a){var b=a||{};a=b.serverUri;var c=a+"/%{resource}",d={resource:S("chat_load.js")};b=b.https;var e=[S("//www.google.com/tools/feedback/%{resource}"),S("https://www.google.com/tools/feedback/%{resource}"),S("https://support.google.com/inapp/%{resource}"),S("https://sandbox.google.com/inapp/%{resource}"),S("https://feedback2-test.corp.google.com/inapp/%{resource}"),S("https://feedback2-test.corp.googleusercontent.com/inapp/%{resource}"),S("https://sandbox.google.com/tools/feedback/%{resource}"),
S("https://feedback2-test.corp.google.com/tools/feedback/%{resource}"),S("https://feedback2-test.corp.googleusercontent.com/tools/feedback/%{resource}"),S("https://www.google.cn/tools/feedback/%{resource}")].filter(function(g){return jd(g)==c})[0];if(e)return qd(e,d);a=si(a,b===void 0||!!b);a=ud(yd(a));return qd(S("//www.google.com/tools/feedback/%{resource}"),d)};Da("userfeedback.api.help.startHelpWithChatSupport",function(a,b){a.flow="help";Th(a,b)});var wi=function(a,b){var c=a.serverUri||"//www.google.com/tools/feedback";z.GOOGLE_HELP_CHAT_ARGUMENTS=arguments;var d=Pd(document,"SCRIPT");c=vi({serverUri:c});Id(d,c);window.document.body.appendChild(d)};Da("userfeedback.api.help.loadChatSupport",wi);var xi=v(["https://www.gstatic.com/uservoice/surveys/resources/","/js/survey/survey_binary__",".js"]),yi=v(["https://gstatic.com/uservoice/surveys/resources/","/js/survey/survey_","_",".css"]),zi=Date.now(),Ai=/uservoice\/surveys\/resources\/(non)?prod\/js\/survey\/survey_(dark|light)_(ltr|rtl)/gi,Bi=/uservoice\/surveys\/resources\/(non)?prod\/js\/survey\/survey_binary__/gi,Z=function(a,b){this.productId_=a;this.receiverUri_=b.receiverUri;this.locale_=b.locale||b.locale||"en".replace(/-/g,"_");this.window_=
b.window||b.window||top;this.productData_=b.productData||b.productData||{};a:{if(a=b.frdProductDataSerializedJspb||b.frdProductDataSerializedJspb)try{var c=hb(a);break a}catch(d){c=void 0;break a}c=(c=b.frdProductData||b.frdProductData)?Ci(c):void 0}this.frdProductDataBase64EncodedString_=c;this.helpCenterPath_=b.helpCenterPath||b.helpCenterPath||"";this.helpcenter=this.helpCenterPath_.startsWith("/")?this.helpCenterPath_.substring(1):this.helpCenterPath_;this.apiKey_=b.apiKey||b.apiKey||"";this.renderApiUri_=
b.renderApiUri||b.renderApiUri||"";this.asxUiUri_=b.asxUiUri||b.asxUiUri||"";this.nonce_=b.nonce||b.nonce||Fd(this.window_);this.surveyStartupConfig_=nh(new mh,ih(!1));this.thirdPartyDomainSupportEnabled_=!1};m=Z.prototype;m.startFeedback=function(a){var b=window.GOOGLE_FEEDBACK_DESTROY_FUNCTION;b&&b();Th(Di(this,a),this.productData_,this.window_)};m.updateProductData=function(a){this.productData_=Object.assign({},this.productData_,a)};
m.updateContext=function(a){var b=A("gapi.rpc");b&&document.getElementById("help_panel_main_frame")!==null&&(a||(a=A("document.location.href",window).substring(0,1200)),b.setup(""),b.sendHandshake("help_panel_main_frame/help_panel_content_frame",""),b.call("help_panel_main_frame/help_panel_content_frame","adaptContext",null,a))};
m.startHelp=function(a){var b=this,c=document.getElementById("help_panel_main_frame");if(c&&c.style.visibility==="hidden"){if(c.style.visibility="visible",a.onRestoreCallback)a.onRestoreCallback()}else{c=(new Date).getTime();var d=a?Ei(this,a,c):{};a=d.openingMode||0;try{Qg(this.receiverUri_||"https://www.google.com/tools/feedback",this.locale_,d.helpCenterContext,this.productId_,this.helpcenter,a).then(function(e){var g=d.fixedHelpPanelContainer;if(g){var f=g.style.width;g.style.width="0";g.style.display=
"none";g.style.width=f!=null?f:"360px";g.replaceChildren()}else Qd(document.getElementById("help_panel_main_frame"));Rg(d,Zc(e),b.window_,b.productData_)})}catch(e){Ed(window,yd("https://support.google.com/"+this.helpcenter))}}};
m.startHelpCard=function(a,b){var c=this,d=(new Date).getTime(),e=a?Ei(this,a,d):{};a=e.openingMode||0;try{Qg(this.receiverUri_||"https://www.google.com/tools/feedback",this.locale_,e.helpCenterContext,this.productId_,this.helpcenter,a).then(function(g){var f;(f=document.getElementById("help_card_main_frame"))==null||f.remove();mf(e,Zc(g),c.window_,b||void 0)})}catch(g){Ed(window,yd("https://support.google.com/"+this.helpcenter))}};
var Di=function(a,b){b=b||{};return{bucket:b.bucket||b.bucket,locale:a.locale_,callback:b.onend||b.onend||function(){},onLoadCallback:b.onLoadCallback||b.onLoadCallback,serverUri:b.serverUri||b.serverUri||a.receiverUri_,productId:a.productId_,productVersion:b.productVersion||b.productVersion,authuser:b.authuser||b.authuser,abuseLink:b.abuseLink||b.abuseLink,customZIndex:b.customZIndex||b.customZIndex,flow:b.flow||b.flow||"wizard",enableAnonymousFeedback:b.enableAnonymousFeedback||b.enableAnonymousFeedback,
allowNonLoggedInFeedback:b.allowNonLoggedInFeedback||b.allowNonLoggedInFeedback,tinyNoPointer:b.tinyNoPointer||b.tinyNoPointer,disableScreenshotAtStartup:b.disableScreenshotAtStartup||b.disableScreenshotAtStartup,disableScreenshotting:b.disableScreenshotting||b.disableScreenshotting,feedbackServerUri:b.feedbackServerUri||b.feedbackServerUri,colorScheme:b.colorScheme||b.colorScheme,triggerId:b.triggerId||b.triggerId,serverEnvironment:b.serverEnvironment||b.serverEnvironment}},Ei=function(a,b,c){var d=
b||{};b=Di(a,b);var e,g,f,h,k,l,n,p=d.anchor,t=d.channel,q=d.context,u=a.helpCenterPath_,B=d.helpFlowSessionId,X=d.enableSendFeedback||!1,nb=d.defaultHelpArticleId,na=d.supportContentUrl,Ga=d.helpPanelTheme,Li=d.nd4cSettings?d.nd4cSettings.isEnabled:!1,Mi=d.nd4cSettings?d.nd4cSettings.countryCode:"",Ni=d.userIp?d.userIp:"",Oi=d.defaultHelpArticleFragment,Pi=d.suggestHost,Qi=a.renderApiUri_,Ri=d.symptom,Si=d.timezone,Ti=d.directToGetHelp||!1,Ui=(e=d.helpGuideOptions)==null?void 0:e.openToHelpGuideEntryButtonId;
e=(g=d.helpGuideOptions)==null?void 0:g.enableHelpGuideMaximize;g=((f=d.helpGuideOptions)==null?void 0:f.enableHelpGuideConversationalAi)===!1?!1:!0;f=(h=d.helpGuideOptions)==null?void 0:h.enableHelpGuideHumanChat;h=a.window_.location.protocol+"//"+a.window_.location.host;var Vi=d.helpPanelMode,Wi=d.fixedHelpPanelContainer,Xi=d.customHelpPanelContainer,Yi=a.frdProductDataBase64EncodedString_,Zi=d.onCloseCallback,$i=d.onMinimizeCallback,aj=d.onLoadCallback,bj=d.onPromotedProductLinkClickCallback,cj=
d.onGseEventCallback,dj=d.openingMode,ej=d.minimizeMode,fj=((k=d.helpGuideOptions)==null?0:k.helpGuideHelpCenterEmbedEntryPoint)?Ci(d.helpGuideOptions.helpGuideHelpCenterEmbedEntryPoint):void 0;k=((l=d.helpGuideOptions)==null?0:l.helpGuideCommonEmbedEntryPoint)?Ci(d.helpGuideOptions.helpGuideCommonEmbedEntryPoint):void 0;a:{var Vf;if((Vf=d.helpGuideOptions)==null?0:Vf.helpGuideStartingFlowSerializedJspb)try{var Gd=hb(d.helpGuideOptions.helpGuideStartingFlowSerializedJspb);break a}catch(gj){Gd=void 0;
break a}var Wf;Gd=((Wf=d.helpGuideOptions)==null?0:Wf.helpGuideStartingFlow)?Ci(d.helpGuideOptions.helpGuideStartingFlow):void 0}a:{var Xf;if((Xf=d.helpGuideOptions)==null?0:Xf.gseSessionOptionsSerializedJspb)try{var Hd=hb(d.helpGuideOptions.gseSessionOptionsSerializedJspb);break a}catch(gj){Hd=void 0;break a}var Yf;Hd=((Yf=d.helpGuideOptions)==null?0:Yf.gseSessionOptions)?Ci(d.helpGuideOptions.gseSessionOptions):void 0}a={anchor:p,channel:t,flow:"help",helpCenterContext:q,helpCenterPath:u,helpFlowSessionId:B,
enableSendFeedback:X,defaultHelpArticleId:nb,supportContentUrl:na,helpPanelTheme:Ga,nd4cSettingsIsEnabled:Li,nd4cSettingsCountryCode:Mi,userIp:Ni,defaultHelpArticleFragment:Oi,newApi:!0,suggestHost:Pi,renderApiUri:Qi,symptom:Ri,timezone:Si,directToGetHelp:Ti,openToHelpGuideEntryButtonId:Ui,enableHelpGuideMaximize:e,enableHelpGuideConversationalAi:g,enableHelpGuideHumanChat:f,startedFromHelpApi:!0,domain:h,helpPanelMode:Vi,fixedHelpPanelContainer:Wi,customHelpPanelContainer:Xi,frdProductData:Yi,onCloseCallback:Zi,
onMinimizeCallback:$i,onLoadCallback:aj,onPromotedProductLinkClickCallback:bj,onGseEventCallback:cj,openingMode:dj,minimizeMode:ej,helpGuideHelpCenterEmbedEntryPoint:fj,helpGuideCommonEmbedEntryPoint:k,helpGuideStartingFlow:Gd,gseSessionOptions:Hd,helpPanelStartTimeMs:c,asxUiUri:a.asxUiUri_,disableEndUserCredentials:d.disableEndUserCredentials,gsePageUrl:(n=d.helpGuideOptions)==null?void 0:n.pageUrl};fd(b,a);return b};m=Z.prototype;
m.loadChatSupport=function(a){var b=a||{};a=a?Ei(this,a):{};fd(a,{escalationJSONString:b.escalationJSONString});wi(a,this.productData_)};
m.requestSurvey=function(a){if(!Fi(a.triggerId))throw Error("Invalid triggerId");var b=Date.now();Gi(this,a,!1).then(function(c,d){var e=d.getSurveyPayload();if(e){var g=Hi(e),f;d.getSession()&&Vc(d.getSession(),1)&&(f=Vc(d.getSession(),1));var h={surveyData:{surveyData:Zc(d),triggerRequestTime:b,apiKey:this.apiKey_,nonProd:a.nonProd,language:e.getLanguage(),libraryVersion:*********,surveyMetadata:{triggerId:a.triggerId,sessionId:f,surveyId:d.getSurveyId()},feedback1pEnabled:P(N(this.surveyStartupConfig_,
hh,1),1),thirdPartyDomainSupportEnabled:this.thirdPartyDomainSupportEnabled_},triggerId:a.triggerId,surveyError:null};setTimeout(function(){return c(h)},g*1E3)}else h={surveyData:null,triggerId:a.triggerId,surveyId:a.surveyIdForTestingMode,surveyError:{reason:"No eligible surveys."}},c(h)}.bind(this,a.callback),function(c){var d="";try{d=JSON.stringify(c)}catch(e){d="message: "+c.message+", stack: "+c.stack}a.callback({surveyData:null,triggerId:a.triggerId,surveyError:{reason:"Failed to trigger survey: "+
d}})})};
m.presentSurvey=function(a){if(a.surveyData){var b=a.surveyData&&a.surveyData.surveyData&&a.surveyData.surveyData.surveyData?a.surveyData.surveyData:a.surveyData;switch(a.defaultStyle){case 1:var c;a.promptStyle=(c=a.promptStyle)!=null?c:2;var d;a.completionStyle=(d=a.completionStyle)!=null?d:2;break;default:var e;a.promptStyle=(e=a.promptStyle)!=null?e:1;var g;a.completionStyle=(g=a.completionStyle)!=null?g:1}c=$c(df,b.surveyData).getSurveyPayload();a:if(d=a.promptStyle,e=c.getDisplaySettings().getAllowedPromptStyleList(),d){switch(d){case 1:d=
e.includes(1);break a;case 2:d=e.includes(2);break a}d=!1}else d=!0;if(d){a:if(d=a.completionStyle,c=c.getCompletion().getAllowedCompletionStyleList(),d){switch(d){case 1:c=c.includes(1);break a;case 2:c=c.includes(2);break a}c=!1}else c=!0;if(c)if(a.parentDomElementId!=void 0&&a.parentDomElementId!=""&&document.getElementById(a.parentDomElementId)==null)a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(a.surveyData,{reason:"Invalid parent dom element id"});else{c=b.nonProd?"nonprod":
"prod";d=a.colorScheme===2?"dark":"light";e=document.body;a:{g=Kd(e);if(g.defaultView&&g.defaultView.getComputedStyle&&(g=g.defaultView.getComputedStyle(e,null))){g=g.direction||g.getPropertyValue("direction")||"";break a}g=""}g="rtl"==(g||(e.currentStyle?e.currentStyle.direction:null)||e.style&&e.style.direction)?"rtl":"ltr";if(a.completionStyle===2&&!Ii("https://gstatic.com/uservoice/surveys/resources/"+c+"/js/survey/survey_"+d+"_"+g+".css")){Ji();e=document.createElement("link");c=T(yi,c,d,g);
if(c instanceof ld)e.href=md(c).toString(),e.rel="stylesheet";else{if(Dd.indexOf("stylesheet")===-1)throw Error('TrustedResourceUrl href attribute required with rel="stylesheet"');c=Ad(c);c!==void 0&&(e.href=c,e.rel="stylesheet")}document.head.appendChild(e)}Ki(this,a,b)}else a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(a.surveyData,{reason:"Invalid completion style"})}else a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(a.surveyData,{reason:"Invalid prompt style"})}};
m.dismissSurvey=function(a){window.hatsNextGlobalObject&&window.hatsNextGlobalObject.dismissSurvey&&window.hatsNextGlobalObject.dismissSurvey(a.surveyMetadata)};
m.scheduleSurvey=function(a){if(!Fi(a.triggerId))throw Error("Invalid triggerId");Gi(this,a,!0).then(function(b){b({surveyData:null,triggerId:a.triggerId,surveyId:a.surveyIdForTestingMode,surveyError:{reason:"Survey scheduled for later."}})}.bind(this,a.callback),function(b){var c="";try{c=JSON.stringify(b)}catch(d){c="message: "+b.message+", stack: "+b.stack}a.callback({surveyData:null,triggerId:a.triggerId,surveyError:{reason:"Failed to trigger survey: "+c}})})};
m.registerHelpAction=function(a,b,c){hj().register(a,b,c)};m.executeHelpAction=function(a,b){hj().execute(a,b)};m.isHelpActionApplicable=function(a){return hj().isApplicable(a)};
var ij=function(a,b){if(b.enableFeedback1pEndpoint&&b.enableTestingMode)return a.surveyStartupConfig_=nh(new mh,ih(!0)),Promise.resolve(a.surveyStartupConfig_);if(!a.productId_||!"5029404 5319991 5171319 ********** 5092034 5096175 5236060 5105063 5189701 713680 96485 17 5145261 5230404 717991 718565".split(" ").includes(a.productId_.toString()))return Promise.resolve(a.surveyStartupConfig_);b=new Me({serverUrl:b.nonProd?"https://stagingqual-feedback-pa-googleapis.sandbox.google.com":"https://feedback-pa.clients6.google.com",
apiKey:"AIzaSyCB6OnnfuitFnaYWu4BvtGKaoLFk4cm-GE",authUser:b.authuser});var c=lh(new kh,(a.productId_||"").toString()).setPlatform(1);return Ne(b,"POST","v1/survey/startup_config",c,mh).then(function(d){return a.surveyStartupConfig_=d})},Gi=function(a,b,c){return ij(a,b).then(function(d){if(P(N(d,hh,1),1)){d=new Me({serverUrl:b.nonProd?"https://stagingqual-feedback-pa-googleapis.sandbox.google.com":"https://feedback-pa.clients6.google.com",apiKey:a.apiKey_,authUser:b.authuser});var e=jj(a,b),g=Se();
g=(new Zg).setTimezoneOffset(g);var f=(new $g).setPlatform(1).setLibraryVersionInt(*********).setSupportedCapabilityList([1,2]),h=b.preferredSurveyLanguageList&&b.preferredSurveyLanguageList.length>0?b.preferredSurveyLanguageList:[a.locale_];h=(new jh).setTriggerId(b.triggerId).setLanguageList(h).setTestingMode(!!b.enableTestingMode);b.surveyIdForTestingMode!=""&&b.surveyIdForTestingMode!=void 0&&b.enableTestingMode==1&&h.setSurveyId(b.surveyIdForTestingMode);g=(new oh).setTriggerContext(h).setClientContext((new ah).setDeviceInfo(g).setLibraryInfo(f));
c!=""&&c!=void 0&&c==1&&g.setScheduledSurveyContext((new fh).setIsScheduledSurvey(c));d=Ne(d,"POST",e,g,ph)}else d=kj(a,b,c);return d},function(){return kj(a,b,c)})},jj=function(a,b){var c=[];var d=d===void 0?!1:d;var e=qh(String(z.location.href));var g=[];var f=d;f=f===void 0?!1:f;var h=z.__SAPISID||z.__APISID||z.__3PSAPISID||z.__OVERRIDE_SID;yh(f)&&(h=h||z.__1PSAPISID);if(h)f=!0;else{if(typeof document!=="undefined"){var k=new wh;h=k.get("SAPISID")||k.get("APISID")||k.get("__Secure-3PAPISID");yh(f)&&
(h=h||k.get("__Secure-1PAPISID"))}f=!!h}f&&(f=(e=e.indexOf("https:")==0||e.indexOf("chrome-extension:")==0||e.indexOf("chrome-untrusted://new-tab-page")==0||e.indexOf("moz-extension:")==0)?z.__SAPISID:z.__APISID,f||typeof document==="undefined"||(f=new wh,f=f.get(e?"SAPISID":"APISID")||f.get("__Secure-3PAPISID")),(f=f?th(f,e?"SAPISIDHASH":"APISIDHASH",c):null)&&g.push(f),e&&yh(d)&&((d=zh("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",c))&&g.push(d),(c=zh("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",
c))&&g.push(c)));g=g.length==0?null:g.join(" ");b.thirdPartyDomainSupportEnabled!=void 0&&b.thirdPartyDomainSupportEnabled&&(a.thirdPartyDomainSupportEnabled_=!0,g=!1);return g?"v1/survey/trigger":"v1/survey/trigger/trigger_anonymous"},kj=function(a,b,c){var d=new Me({serverUrl:b.nonProd?"https://test-scone-pa-googleapis.sandbox.google.com":"https://scone-pa.clients6.google.com",apiKey:a.apiKey_,authUser:b.authuser}),e=jj(a,b),g=Se();g=(new Te).setTimezoneOffset(g);var f=(new Ue).setPlatform(1).setLibraryVersionInt(*********).setSupportedCapabilityList([1,
2]);a=b.preferredSurveyLanguageList&&b.preferredSurveyLanguageList.length>0?b.preferredSurveyLanguageList:[a.locale_];a=(new bf).setTriggerId(b.triggerId).setLanguageList(a).setTestingMode(!!b.enableTestingMode);b.surveyIdForTestingMode!=""&&b.surveyIdForTestingMode!=void 0&&b.enableTestingMode==1&&a.setSurveyId(b.surveyIdForTestingMode);b=(new cf).setTriggerContext(a).setClientContext((new Ve).setDeviceInfo(g).setLibraryInfo(f));c!=""&&c!=void 0&&c==1&&b.setScheduledSurveyContext((new $e).setIsScheduledSurvey(c));
return Ne(d,"POST",e,b,df)},Fi=function(a){return typeof a=="string"&&!!a.match(/^[A-Za-z0-9]+$/)},Hi=function(a){try{var b=parseInt(Uc(N(a.getDisplaySettings().getPromptDelay(),V,1)),10);isNaN(b)&&(b=0)}catch(d){b=0}try{var c=parseInt(Uc(N(a.getDisplaySettings().getPromptDelay(),V,2)),10);isNaN(c)&&(c=0)}catch(d){c=0}return Math.floor(Math.random()*(c-b+1))+b},Ki=function(a,b,c){var d=c.nonProd?"nonprod":"prod",e=c.language&&Tg[c.language.toLowerCase()]?Tg[c.language.toLowerCase()]:Tg[a.locale_.toLowerCase()];
e=e&&e.replace("-","_");e==="fa"&&(e="en");var g=e?encodeURI(e):"en";b.enableReloadScriptWhenLanguageChanges&&!lj("https://www.gstatic.com/uservoice/surveys/resources/"+d+"/js/survey/survey_binary__"+g+".js")&&mj();window.hatsNextGlobalObject?nj(b,c):(e=document.createElement("script"),d=T(xi,d,g),Id(e,d),e.type="text/javascript",e.onload=function(){return nj(b,c)},e.onerror=function(){b.listener&&b.listener.surveyPrompted&&b.listener.surveyPrompted(c,{reason:"Failed to load survey binary"})},e.setAttribute("data-survey-binary",
""),a.nonce_&&e.setAttribute("nonce",a.nonce_),document.querySelector("[data-survey-binary]")||document.body.appendChild(e))},nj=function(a,b){a:{var c="triggerCutoffTime";a.parentDomElementId!=null&&a.parentDomElementId!=""&&(c+="_"+a.parentDomElementId);if(window.hatsNextGlobalObject&&window.hatsNextGlobalObject[c]){if(window.hatsNextGlobalObject[c]>b.triggerRequestTime){a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(b,{reason:"Survey was triggered before the most recent survey event. Please re-trigger the survey."});
c=!1;break a}}else if(zi>b.triggerRequestTime){a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(b,{reason:"Survey must be triggered after initializing the help API."});c=!1;break a}c=Date.now()-b.triggerRequestTime;c>864E5?(a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(b,{reason:"Survey must be triggered within the last 24 hours. Survey was triggered "+(c+" ms ago.")}),c=!1):c=!0}if(c){var d,e,g;window.hatsNextGlobalObject.initSurvey({surveyTriggerResponse:b.surveyData,
nonprod:b.nonProd,darkMode:a.colorScheme==2,seamlessMode:a.seamlessMode,zIndex:a.customZIndex,triggerRequestTime:b.triggerRequestTime,authuser:a.authuser,apiKey:b.apiKey,locale:b.language,customLogoAltText:a.customLogoAltText,customLogoUrl:a.customLogoUrl,productData:a.productData,listener:a.listener,surveyData:b,surveyMetadata:b.surveyMetadata,promptStyle:(d=a.promptStyle)!=null?d:1,completionStyle:(e=a.completionStyle)!=null?e:1,defaultStyle:(g=a.defaultStyle)!=null?g:0,parentDomElementId:a.parentDomElementId,
persistCompletionCard:a.persistCompletionCard,hidePrivacyBanner:a.hidePrivacyBanner,hideInlineSurveyBorder:a.hideInlineSurveyBorder,hideInlineSurveyBackground:a.hideInlineSurveyBackground,feedback1pEnabled:b.feedback1pEnabled,thirdPartyDomainSupportEnabled:b.thirdPartyDomainSupportEnabled})}},hj=function(){var a=A("help.globals.actions",top);a||(a=new ef,Da("help.globals.actions",a,top));return a},lj=function(a){return[].concat(fa(document.getElementsByTagName("script"))).find(function(b){return b&&
b.getAttribute("src")===a})},mj=function(){[].concat(fa(document.getElementsByTagName("script"))).forEach(function(a){var b;if(a==null?0:(b=a.getAttribute("src"))==null?0:b.match(Bi))a.parentNode.removeChild(a),window.hatsNextGlobalObject=null})},Ji=function(){document.querySelectorAll("link[rel=stylesheet]").forEach(function(a){var b;(a==null?0:(b=a.getAttribute("href"))==null?0:b.match(Ai))&&a.parentNode.removeChild(a)})},Ci=function(a){if(!a)return"";try{return hb(Zc(a))}catch(b){return console.log("Failed to serialize and encode proto: ",
b),""}},Ii=function(a){return[].concat(fa(document.querySelectorAll("link[rel=stylesheet]"))).find(function(b){return b&&b.getAttribute("href")===a})};Z.prototype.isHelpActionApplicable=Z.prototype.isHelpActionApplicable;Z.prototype.executeHelpAction=Z.prototype.executeHelpAction;Z.prototype.registerHelpAction=Z.prototype.registerHelpAction;Z.prototype.scheduleSurvey=Z.prototype.scheduleSurvey;Z.prototype.dismissSurvey=Z.prototype.dismissSurvey;Z.prototype.presentSurvey=Z.prototype.presentSurvey;
Z.prototype.requestSurvey=Z.prototype.requestSurvey;Z.prototype.loadChatSupport=Z.prototype.loadChatSupport;Z.prototype.startHelpCard=Z.prototype.startHelpCard;Z.prototype.startHelp=Z.prototype.startHelp;Z.prototype.updateContext=Z.prototype.updateContext;Z.prototype.updateProductData=Z.prototype.updateProductData;Z.prototype.startFeedback=Z.prototype.startFeedback;Da("help.service.Lazy",Z);Da("help.service.Lazy.create",function(a,b){return new Z(a,b)});}).call(this);
