gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([0x200000, ]);
var aa,fa,ha,na,oa,ta,va,xa;aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};fa=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
ha=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.ma=ha(this);na=function(a,b){if(b)a:{var c=_.ma;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&fa(c,a,{configurable:!0,writable:!0,value:b})}};
na("Symbol",function(a){if(a)return a;var b=function(f,h){this.c1=f;fa(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.c1};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
na("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.ma[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&fa(d.prototype,a,{configurable:!0,writable:!0,value:function(){return oa(aa(this))}})}return a});oa=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.sa=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error("b`"+String(a));};ta=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};va=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)ta(d,e)&&(a[e]=d[e])}return a};na("Object.assign",function(a){return a||va});
_.wa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")xa=Object.setPrototypeOf;else{var ya;a:{var Aa={a:!0},Ba={};try{Ba.__proto__=Aa;ya=Ba.a;break a}catch(a){}ya=!1}xa=ya?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.Da=xa;
na("Promise",function(a){function b(){this.Kf=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.YO=function(h){if(this.Kf==null){this.Kf=[];var k=this;this.ZO(function(){k.z7()})}this.Kf.push(h)};var d=_.ma.setTimeout;b.prototype.ZO=function(h){d(h,0)};b.prototype.z7=function(){for(;this.Kf&&this.Kf.length;){var h=this.Kf;this.Kf=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.nq(m)}}}this.Kf=null};b.prototype.nq=function(h){this.ZO(function(){throw h;
})};var e=function(h){this.Ga=0;this.Cf=void 0;this.Qr=[];this.iV=!1;var k=this.fF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.fF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.Rda),reject:h(this.RJ)}};e.prototype.Rda=function(h){if(h===this)this.RJ(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.wfa(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.Qda(h):this.mS(h)}};e.prototype.Qda=function(h){var k=void 0;try{k=h.then}catch(l){this.RJ(l);return}typeof k=="function"?this.xfa(k,h):this.mS(h)};e.prototype.RJ=function(h){this.XZ(2,h)};e.prototype.mS=function(h){this.XZ(1,h)};e.prototype.XZ=function(h,k){if(this.Ga!=0)throw Error("c`"+h+"`"+k+"`"+this.Ga);this.Ga=h;this.Cf=k;this.Ga===2&&this.gea();this.A7()};e.prototype.gea=function(){var h=this;d(function(){if(h.Yba()){var k=_.ma.console;typeof k!=="undefined"&&k.error(h.Cf)}},
1)};e.prototype.Yba=function(){if(this.iV)return!1;var h=_.ma.CustomEvent,k=_.ma.Event,l=_.ma.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.ma.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.Cf;return l(h)};e.prototype.A7=function(){if(this.Qr!=null){for(var h=0;h<this.Qr.length;++h)f.YO(this.Qr[h]);
this.Qr=null}};var f=new b;e.prototype.wfa=function(h){var k=this.fF();h.iy(k.resolve,k.reject)};e.prototype.xfa=function(h,k){var l=this.fF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,t){return typeof q=="function"?function(v){try{m(q(v))}catch(u){n(u)}}:t}var m,n,p=new e(function(q,t){m=q;n=t});this.iy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.iy=function(h,k){function l(){switch(m.Ga){case 1:h(m.Cf);
break;case 2:k(m.Cf);break;default:throw Error("d`"+m.Ga);}}var m=this;this.Qr==null?f.YO(l):this.Qr.push(l);this.iV=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.sa(h),n=m.next();!n.done;n=m.next())c(n.value).iy(k,l)})};e.all=function(h){var k=_.sa(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(v){return function(u){q[v]=u;t--;t==0&&m(q)}}var q=[],t=0;do q.push(void 0),t++,c(l.value).iy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Ea=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
na("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ea(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});
na("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!ta(l,f)){var m=new b;fa(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Ha=(h+=Math.random()+1).toString();if(l){l=_.sa(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!ta(l,f))throw Error("f`"+l);l[f][this.Ha]=m;return this};k.prototype.get=function(l){return c(l)&&ta(l,f)?l[f][this.Ha]:void 0};k.prototype.has=function(l){return c(l)&&ta(l,f)&&ta(l[f],this.Ha)};k.prototype.delete=
function(l){return c(l)&&ta(l,f)&&ta(l[f],this.Ha)?delete l[f][this.Ha]:!1};return k});
na("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.sa([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.sa(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.kf?m.kf.value=l:(m.kf={next:this[1],Yk:this[1].Yk,head:this[1],key:k,value:l},m.list.push(m.kf),this[1].Yk.next=m.kf,this[1].Yk=m.kf,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.kf&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.kf.Yk.next=k.kf.next,k.kf.next.Yk=
k.kf.Yk,k.kf.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Yk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).kf};c.prototype.get=function(k){return(k=d(this,k).kf)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&ta(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,kf:p}}return{id:m,list:n,index:-1,kf:void 0}},e=function(k,l){var m=k[1];return oa(function(){if(m){for(;m.head!=k[1];)m=m.Yk;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Yk=k.next=k.head=k},h=0;return c});na("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});na("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Ea(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});
var Ga=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};na("Array.prototype.keys",function(a){return a?a:function(){return Ga(this,function(b){return b})}});
na("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.sa([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Da=new Map;if(c){c=
_.sa(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Da.size};b.prototype.add=function(c){c=c===0?0:c;this.Da.set(c,c);this.size=this.Da.size;return this};b.prototype.delete=function(c){c=this.Da.delete(c);this.size=this.Da.size;return c};b.prototype.clear=function(){this.Da.clear();this.size=0};b.prototype.has=function(c){return this.Da.has(c)};b.prototype.entries=function(){return this.Da.entries()};b.prototype.values=function(){return this.Da.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Da.forEach(function(f){return c.call(d,f,f,e)})};return b});na("Array.prototype.entries",function(a){return a?a:function(){return Ga(this,function(b,c){return[b,c]})}});var Ja=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{pU:e,AD:f}}return{pU:-1,AD:void 0}};
na("Array.prototype.find",function(a){return a?a:function(b,c){return Ja(this,b,c).AD}});na("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});na("Array.prototype.values",function(a){return a?a:function(){return Ga(this,function(b,c){return c})}});
na("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});na("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)ta(b,d)&&c.push([d,b[d]]);return c}});
na("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)ta(b,d)&&c.push(b[d]);return c}});na("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});na("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
na("String.prototype.includes",function(a){return a?a:function(b,c){return Ea(this,b,"includes").indexOf(b,c||0)!==-1}});na("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});
na("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});na("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});na("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
na("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});na("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});na("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});var La=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
na("Array.prototype.at",function(a){return a?a:La});var Oa=function(a){return a?a:La};na("Int8Array.prototype.at",Oa);na("Uint8Array.prototype.at",Oa);na("Uint8ClampedArray.prototype.at",Oa);na("Int16Array.prototype.at",Oa);na("Uint16Array.prototype.at",Oa);na("Int32Array.prototype.at",Oa);na("Uint32Array.prototype.at",Oa);na("Float32Array.prototype.at",Oa);na("Float64Array.prototype.at",Oa);na("String.prototype.at",function(a){return a?a:La});
na("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});na("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ja(this,b,c).pU}});
na("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});
na("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Ea(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});na("globalThis",function(a){return a||_.ma});na("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});_.Pa={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Ra=_.Ra||{};_.Sa=this||self;_.Wa=_.Sa._F_toggles||[];_.Za="closure_uid_"+(Math.random()*1E9>>>0);_.$a=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.r=function(a,b){a=a.split(".");var c=_.Sa;a[0]in c||typeof c.execScript=="undefined"||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.bb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.tt=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.eb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0}});
var hb,lb;_.gb=function(a){return function(){return _.fb[a].apply(this,arguments)}};_.fb=[];hb=function(a,b,c){return a.call.apply(a.bind,arguments)};lb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};
_.z=function(a,b,c){_.z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?hb:lb;return _.z.apply(null,arguments)};
/*

 SPDX-License-Identifier: Apache-2.0
*/
var Hb,Mb,ac,pc,Bc,Dc,Hc,Kc,Mc,Nc,Rc,Sc,Vc;_.nb=function(a,b){return _.fb[a]=b};_.rb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.rb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.tY=!0};_.tb=function(a,b){return(0,_.sb)(a,b)>=0};_.ub=function(a,b){b=(0,_.sb)(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.vb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};
_.wb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};_.xb=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.yb=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.zb=function(a,b){for(var c in a)if(a[c]==b)return!0;return!1};_.Bb=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Ab.length;f++)c=Ab[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};
_.Gb=function(a){var b=arguments.length;if(b==1&&Array.isArray(arguments[0]))return _.Gb.apply(null,arguments[0]);for(var c={},d=0;d<b;d++)c[arguments[d]]=!0;return c};Hb=function(a){return{valueOf:a}.valueOf()};_.Kb=function(a){if(a instanceof _.Jb)return a.XX;throw Error("m");};Mb=function(a){return new Lb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};
_.Pb=function(a){var b=b===void 0?Nb:b;a:if(b=b===void 0?Nb:b,!(a instanceof _.Jb)){for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Lb&&d.Ej(a)){a=new _.Jb(a);break a}}a=void 0}return a||_.Ob};_.Sb=function(a){if(Qb.test(a))return a};_.Tb=function(a){return a instanceof _.Jb?_.Kb(a):_.Sb(a)};
_.Wb=function(a,b){b=b===void 0?{}:b;if(a instanceof _.Ub)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");b.bra&&(a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;"));b.fda&&(a=a.replace(/(\r\n|\n|\r)/g,"<br>"));b.cra&&(a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>'));return _.Vb(a)};_.Xb=function(){var a=_.Sa.navigator;return a&&(a=a.userAgent)?a:""};
ac=function(a){return _.Yb?_.Zb?_.Zb.brands.some(function(b){return(b=b.brand)&&_.$b(b,a)}):!1:!1};_.bc=function(a){return _.$b(_.Xb(),a)};_.cc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.dc=function(){return _.Yb?!!_.Zb&&_.Zb.brands.length>0:!1};_.ec=function(){return _.dc()?!1:_.bc("Opera")};_.fc=function(){return _.dc()?!1:_.bc("Trident")||_.bc("MSIE")};_.gc=function(){return _.dc()?!1:_.bc("Edge")};
_.hc=function(){return _.dc()?ac("Microsoft Edge"):_.bc("Edg/")};_.ic=function(){return _.dc()?ac("Opera"):_.bc("OPR")};_.jc=function(){return _.bc("Firefox")||_.bc("FxiOS")};_.lc=function(){return _.bc("Safari")&&!(_.kc()||(_.dc()?0:_.bc("Coast"))||_.ec()||_.gc()||_.hc()||_.ic()||_.jc()||_.bc("Silk")||_.bc("Android"))};_.kc=function(){return _.dc()?ac("Chromium"):(_.bc("Chrome")||_.bc("CriOS"))&&!_.gc()||_.bc("Silk")};_.mc=function(){return _.bc("Android")&&!(_.kc()||_.jc()||_.ec()||_.bc("Silk"))};
_.nc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.oc=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
pc=function(){return _.Yb?!!_.Zb&&!!_.Zb.platform:!1};_.qc=function(){return pc()?_.Zb.platform==="Android":_.bc("Android")};_.rc=function(){return _.bc("iPhone")&&!_.bc("iPod")&&!_.bc("iPad")};_.sc=function(){return _.rc()||_.bc("iPad")||_.bc("iPod")};_.tc=function(){return pc()?_.Zb.platform==="macOS":_.bc("Macintosh")};_.uc=function(){return pc()?_.Zb.platform==="Windows":_.bc("Windows")};_.vc=function(){return pc()?_.Zb.platform==="Chrome OS":_.bc("CrOS")};
_.xc=function(a,b){if(a.nodeType===1){var c=a.tagName;if(c==="SCRIPT"||c==="STYLE")throw Error("m");}a.innerHTML=_.wc(b)};_.yc=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};_.zc=function(a){if(!(a instanceof Array)){a=_.sa(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};
_.A=function(a,b){a.prototype=(0,_.wa)(b.prototype);a.prototype.constructor=a;if(_.Da)(0,_.Da)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.Ac=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.Cc=function(a,b){a=a.split(".");b=b||_.Sa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};
Dc=function(a,b){var c=_.Cc("WIZ_global_data.oxN3nb");a=c&&c[a];return a!=null?a:b};_.Fc=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.Gc=function(a){var b=_.Fc(a);return b=="array"||b=="object"&&typeof a.length=="number"};Hc=0;_.Ic=function(a){return Object.prototype.hasOwnProperty.call(a,_.Za)&&a[_.Za]||(a[_.Za]=++Hc)};_.Jc=function(){return Date.now()};Kc=function(a){return a};_.bb(_.rb,Error);_.rb.prototype.name="CustomError";Mc={};Nc={};
_.Oc=function(a,b){this.I_=a===Mc&&b||"";this.Y4=Nc};_.Oc.prototype.toString=function(){return this.I_};_.Pc=function(a){return a instanceof _.Oc&&a.constructor===_.Oc&&a.Y4===Nc?a.I_:"type_error:Const"};_.Qc=function(a){return new _.Oc(Mc,a)};Sc=function(){if(Rc===void 0){var a=null,b=_.Sa.trustedTypes;if(b&&b.createPolicy)try{a=b.createPolicy("gapi#html",{createHTML:Kc,createScript:Kc,createScriptURL:Kc})}catch(c){_.Sa.console&&_.Sa.console.error(c.message)}Rc=a}return Rc};
_.Tc=function(a){this.VX=a};_.Tc.prototype.toString=function(){return this.VX+""};_.Uc=function(a){if(a instanceof _.Tc&&a.constructor===_.Tc)return a.VX;_.Fc(a);return"type_error:TrustedResourceUrl"};Vc={};_.Xc=function(a){var b=Sc();a=b?b.createScriptURL(a):a;return new _.Tc(a,Vc)};_.Yc=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};
_.sb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Zc=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.$c=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.ad=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.bd=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.cd=Array.prototype.reduce?function(a,b,c){return Array.prototype.reduce.call(a,b,c)}:function(a,b,c){var d=c;(0,_.$c)(a,function(e,f){d=b.call(void 0,d,e,f,a)});return d};_.dd=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};
_.ed=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Ab="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");var jd,kd,ld,md,nd,od,id,qd;_.fd=function(a,b){return a.lastIndexOf(b,0)==0};_.gd=function(a){return/^[\s\xa0]*$/.test(a)};_.hd=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};
_.pd=function(a){if(!id.test(a))return a;a.indexOf("&")!=-1&&(a=a.replace(jd,"&amp;"));a.indexOf("<")!=-1&&(a=a.replace(kd,"&lt;"));a.indexOf(">")!=-1&&(a=a.replace(ld,"&gt;"));a.indexOf('"')!=-1&&(a=a.replace(md,"&quot;"));a.indexOf("'")!=-1&&(a=a.replace(nd,"&#39;"));a.indexOf("\x00")!=-1&&(a=a.replace(od,"&#0;"));return a};jd=/&/g;kd=/</g;ld=/>/g;md=/"/g;nd=/'/g;od=/\x00/g;id=/[\x00&<>"']/;_.$b=function(a,b){return a.indexOf(b)!=-1};
_.rd=function(a,b){var c=0;a=(0,_.hd)(String(a)).split(".");b=(0,_.hd)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=qd(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||qd(f[2].length==0,h[2].length==0)||qd(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
qd=function(a,b){return a<b?-1:a>b?1:0};_.Jb=function(a){this.XX=a};_.Jb.prototype.toString=function(){return this.XX};_.Ob=new _.Jb("about:invalid#zClosurez");var Lb,Nb,Qb;Lb=function(a){this.Ej=a};Nb=[Mb("data"),Mb("http"),Mb("https"),Mb("mailto"),Mb("ftp"),new Lb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.sd=Hb(function(){return typeof URL==="function"});Qb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.td={};_.ud=function(a){this.UX=a};_.ud.prototype.toString=function(){return this.UX.toString()};_.vd=new _.ud("",_.td);_.wd=RegExp("^[-+,.\"'%_!#/ a-zA-Z0-9\\[\\]]+$");_.xd=RegExp("\\b(url\\([ \t\n]*)('[ -&(-\\[\\]-~]*'|\"[ !#-\\[\\]-~]*\"|[!#-&*-\\[\\]-~]*)([ \t\n]*\\))","g");_.yd=RegExp("\\b(calc|cubic-bezier|fit-content|hsl|hsla|linear-gradient|matrix|minmax|radial-gradient|repeat|rgb|rgba|(rotate|scale|translate)(X|Y|Z|3d)?|steps|var)\\([-+*/0-9a-zA-Z.%#\\[\\], ]+\\)","g");var Cd;_.Ad={};_.Bd=function(a){this.TX=a};_.Bd.prototype.toString=function(){return this.TX.toString()};_.Dd=function(a){a=_.Pc(a);return a.length===0?Cd:new _.Bd(a,_.Ad)};Cd=new _.Bd("",_.Ad);var Ed;Ed={};_.Ub=function(a){this.SX=a};_.Ub.prototype.toString=function(){return this.SX.toString()};_.wc=function(a){if(a instanceof _.Ub&&a.constructor===_.Ub)return a.SX;_.Fc(a);return"type_error:SafeHtml"};_.Fd=function(a){return a instanceof _.Ub?a:_.Vb(_.pd(String(a)))};_.Vb=function(a){var b=Sc();a=b?b.createHTML(a):a;return new _.Ub(a,Ed)};_.Id=new _.Ub(_.Sa.trustedTypes&&_.Sa.trustedTypes.emptyHTML||"",Ed);var Kd=function(a,b,c,d){var e=new Map(Jd);this.M5=a;this.TQ=e;this.N5=b;this.A9=c;this.pT=d};var Ld="ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" "),
Jd=[["A",new Map([["href",{Bd:2}]])],["AREA",new Map([["href",{Bd:2}]])],["LINK",new Map([["href",{Bd:5,conditions:new Map([["rel",new Set("alternate author bookmark canonical cite help icon license next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" "))]])}]])],["SOURCE",new Map([["src",{Bd:5}],["srcset",{Bd:6}]])],["IMG",new Map([["src",{Bd:5}],["srcset",{Bd:6}]])],["VIDEO",new Map([["src",{Bd:5}]])],["AUDIO",new Map([["src",{Bd:5}]])]],Md="title aria-atomic aria-autocomplete aria-busy aria-checked aria-current aria-disabled aria-dropeffect aria-expanded aria-haspopup aria-hidden aria-invalid aria-label aria-level aria-live aria-multiline aria-multiselectable aria-orientation aria-posinset aria-pressed aria-readonly aria-relevant aria-required aria-selected aria-setsize aria-sort aria-valuemax aria-valuemin aria-valuenow aria-valuetext alt align autocapitalize autocomplete autocorrect autofocus autoplay bgcolor border cellpadding cellspacing checked color cols colspan controls datetime disabled download draggable enctype face formenctype frameborder height hreflang hidden ismap label lang loop max maxlength media minlength min multiple muted nonce open placeholder preload rel required reversed role rows rowspan selected shape size sizes slot span spellcheck start step summary translate type valign value width wrap itemscope itemtype itemid itemprop itemref".split(" "),
Nd=[["dir",{Bd:3,conditions:Hb(function(){return new Map([["dir",new Set(["auto","ltr","rtl"])]])})}],["async",{Bd:3,conditions:Hb(function(){return new Map([["async",new Set(["async"])]])})}],["cite",{Bd:2}],["loading",{Bd:3,conditions:Hb(function(){return new Map([["loading",new Set(["eager","lazy"])]])})}],["poster",{Bd:2}],["target",{Bd:3,conditions:Hb(function(){return new Map([["target",new Set(["_self","_blank"])]])})}]],Pd=new Kd(new Set(Ld),new Set(Md),new Map(Nd)),Qd=new Kd(new Set(Ld.concat(["BUTTON",
"INPUT"])),new Set(Hb(function(){return Md.concat(["class","id","name"])})),new Map(Hb(function(){return Nd.concat([["style",{Bd:1}]])}))),Rd=new Kd(new Set(Hb(function(){return Ld.concat("STYLE TITLE INPUT TEXTAREA BUTTON LABEL".split(" "))})),new Set(Hb(function(){return Md.concat(["class","id","tabindex","contenteditable","name"])})),new Map(Hb(function(){return Nd.concat([["style",{Bd:1}]])})),new Set(["data-","aria-"]));var Sd=function(a){this.MY=a};Sd.prototype.createTextNode=function(a){return document.createTextNode(a)};_.Td=Hb(function(){return new Sd(Pd)});_.Ud=Hb(function(){return new Sd(Qd)});_.Vd=Hb(function(){return new Sd(Rd)});var Wd=!!(_.Wa[0]&1024),Xd=!!(_.Wa[0]&32),Yd=!!(_.Wa[0]&2048),Zd=!!(_.Wa[0]&16),$d=!!(_.Wa[0]&8);var ae;ae=Dc(1,!0);_.Yb=Wd?Yd:Dc(610401301,!1);_.be=Wd?Xd||!Zd:Dc(188588736,ae);_.ce=Wd?Xd||!$d:Dc(645172343,ae);var de;de=_.Sa.navigator;_.Zb=de?de.userAgentData||null:null;var ee=function(a){ee[" "](a);return a};ee[" "]=function(){};_.fe=function(a,b){try{return ee(a[b]),!0}catch(c){}return!1};var ve,we,Be;_.ge=_.ec();_.he=_.fc();_.ie=_.bc("Edge");_.je=_.ie||_.he;_.ke=_.bc("Gecko")&&!(_.$b(_.Xb().toLowerCase(),"webkit")&&!_.bc("Edge"))&&!(_.bc("Trident")||_.bc("MSIE"))&&!_.bc("Edge");_.le=_.$b(_.Xb().toLowerCase(),"webkit")&&!_.bc("Edge");_.me=_.le&&_.bc("Mobile");_.ne=_.tc();_.oe=_.uc();_.pe=(pc()?_.Zb.platform==="Linux":_.bc("Linux"))||_.vc();_.qe=_.qc();_.re=_.rc();_.se=_.bc("iPad");_.te=_.bc("iPod");_.ue=_.sc();ve=function(){var a=_.Sa.document;return a?a.documentMode:void 0};
a:{var xe="",ye=function(){var a=_.Xb();if(_.ke)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.ie)return/Edge\/([\d\.]+)/.exec(a);if(_.he)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.le)return/WebKit\/(\S+)/.exec(a);if(_.ge)return/(?:Version)[ \/]?(\S+)/.exec(a)}();ye&&(xe=ye?ye[1]:"");if(_.he){var ze=ve();if(ze!=null&&ze>parseFloat(xe)){we=String(ze);break a}}we=xe}_.Ae=we;if(_.Sa.document&&_.he){var De=ve();Be=De?De:parseInt(_.Ae,10)||void 0}else Be=void 0;_.Ee=Be;try{(new self.OffscreenCanvas(0,0)).getContext("2d")}catch(a){};var Fe,He;Fe=_.Yc(function(){var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);b=a.firstChild.firstChild;a.innerHTML=_.wc(_.Id);return!b.parentElement});_.Ge=function(a,b){if(Fe())for(;a.lastChild;)a.removeChild(a.lastChild);a.innerHTML=_.wc(b)};He=/^[\w+/_-]+[=]{0,2}$/;_.Ie=function(a,b){b=(b||_.Sa).document;return b.querySelector?(a=b.querySelector(a))&&(a=a.nonce||a.getAttribute("nonce"))&&He.test(a)?a:"":""};_.Je=function(a,b){this.width=a;this.height=b};_.Ke=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.Je.prototype;_.g.clone=function(){return new _.Je(this.width,this.height)};_.g.Nx=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.Nx()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.Le=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.Me=Math.random()*2147483648|0;var Se,$e,Ze;_.Pe=function(a){return a?new _.Ne(_.Oe(a)):Bc||(Bc=new _.Ne)};_.Qe=function(a,b){return typeof b==="string"?a.getElementById(b):b};
_.Re=function(a,b,c,d){a=d||a;b=b&&b!="*"?String(b).toUpperCase():"";if(a.querySelectorAll&&a.querySelector&&(b||c))return a.querySelectorAll(b+(c?"."+c:""));if(c&&a.getElementsByClassName){a=a.getElementsByClassName(c);if(b){d={};for(var e=0,f=0,h;h=a[f];f++)b==h.nodeName&&(d[e++]=h);d.length=e;return d}return a}a=a.getElementsByTagName(b||"*");if(c){d={};for(f=e=0;h=a[f];f++)b=h.className,typeof b.split=="function"&&_.tb(b.split(/\s+/),c)&&(d[e++]=h);d.length=e;return d}return a};
_.Te=function(a,b){_.wb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:Se.hasOwnProperty(d)?a.setAttribute(Se[d],c):_.fd(d,"aria-")||_.fd(d,"data-")?a.setAttribute(d,c):a[d]=c})};Se={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.Ve=function(a){return _.Ue(a||window)};
_.Ue=function(a){a=a.document;a=_.We(a)?a.documentElement:a.body;return new _.Je(a.clientWidth,a.clientHeight)};_.Ye=function(a){return a?a.parentWindow||a.defaultView:window};_.af=function(a,b){var c=b[1],d=Ze(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.Te(d,c));b.length>2&&$e(a,d,b,2);return d};
$e=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.Gc(f)||_.yc(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.yc(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.$c(h?_.vb(f):f,e)}}};_.bf=function(a){return Ze(document,a)};
Ze=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.We=function(a){return a.compatMode=="CSS1Compat"};_.cf=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.df=function(a,b){$e(_.Oe(a),a,arguments,1)};_.ef=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.ff=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.gf=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.hf=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.jf=function(a){return _.yc(a)&&a.nodeType==1};
_.kf=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.Oe=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.lf=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.ef(a),a.appendChild(_.Oe(a).createTextNode(String(b)))};_.Ne=function(a){this.Rb=a||_.Sa.document||document};_.g=_.Ne.prototype;_.g.Ja=_.Pe;_.g.QK=_.gb(0);_.g.Ab=function(){return this.Rb};_.g.O=_.gb(1);_.g.getElementsByTagName=function(a,b){return(b||this.Rb).getElementsByTagName(String(a))};
_.g.aH=_.gb(2);_.g.wa=function(a,b,c){return _.af(this.Rb,arguments)};_.g.createElement=function(a){return Ze(this.Rb,a)};_.g.createTextNode=function(a){return this.Rb.createTextNode(String(a))};_.g.getWindow=function(){var a=this.Rb;return a.parentWindow||a.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.df;_.g.canHaveChildren=_.cf;_.g.Be=_.ef;_.g.LU=_.ff;_.g.removeNode=_.gf;_.g.kG=_.hf;_.g.isElement=_.jf;_.g.contains=_.kf;_.g.CG=_.Oe;_.g.Cj=_.gb(3);
_.mf=function(a){var b=_.Ac.apply(1,arguments);if(b.length===0)return _.Xc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.Xc(c)};_.nf=function(a){var b,c;return(a=(c=(b=a.document).querySelector)==null?void 0:c.call(b,"script[nonce]"))?a.nonce||a.getAttribute("nonce")||"":""};_.of=function(a,b){a.src=_.Uc(b);(b=_.nf(a.ownerDocument&&a.ownerDocument.defaultView||window))&&a.setAttribute("nonce",b)};_.pf=function(a){return a.raw=a};
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.qf=function(a){return a===null?"null":a===void 0?"undefined":a};_.rf=window;_.sf=document;_.tf=_.rf.location;_.uf=/\[native code\]/;_.vf=function(a,b,c){return a[b]=a[b]||c};_.wf=function(){var a;if((a=Object.create)&&_.uf.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.xf=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.Af=function(a,b){a=a||{};for(var c in a)_.xf(a,c)&&(b[c]=a[c])};_.Bf=_.vf(_.rf,"gapi",{});_.Cf=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.Df=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Ef=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Ff=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Hf=function(a,b,c){_.Gf(a,b,c,"add","at")};_.Gf=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.If={};_.If=_.vf(_.rf,"___jsl",_.wf());_.vf(_.If,"I",0);_.vf(_.If,"hel",10);var Jf,Kf,Lf,Mf,Pf,Nf,Of,Qf,Rf;Jf=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Kf=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Lf=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Mf=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Lf(a[d])&&!Lf(b[d])?Mf(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Lf(b[d])?[]:{},Mf(a[d],b[d])):a[d]=b[d])};
Pf=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Jf("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Nf())if(e=Of(c),d.push(25),typeof e===
"object")return e;return{}}};Nf=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Of=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Qf=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Mf(c,b);a.push(c)};
Rf=function(a){Kf(!0);var b=window.___gcfg,c=Jf("cu"),d=window.___gu;b&&b!==d&&(Qf(c,b),window.___gu=b);b=Jf("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Jf("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=Pf(f,h))&&b.push(f));a&&Qf(c,a);d=Jf("cd");a=0;for(b=d.length;a<b;++a)Mf(Kf(),d[a],!0);d=Jf("ci");a=0;for(b=d.length;a<b;++a)Mf(Kf(),d[a],!0);a=0;for(b=c.length;a<b;++a)Mf(Kf(),c[a],!0)};_.Sf=function(a,b){var c=Kf();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.Tf=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;Rf(c)};var Uf=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.vf(_.If,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};Uf&&Uf();Rf();_.r("gapi.config.get",_.Sf);_.r("gapi.config.update",_.Tf);
var Vf,Wf,Xf,Yf,Zf,$f,bg,fg,gg,hg,ig,cg,dg;Vf=function(a,b){var c=b.createRange();c.selectNode(b.body);a=_.Vb(a);return c.createContextualFragment(_.wc(a))};Wf=function(a){a=a.nodeName;return typeof a==="string"?a:"FORM"};Xf=function(a){a=a.nodeType;return a===1||typeof a!=="number"};Yf=function(a,b,c){a.setAttribute(b,c)};Zf=function(a,b){var c=new XMLHttpRequest;c.open("POST",a);c.setRequestHeader("Content-Type","application/json");c.send(b)};
$f=function(a,b){(typeof window!=="undefined"&&window.navigator&&window.navigator.sendBeacon!==void 0?navigator.sendBeacon.bind(navigator):Zf)("https://csp.withgoogle.com/csp/lcreport/"+a.WJ,JSON.stringify({host:window.location.hostname,type:b,additionalData:void 0}))};bg=function(a,b){try{_.ag(_.Vd,a)}catch(c){return $f(b,"H_SLSANITIZE"),!0}try{_.ag(_.Ud,a)}catch(c){return $f(b,"H_RSANITIZE"),!0}try{_.ag(_.Td,a)}catch(c){return $f(b,"H_SANITIZE"),!0}return!1};
_.eg=function(a,b){a=_.qf(a);var c;if(c=b){var d,e;c=Math.random()<((e=(d=b.wra)!=null?d:cg[b.WJ[0]])!=null?e:0)}if(c&&window.SAFEVALUES_REPORTING!==!1&&"DocumentFragment"in window){var f,h;Math.random()<((h=(f=b.oqa)!=null?f:dg[b.WJ[0]])!=null?h:0)&&$f(b,"HEARTBEAT");bg(a,b)||_.Wb(a).toString()!==a&&$f(b,"H_ESCAPE")}return _.Vb(a)};fg=["data:","http:","https:","mailto:","ftp:"];
gg=function(a,b,c){c=a.TQ.get(c);return(c==null?0:c.has(b))?c.get(b):a.N5.has(b)?{Bd:1}:(c=a.A9.get(b))?c:a.pT&&[].concat(_.zc(a.pT)).some(function(d){return b.indexOf(d)===0})?{Bd:1}:{Bd:0}};
hg=function(a,b,c){var d=Wf(b);c=c.createElement(d);b=b.attributes;for(var e=_.sa(b),f=e.next();!f.done;f=e.next()){var h=f.value;f=h.name;h=h.value;var k=gg(a.MY,f,d),l;a:{if(l=k.conditions){l=_.sa(l);for(var m=l.next();!m.done;m=l.next()){var n=_.sa(m.value);m=n.next().value;n=n.next().value;var p=void 0;if((m=(p=b.getNamedItem(m))==null?void 0:p.value)&&!n.has(m)){l=!1;break a}}}l=!0}if(l)switch(k.Bd){case 1:Yf(c,f,h);break;case 2:a:if(k=void 0,_.sd){try{k=new URL(h)}catch(q){k="https:";break a}k=
k.protocol}else b:{k=document.createElement("a");try{k.href=h}catch(q){k=void 0;break b}k=k.protocol;k=k===":"||k===""?"https:":k}Yf(c,f,k!==void 0&&fg.indexOf(k.toLowerCase())!==-1?h:"about:invalid#zClosurez");break;case 3:Yf(c,f,h.toLowerCase());break;case 4:Yf(c,f,h);break;case 5:Yf(c,f,h);break;case 6:Yf(c,f,h)}}return c};
ig=function(a,b,c){b=Vf(b,c);b=document.createTreeWalker(b,5,function(k){if(k.nodeType===3)k=1;else if(Xf(k))if(k=Wf(k),k===null)k=2;else{var l=a.MY;k=k!=="FORM"&&(l.M5.has(k)||l.TQ.has(k))?1:2}else k=2;return k});for(var d=b.nextNode(),e=c.createDocumentFragment(),f=e;d!==null;){var h=void 0;if(d.nodeType===3)h=a.createTextNode(d.data);else if(Xf(d))h=hg(a,d,c);else throw Error("m");f.appendChild(h);if(d=b.firstChild())f=h;else for(;!(d=b.nextSibling())&&(d=b.parentNode());)f=f.parentNode}return e};
_.ag=function(a,b){var c=document.implementation.createHTMLDocument("");a=ig(a,b,c);c=c.body;c.appendChild(a);c=(new XMLSerializer).serializeToString(c);c=c.slice(c.indexOf(">")+1,c.lastIndexOf("</"));return _.Vb(c)};cg={0:1,1:1};dg={0:.1,1:.1};
_.Oh=window.googleapis&&window.googleapis.server||{};
_.jg=_.jg||{};
_.jg=_.jg||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.jg.kh=a;a()})();_.r("gadgets.util.getUrlParameters",_.jg.kh);
_.ng=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Tf(a());return{register:function(b,c,d){d&&d(_.Sf())},get:function(b){return _.Sf(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Tf(b)},zd:function(){}}}();_.r("gadgets.config.register",_.ng.register);_.r("gadgets.config.get",_.ng.get);_.r("gadgets.config.init",_.ng.zd);_.r("gadgets.config.update",_.ng.update);
var og,pg,qg,rg,sg,tg,ug,vg,wg,xg,yg,zg,Bg,Cg,Dg,Eg,Fg,Gg,Hg,Ig,Jg,Kg,Lg,Mg,Ng,Og,Pg,Qg,Rg,Sg,Tg,Wg,Xg;qg=void 0;rg=function(a){try{return _.Sa.JSON.parse.call(_.Sa.JSON,a)}catch(b){return!1}};sg=function(a){return Object.prototype.toString.call(a)};tg=sg(0);ug=sg(new Date(0));vg=sg(!0);wg=sg("");xg=sg({});yg=sg([]);
zg=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=sg(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==yg||a.constructor!==Array&&a.constructor!==Object)&&(e!==xg||a.constructor!==Array&&a.constructor!==Object)&&e!==wg&&e!==tg&&e!==vg&&e!==ug))return zg(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===tg)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===vg)b[b.length]=String(!!Number(a));else{if(e===ug)return zg(a.toISOString.call(a),c);if(e===yg&&sg(a.length)===tg){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=zg(a[f],c)||"null";b[b.length]="]"}else if(e==wg&&sg(a.length)===tg){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=zg(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=zg(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};Bg=/[\0-\x07\x0b\x0e-\x1f]/;
Cg=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;Dg=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;Eg=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;Fg=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;Gg=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Hg=/[ \t\n\r]+/g;Ig=/[^"]:/;Jg=/""/g;Kg=/true|false|null/g;Lg=/00/;Mg=/[\{]([^0\}]|0[^:])/;Ng=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Og=/[^\[,:][\[\{]/;Pg=/^(\{|\}|\[|\]|,|:|0)+/;Qg=/\u2028/g;
Rg=/\u2029/g;
Sg=function(a){a=String(a);if(Bg.test(a)||Cg.test(a)||Dg.test(a)||Eg.test(a))return!1;var b=a.replace(Fg,'""');b=b.replace(Gg,"0");b=b.replace(Hg,"");if(Ig.test(b))return!1;b=b.replace(Jg,"0");b=b.replace(Kg,"0");if(Lg.test(b)||Mg.test(b)||Ng.test(b)||Og.test(b)||!b||(b=b.replace(Pg,"")))return!1;a=a.replace(Qg,"\\u2028").replace(Rg,"\\u2029");b=void 0;try{b=qg?[rg(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Tg=function(){var a=((_.Sa.document||{}).scripts||[]).length;if((og===void 0||qg===void 0||pg!==a)&&pg!==-1){og=qg=!1;pg=-1;try{try{qg=!!_.Sa.JSON&&_.Sa.JSON.stringify.call(_.Sa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&rg("true")===!0&&rg('[{"a":3}]')[0].a===3}catch(b){}og=qg&&!rg("[00]")&&!rg('"\u0007"')&&!rg('"\\0"')&&!rg('"\\v"')}finally{pg=a}}};_.Ug=function(a){if(pg===-1)return!1;Tg();return(og?rg:Sg)(a)};
_.Vg=function(a){if(pg!==-1)return Tg(),qg?_.Sa.JSON.stringify.call(_.Sa.JSON,a):zg(a)};Wg=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Xg=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Wg?Xg:Date.prototype.toISOString;
_.r("gadgets.json.stringify",_.Vg);_.r("gadgets.json.parse",_.Ug);
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.kg=function(e){a(2,e)};_.lg=function(e){a(3,e)};_.mg=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.jg=_.jg||{};(function(){var a=[];_.jg.hra=function(b){a.push(b)};_.jg.ura=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
var Yg=function(){this.Tg=window.console};Yg.prototype.log=function(a){this.Tg&&this.Tg.log&&this.Tg.log(a)};Yg.prototype.error=function(a){this.Tg&&(this.Tg.error?this.Tg.error(a):this.Tg.log&&this.Tg.log(a))};Yg.prototype.warn=function(a){this.Tg&&(this.Tg.warn?this.Tg.warn(a):this.Tg.log&&this.Tg.log(a))};Yg.prototype.debug=function(){};_.Zg=new Yg;
_.$g=function(){var a=_.sf.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.ah=function(a){if(_.$g())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.rf.addEventListener?(_.rf.addEventListener("load",c,!1),_.rf.addEventListener("DOMContentLoaded",c,!1)):_.rf.attachEvent&&(_.rf.attachEvent("onreadystatechange",function(){_.$g()&&c.apply(this,arguments)}),_.rf.attachEvent("onload",c))}};
_.bh=function(a,b){var c=_.vf(_.If,"watt",_.wf());_.vf(c,a,b)};_.Cf(_.rf.location.href,"rpctoken")&&_.Hf(_.sf,"unload",function(){});var ch=ch||{};ch.IY=null;ch.yW=null;ch.nA=null;ch.frameElement=null;ch=ch||{};
ch.tN||(ch.tN=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Ug(f.data);if(h&&h.f){_.mg();var k=_.dh.Do(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.lg("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{AS:function(){return"wpm"},Zaa:function(){return!0},zd:function(f,h){_.ng.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Nb:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.dh.Do(f),m=_.dh.mO(f);l?window.setTimeout(function(){var n=_.Vg(k);_.mg();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.lg("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.dh!="undefined"&&_.dh||(_.dh=window.gadgets.rpc,_.dh.config=_.dh.config,_.dh.register=_.dh.register,_.dh.unregister=_.dh.unregister,_.dh.nY=_.dh.registerDefault,_.dh.v0=_.dh.unregisterDefault,_.dh.hS=_.dh.forceParentVerifiable,_.dh.call=_.dh.call,_.dh.Hu=_.dh.getRelayUrl,_.dh.Yj=_.dh.setRelayUrl,_.dh.zC=_.dh.setAuthToken,_.dh.xw=_.dh.setupReceiver,_.dh.ko=_.dh.getAuthToken,_.dh.VJ=_.dh.removeReceiver,_.dh.ZS=_.dh.getRelayChannel,_.dh.jY=_.dh.receive,
_.dh.kY=_.dh.receiveSameDomain,_.dh.getOrigin=_.dh.getOrigin,_.dh.Do=_.dh.getTargetOrigin,_.dh.mO=_.dh._getTargetWin,_.dh.w5=_.dh._parseSiblingId);else{_.dh=function(){function a(B,ea){if(!W[B]){var T=cb;ea||(T=Ya);W[B]=T;ea=J[B]||[];for(var ua=0;ua<ea.length;++ua){var za=ea[ua];za.t=C[B];T.call(B,za.f,za)}J[B]=[]}}function b(){function B(){Eb=!0}Fb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",B,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
B),Fb=!0)}function c(B,ea,T,ua,za){C[ea]&&C[ea]===T||(_.lg("Invalid gadgets.rpc token. "+C[ea]+" vs "+T),ob(ea,2));za.onunload=function(){P[ea]&&!Eb&&(ob(ea,1),_.dh.VJ(ea))};b();ua=_.Ug(decodeURIComponent(ua))}function d(B,ea){if(B&&typeof B.s==="string"&&typeof B.f==="string"&&B.a instanceof Array)if(C[B.f]&&C[B.f]!==B.t&&(_.lg("Invalid gadgets.rpc token. "+C[B.f]+" vs "+B.t),ob(B.f,2)),B.s==="__ack")window.setTimeout(function(){a(B.f,!0)},0);else{B.c&&(B.callback=function(Y){_.dh.call(B.f,(B.g?
"legacy__":"")+"__cb",null,B.c,Y)});if(ea){var T=e(ea);B.origin=ea;var ua=B.r;try{var za=e(ua)}catch(Y){}ua&&za==T||(ua=ea);B.referer=ua}ea=(w[B.s]||w[""]).apply(B,B.a);B.c&&typeof ea!=="undefined"&&_.dh.call(B.f,"__cb",null,B.c,ea)}}function e(B){if(!B)return"";B=B.split("#")[0].split("?")[0];B=B.toLowerCase();B.indexOf("//")==0&&(B=window.location.protocol+B);B.indexOf("://")==-1&&(B=window.location.protocol+"//"+B);var ea=B.substring(B.indexOf("://")+3),T=ea.indexOf("/");T!=-1&&(ea=ea.substring(0,
T));B=B.substring(0,B.indexOf("://"));if(B!=="http"&&B!=="https"&&B!=="chrome-extension"&&B!=="file"&&B!=="android-app"&&B!=="chrome-search"&&B!=="chrome-untrusted"&&B!=="chrome"&&B!=="devtools")throw Error("t");T="";var ua=ea.indexOf(":");if(ua!=-1){var za=ea.substring(ua+1);ea=ea.substring(0,ua);if(B==="http"&&za!=="80"||B==="https"&&za!=="443")T=":"+za}return B+"://"+ea+T}function f(B){if(B.charAt(0)=="/"){var ea=B.indexOf("|"),T=ea>0?B.substring(1,ea):B.substring(1);B=ea>0?B.substring(ea+1):null;
return{id:T,origin:B}}return null}function h(B){if(typeof B==="undefined"||B==="..")return window.parent;var ea=f(B);if(ea)return k(window.top.frames[ea.id]);B=String(B);return(ea=window.frames[B])?k(ea):(ea=document.getElementById(B))&&ea.contentWindow?ea.contentWindow:null}function k(B){return B?"postMessage"in B?B:B instanceof HTMLIFrameElement&&"contentWindow"in B&&B.contentWindow!=null&&"postMessage"in B.contentWindow?B.contentWindow:null:null}function l(B,ea){if(P[B]!==!0){typeof P[B]==="undefined"&&
(P[B]=0);var T=h(B);B!==".."&&T==null||cb.Nb(B,ea)!==!0?P[B]!==!0&&P[B]++<10?window.setTimeout(function(){l(B,ea)},500):(W[B]=Ya,P[B]=!0):P[B]=!0}}function m(B){(B=y[B])&&B.substring(0,1)==="/"&&(B=B.substring(1,2)==="/"?document.location.protocol+B:document.location.protocol+"//"+document.location.host+B);return B}function n(B,ea,T){ea&&!/http(s)?:\/\/.+/.test(ea)&&(ea.indexOf("//")==0?ea=window.location.protocol+ea:ea.charAt(0)=="/"?ea=window.location.protocol+"//"+window.location.host+ea:ea.indexOf("://")==
-1&&(ea=window.location.protocol+"//"+ea));y[B]=ea;typeof T!=="undefined"&&(D[B]=!!T)}function p(B,ea){ea=ea||"";C[B]=String(ea);l(B,ea)}function q(B){B=(B.passReferrer||"").split(":",2);O=B[0]||"none";ja=B[1]||"origin"}function t(B){String(B.useLegacyProtocol)==="true"&&(cb=ch.nA||Ya,cb.zd(d,a))}function v(B,ea){function T(ua){ua=ua&&ua.rpc||{};q(ua);var za=ua.parentRelayUrl||"";za=e(la.parent||ea)+za;n("..",za,String(ua.useLegacyProtocol)==="true");t(ua);p("..",B)}!la.parent&&ea?T({}):_.ng.register("rpc",
null,T)}function u(B,ea,T){if(B==="..")v(T||la.rpctoken||la.ifpctok||"",ea);else a:{var ua=null;if(B.charAt(0)!="/"){if(!_.jg)break a;ua=document.getElementById(B);if(!ua)throw Error("u`"+B);}ua=ua&&ua.src;ea=ea||e(ua);n(B,ea);ea=_.jg.kh(ua);p(B,T||ea.rpctoken)}}var w={},y={},D={},C={},I=0,L={},P={},la={},W={},J={},O=null,ja=null,pa=window.top!==window.self,Ha=window.name,ob=function(){},ab=window.console,pb=ab&&ab.log&&function(B){ab.log(B)}||function(){},Ya=function(){function B(ea){return function(){pb(ea+
": call ignored")}}return{AS:function(){return"noop"},Zaa:function(){return!0},zd:B("init"),Nb:B("setup"),call:B("call")}}();_.jg&&(la=_.jg.kh());var Eb=!1,Fb=!1,cb=function(){if(la.rpctx=="rmr")return ch.IY;var B=typeof window.postMessage==="function"?ch.tN:typeof window.postMessage==="object"?ch.tN:window.ActiveXObject?ch.yW?ch.yW:ch.nA:navigator.userAgent.indexOf("WebKit")>0?ch.IY:navigator.product==="Gecko"?ch.frameElement:ch.nA;B||(B=Ya);return B}();w[""]=function(){pb("Unknown RPC service: "+
this.s)};w.__cb=function(B,ea){var T=L[B];T&&(delete L[B],T.call(this,ea))};return{config:function(B){typeof B.WY==="function"&&(ob=B.WY)},register:function(B,ea){if(B==="__cb"||B==="__ack")throw Error("v");if(B==="")throw Error("w");w[B]=ea},unregister:function(B){if(B==="__cb"||B==="__ack")throw Error("x");if(B==="")throw Error("y");delete w[B]},nY:function(B){w[""]=B},v0:function(){delete w[""]},hS:function(){},call:function(B,ea,T,ua){B=B||"..";var za="..";B===".."?za=Ha:B.charAt(0)=="/"&&(za=
e(window.location.href),za="/"+Ha+(za?"|"+za:""));++I;T&&(L[I]=T);var Y={s:ea,f:za,c:T?I:0,a:Array.prototype.slice.call(arguments,3),t:C[B],l:!!D[B]};a:if(O==="bidir"||O==="c2p"&&B===".."||O==="p2c"&&B!==".."){var da=window.location.href;var Va="?";if(ja==="query")Va="#";else if(ja==="hash")break a;Va=da.lastIndexOf(Va);Va=Va===-1?da.length:Va;da=da.substring(0,Va)}else da=null;da&&(Y.r=da);if(B===".."||f(B)!=null||document.getElementById(B))(da=W[B])||f(B)===null||(da=cb),ea.indexOf("legacy__")===
0&&(da=cb,Y.s=ea.substring(8),Y.c=Y.c?Y.c:I),Y.g=!0,Y.r=za,da?(D[B]&&(da=ch.nA),da.call(B,za,Y)===!1&&(W[B]=Ya,cb.call(B,za,Y))):J[B]?J[B].push(Y):J[B]=[Y]},Hu:m,Yj:n,zC:p,xw:u,ko:function(B){return C[B]},VJ:function(B){delete y[B];delete D[B];delete C[B];delete P[B];delete W[B]},ZS:function(){return cb.AS()},jY:function(B,ea){B.length>4?cb.Goa(B,d):c.apply(null,B.concat(ea))},kY:function(B){B.a=Array.prototype.slice.call(B.a);window.setTimeout(function(){d(B)},0)},getOrigin:e,Do:function(B){var ea=
null,T=m(B);T?ea=T:(T=f(B))?ea=T.origin:B==".."?ea=la.parent:(B=document.getElementById(B))&&B.tagName.toLowerCase()==="iframe"&&(ea=B.src);return e(ea)},zd:function(){cb.zd(d,a)===!1&&(cb=Ya);pa?u(".."):_.ng.register("rpc",null,function(B){B=B.rpc||{};q(B);t(B)})},mO:h,w5:f,Sga:"__ack",Rla:Ha||"..",bma:0,ama:1,Zla:2}}();_.dh.zd()};_.dh.config({WY:function(a){throw Error("z`"+a);}});_.r("gadgets.rpc.config",_.dh.config);_.r("gadgets.rpc.register",_.dh.register);_.r("gadgets.rpc.unregister",_.dh.unregister);_.r("gadgets.rpc.registerDefault",_.dh.nY);_.r("gadgets.rpc.unregisterDefault",_.dh.v0);_.r("gadgets.rpc.forceParentVerifiable",_.dh.hS);_.r("gadgets.rpc.call",_.dh.call);_.r("gadgets.rpc.getRelayUrl",_.dh.Hu);_.r("gadgets.rpc.setRelayUrl",_.dh.Yj);_.r("gadgets.rpc.setAuthToken",_.dh.zC);_.r("gadgets.rpc.setupReceiver",_.dh.xw);_.r("gadgets.rpc.getAuthToken",_.dh.ko);
_.r("gadgets.rpc.removeReceiver",_.dh.VJ);_.r("gadgets.rpc.getRelayChannel",_.dh.ZS);_.r("gadgets.rpc.receive",_.dh.jY);_.r("gadgets.rpc.receiveSameDomain",_.dh.kY);_.r("gadgets.rpc.getOrigin",_.dh.getOrigin);_.r("gadgets.rpc.getTargetOrigin",_.dh.Do);
var $h={dha:"Authorization",E1:"Content-ID",Bha:"Content-Transfer-Encoding",Cha:"Content-Type",iia:"Date",Zka:"OriginToken",xja:"hotrod-board-name",yja:"hotrod-chrome-cpu-model",zja:"hotrod-chrome-processors",Lna:"WWW-Authenticate",Nna:"X-Ad-Manager-Impersonation",Mna:"X-Ad-Manager-Debug-Info",Ona:"X-ClientDetails",Pna:"X-Compass-Routing-Destination",Sna:"X-Goog-AuthUser",Wna:"X-Goog-Encode-Response-If-Executable",Qna:"X-Google-Consent",Rna:"X-Google-EOM",Yna:"X-Goog-Meeting-ABR",Zna:"X-Goog-Meeting-Botguardid",
aoa:"X-Goog-Meeting-ClientInfo",boa:"X-Goog-Meeting-ClientVersion",coa:"X-Goog-Meeting-Debugid",doa:"X-Goog-Meeting-Identifier",eoa:"X-Goog-Meeting-Interop-Cohorts",foa:"X-Goog-Meeting-Interop-Type",goa:"X-Goog-Meeting-OidcIdToken",hoa:"X-Goog-Meeting-RtcClient",ioa:"X-Goog-Meeting-StartSource",joa:"X-Goog-Meeting-Token",koa:"X-Goog-Meeting-Viewer-Token",loa:"X-Goog-PageId",noa:"X-Goog-Safety-Content-Type",ooa:"X-Goog-Safety-Encoding",Una:"X-Goog-Drive-Client-Version",Vna:"X-Goog-Drive-Resource-Keys",
poa:"X-HTTP-Method-Override",qoa:"X-JavaScript-User-Agent",roa:"X-Origin",soa:"X-Referer",toa:"X-Requested-With",woa:"X-Use-HTTP-Status-Code-Override",uoa:"X-Server-Timeout",Xna:"X-Goog-First-Party-Reauth",voa:"X-Server-Token",Tna:"x-goog-chat-space-id",moa:"x-goog-pan-request-context"},ai="Accept Accept-Language Authorization Cache-Control cast-device-capabilities Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date developer-token EES-S7E-MODE financial-institution-id GData-Version google-cloud-resource-prefix hotrod-board-name hotrod-chrome-cpu-model hotrod-chrome-processors Host If-Match If-Modified-Since If-None-Match If-Unmodified-Since linked-customer-id login-customer-id MIME-Version Origin OriginToken Pragma Range request-id Slug Transfer-Encoding Want-Digest X-Ad-Manager-Impersonation X-Ad-Manager-Debug-Info x-alkali-account-key x-alkali-application-key x-alkali-auth-apps-namespace x-alkali-auth-entities-namespace x-alkali-auth-entity x-alkali-client-locale x-chrome-connected x-framework-xsrf-token X-Client-Data X-Client-Pctx X-ClientDetails X-Client-Version x-debug-settings-metadata X-Firebase-Locale X-GData-Client X-GData-Key X-Goog-AuthUser X-Goog-PageId X-Goog-Encode-Response-If-Executable X-GoogApps-Allowed-Domains X-Goog-AdX-Buyer-Impersonation X-Goog-Api-Client X-Goog-Api-Key X-Google-EOM X-Goog-Visibilities X-Goog-Correlation-Id X-Goog-Request-Info X-Goog-Request-Reason X-Goog-Request-Time X-Goog-Experiments x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin X-Goog-Firebase-Installations-Auth x-goog-greenenergyuserappservice-metadata X-Firebase-Client X-Firebase-Client-Log-Type X-Firebase-GMPID X-Firebase-Auth-Token X-Firebase-AppCheck X-Firebase-Token X-Goog-Drive-Client-Version X-Goog-Drive-Resource-Keys x-goog-iam-authority-selector x-goog-iam-authorization-token x-goog-request-params x-goog-sherlog-context X-Goog-Sn-Metadata X-Goog-Sn-PatientId X-Goog-Spatula X-Goog-Travel-Bgr X-Goog-Travel-Settings X-Goog-Upload-Command X-Goog-Upload-Content-Disposition X-Goog-Upload-Content-Length X-Goog-Upload-Content-Type X-Goog-Upload-File-Name X-Goog-Upload-Header-Content-Encoding X-Goog-Upload-Header-Content-Length X-Goog-Upload-Header-Content-Type X-Goog-Upload-Header-Transfer-Encoding X-Goog-Upload-Offset X-Goog-Upload-Protocol X-Goog-User-Project X-Goog-Visitor-Id X-Goog-FieldMask X-Google-Project-Override x-goog-maps-api-salt x-goog-maps-api-signature x-goog-maps-client-id x-goog-maps-channel-id x-goog-maps-solution-id x-goog-spanner-database-role X-HTTP-Method-Override X-JavaScript-User-Agent X-Pan-Versionid X-Proxied-User-IP X-Origin X-Referer X-Requested-With X-Stadia-Client-Context X-Upload-Content-Length X-Upload-Content-Type X-Use-Alt-Service X-Use-HTTP-Status-Code-Override X-Ios-Bundle-Identifier X-Android-Package X-Android-Cert X-Goog-Maps-Ios-Uuid X-Goog-Maps-Android-Uuid X-Ariane-Xsrf-Token X-Earth-Engine-App-ID-Token X-Earth-Engine-Computation-Profile X-Earth-Engine-Computation-Profiling X-Play-Console-Experiments-Override X-Play-Console-Session-Id X-YouTube-Bootstrap-Logged-In X-YouTube-VVT X-YouTube-Page-CL X-YouTube-Page-Timestamp X-Compass-Routing-Destination X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-Interop-Cohorts X-Goog-Meeting-Interop-Type X-Goog-Meeting-OidcIdToken X-Goog-Meeting-RtcClient X-Goog-Meeting-StartSource X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token x-sdm-id-token X-Sfdc-Authorization X-Server-Timeout x-foyer-client-environment X-Goog-First-Party-Reauth X-Server-Token x-rfui-request-context x-goog-chat-space-id x-goog-nest-jwt X-Cloud-Trace-Context traceparent x-goog-pan-request-context".split(" "),
bi="Digest Cache-Control Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date ETag Expires Last-Modified Location Pragma Range Server Transfer-Encoding WWW-Authenticate Vary Unzipped-Content-MD5 X-Correlation-ID X-Debug-Tracking-Id X-Google-Consent X-Google-EOM X-Goog-Generation X-Goog-Metageneration X-Goog-Safety-Content-Type X-Goog-Safety-Encoding X-Google-Trace X-Goog-Upload-Chunk-Granularity X-Goog-Upload-Control-URL X-Goog-Upload-Size-Received X-Goog-Upload-Status X-Goog-Upload-URL X-Goog-Diff-Download-Range X-Goog-Hash X-Goog-Updated-Authorization X-Server-Object-Version X-Guploader-Customer X-Guploader-Upload-Result X-Guploader-Uploadid X-Google-Gfe-Backend-Request-Cost X-Earth-Engine-Computation-Profile X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-RtcClient X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token X-Compass-Routing-Destination".split(" ");var ci,di,ei,fi,hi,ii,ji,ki,li,mi,ni,oi;ci=null;di=null;ei=null;fi=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);e>=65&&e<=90&&(e+=32);f>=65&&f<=90&&(f+=32);if(e!=f)return!1}return!0};
_.gi=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=a.length,e=0;e<d;++e){var f=a.charAt(e),h=a.charCodeAt(e);if(h>=55296&&h<=56319&&e+1<d){var k=a.charAt(e+1),l=a.charCodeAt(e+1);l>=56320&&l<=57343&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++e)}if(!(h>=0&&h<=1114109)||h>=55296&&h<=57343||h>=64976&&h<=65007||(h&65534)==65534)h=65533,f=String.fromCharCode(h);k=!(h>=32&&h<=126)||f==" "||c&&f==":"||f=="\\";!c||f!="/"&&f!="?"||(c=!1);f=="%"&&(e+2>=d?k=!0:(l=16*parseInt(a.charAt(e+
1),16)+parseInt(a.charAt(e+2),16),l>=0&&l<=255?(h=l,f=h==0?"":"%"+(256+l).toString(16).toUpperCase().substr(1),e+=2):k=!0));k&&(f=encodeURIComponent(f),f.length<=1&&(h>=0&&h<=127?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=encodeURIComponent(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=b.length;for(e=0;e<d;++e)f=b[e],h=f.split("%2E").join("."),h=h.split(encodeURIComponent("\uff0e")).join("."),h=="."?e+1==d&&c.push(""):
h==".."?(c.length>0&&c.pop(),e+1==d&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&a.charAt(0)=="/";)a=a.substr(1);return"/"+a};hi={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
ii={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
ji=function(a){if(!_.Gc(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if(typeof d==="string"&&d){var e=d.toLowerCase();fi(d,e)&&(b[e]=d)}}for(var f in $h)Object.prototype.hasOwnProperty.call($h,f)&&(a=$h[f],c=a.toLowerCase(),fi(a,c)&&Object.prototype.hasOwnProperty.call(b,c)&&(b[c]=a));return b};ki=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");li=/[ \t]*(\r?\n[ \t]+)+/g;mi=/^[ \t]+|[ \t]+$/g;
ni=function(a,b){if(!b&&typeof a==="object"&&a&&typeof a.length==="number"){b=a;a="";for(var c=b.length,d=0;d<c;++d){var e=ni(b[d],!0);e&&(a&&(e=a+", "+e),a=e)}}if(typeof a==="string"&&(a=a.replace(li," "),a=a.replace(mi,""),a.replace(ki,"")==""&&a))return a};oi=/^[-0-9A-Za-z!#\$%&'\*\+\.\^_`\|~]+$/g;
_.pi=function(a){if(typeof a!=="string"||!a||!a.match(oi))return null;a=a.toLowerCase();if(ei==null){var b=[],c=_.Sf("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Sf("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(bi);(c=_.Sf("googleapis/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Sf("client/headers/request"))&&
typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(ai);for(var d in $h)Object.prototype.hasOwnProperty.call($h,d)&&b.push($h[d]);ei=ji(b)}return ei!=null&&ei.hasOwnProperty(a)?ei[a]:a};
_.qi=function(a,b){if(!_.pi(a)||!ni(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||ii[a])return null;if(ci==null){b=[];var c=_.Sf("googleapis/headers/request");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Sf("client/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(ai);ci=ji(b)}return ci!=null&&ci.hasOwnProperty(a)?ci[a]:null};
_.ri=function(a,b){if(!_.pi(a)||!ni(b))return null;a=a.toLowerCase();if(hi[a])return null;if(di==null){b=[];var c=_.Sf("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Sf("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(bi);di=ji(b)}return di!=null&&di.hasOwnProperty(a)?a:null};
_.si=function(a,b){if(_.pi(b)&&a!=null&&typeof a==="object"){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&fi(d,b)){var e=ni(a[d]);e&&(c!==void 0&&(e=c+", "+e),c=e)}return c}};_.ti=function(a,b,c,d){var e=_.pi(b);if(e){c&&(c=ni(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&fi(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.ui=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=a.length,e=0;e<d;++e){var f=a[e];if(!f)break;var h=f.indexOf(":");if(!(h<=0)){var k=f.substring(0,h);if(k=_.pi(k)){for(f=f.substring(h+1);e+1<d&&a[e+1].match(/^[ \t]/);)f+="\r\n"+a[e+1],++e;if(f=ni(f))if(k=_.ri(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.si(c,k),h!==void 0&&(f=h+", "+f),_.ti(c,k,f,!0)}}}return c};
_.vi=function(a){_.Sa.setTimeout(function(){throw a;},0)};
_.wi=_.jc();_.xi=_.rc()||_.bc("iPod");_.zi=_.bc("iPad");_.Ai=_.mc();_.Bi=_.kc();_.Ci=_.lc()&&!_.sc();
_.Di=function(a){for(var b in a)return!1;return!0};_.Ei=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Fi,Gi,Ii;Fi={};Gi=null;_.Hi=_.ke||_.le||!_.Ci&&typeof _.Sa.atob=="function";_.Ji=function(a,b){b===void 0&&(b=0);Ii();b=Fi[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Ki=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Gi[m];if(n!=null)return n;if(!_.gd(m))throw Error("E`"+m);}return l}Ii();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Ii=function(){if(!Gi){Gi={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Fi[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Gi[f]===void 0&&(Gi[f]=e)}}}};
_.Li=function(a){return a==null?"":String(a)};_.Mi=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");_.Ni=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};
_.Oi=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Oi(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Pi=function(a){var b=[],c;for(c in a)_.Oi(c,a[c],b);return b.join("&")};_.Qi=function(a,b){b=_.Pi(b);return _.Ni(a,b)};
var Ri,Si=function(){try{return new XMLHttpRequest}catch(a){}try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(a){}return null},Ti=function(a){var b=_.gi(a);if(String(a)!=b)throw Error("F");(a=b)&&a.charAt(a.length-1)=="/"||(a=(a||"")+"/");_.dh.register("init",function(){Ti(a)});Ri=a;_.jg.kh(window.location.href)},Ui=function(a,b,c,d){var e={};if(b)for(var f in b)if(Object.prototype.hasOwnProperty.call(b,f)){var h=_.si(b,f),k=_.ri(f,h);k&&h!==void 0&&_.ti(e,k,h,!0)}return{body:a,headers:e,status:typeof c===
"number"?c:void 0,statusText:d||void 0}},Vi=function(a,b){a={error:{code:-1,message:a}};if(b.url=="/rpc"){b=b.body;for(var c=[],d=0;d<b.length;d++){var e=_.Vg(a);e=_.Ug(e);e.id=b[d].id;c.push(e)}a=c}return _.Vg(a)},Wi=function(a,b,c,d){a=a||{};var e=a.headers||{},f=a.httpMethod||"GET",h=String(a.url||""),k=a.urlParams||null,l=a.body||null;c=c||null;d=d||null;h=_.gi(h);h=Ri+String(h||"/").substr(1);h=_.Qi(h,k);var m=[];k=[];for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){m.push(n);var p=
_.si(e,n);p!==void 0&&(n=_.qi(n,p))&&k.push([n,p])}for(;m.length;)delete e[m.pop()];for(;k.length;)n=k.pop(),_.ti(e,n[0],n[1]);_.ti(e,"X-Origin",c||void 0);_.ti(e,"X-Referer",d||void 0);_.ti(e,"X-Goog-Encode-Response-If-Executable","base64");l&&typeof l==="object"&&(l=_.Vg(l));var q=Si();if(!q)throw Error("G");q.open(f,h);q.onreadystatechange=function(){if(q.readyState==4&&q.status!==0){var v=Ui(q.responseText,_.ui(q.getAllResponseHeaders(),!0),q.status,q.statusText);b(v)}};q.onerror=function(){var v=
Vi("A network error occurred, and the request could not be completed.",a);v=Ui(v);b(v)};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(f=e[t],q.setRequestHeader(unescape(encodeURIComponent(t)),unescape(encodeURIComponent(f))));q.send(l?l:null)},Xi=function(a,b,c,d){var e={},f=0;if(a.length==0)b(e);else{var h=function(k){var l=k.key;k=k.params;try{Wi(k,function(n){e[l]={data:n};f++;a.length==f?b(_.Vg(e)):h(a[f])},c,d)}catch(n){var m="";n&&(m+=" [",n.name&&(m+=n.name+": "),m+=n.message||
String(n),m+="]");k=Vi("An error occurred, and the request could not be completed."+m,k);k=Ui(k);e[l]={data:k};f++;a.length==f?b(_.Vg(e)):h(a[f])}};h(a[f])}};_.Oh=_.Oh||{};_.Oh.uda=function(){_.dh.register("makeHttpRequests",function(a){this.f==".."&&this.t==_.dh.ko("..")&&this.origin==_.dh.Do("..")&&Xi.call(this,a,this.callback,this.origin,this.referer)})};
_.Oh.zd=function(){var a=String(window.location.pathname);a.length>=18&&a.substr(a.length-18)=="/static/proxy.html"&&(a=a.substr(0,a.length-18));a||(a="/");_.Oh.GU(a)};_.Oh.GU=function(a){var b=_.gi(a);if(String(a)!=b)throw Error("F");_.Oh.uda();Ti(a);_.dh.call("..","ready:"+_.dh.ko(".."))};_.r("googleapis.ApiServer.makeHttpRequests",Xi);_.r("googleapis.ApiServer.initWithPath",Ti);_.r("googleapis.server.init",_.Oh.zd);_.r("googleapis.server.initWithPath",_.Oh.GU);
});
// Google Inc.
