import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/Models/order_model.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/shared/theme.dart';
import 'package:tbb_web/views/common/responsive.dart';
import 'package:tbb_web/wrapper.dart';

class Account extends StatefulWidget {
  const Account({super.key});

  @override
  State<Account> createState() => _AccountState();
}

class _AccountState extends State<Account> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      return Wrapper(
          body: Column(children: [
        SizedBox(
            height: size.width <= 510 ? 100 : 200,
            width: double.infinity,
            child: Stack(
              fit: StackFit.expand,
              children: [
                Image.asset(
                  "assets/images/common-banner 3.png",
                  fit: BoxFit.fill,
                ),
                Align(
                  child: Text(
                    "MY ACCOUNT",
                    style: GoogleFonts.crimsonPro(
                        fontSize: size.width <= 510 ? 24 : 34,
                        fontWeight: FontWeight.bold),
                  ),
                )
              ],
            )),
        FutureBuilder(
            future: FBFireStore.order
                .where('uId', isEqualTo: pctrl.userDetails?.docId ?? "")
                .get()
                .then((value) =>
                    value.docs.map((e) => OrderModel.fromSnapshot(e)).toList()),
            builder: (context, snapshot) {
              if (snapshot.hasError) {
                return const Text("Facing some issue");
              }
              if (snapshot.hasData) {
                return ResponsiveWid(
                    mobile: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        children: [
                          // SizedBox(
                          //   height: 70,
                          // ),
                          // const Text(
                          //   "My Account",
                          //   style: TextStyle(
                          //       fontSize: 25,
                          //       fontWeight: FontWeight.bold,
                          //       letterSpacing: 1),
                          // ),
                          const SizedBox(height: 40),
                          Column(
                            children: [
                              const Text(
                                "ORDER HISTORY",
                                style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w800,
                                    letterSpacing: 1),
                              ),
                              StaggeredGrid.extent(
                                maxCrossAxisExtent: 300,
                                mainAxisSpacing: 10,
                                crossAxisSpacing: 10,
                                children: [
                                  ...List.generate(
                                    snapshot.data?.length ?? 0,
                                    (index) {
                                      return Card(
                                        color: Colors.white,
                                        child: Padding(
                                          padding: const EdgeInsets.all(20.0),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                  "Order Id: ${snapshot.data?[index].oId}"),
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              Text(
                                                  "Status: ${snapshot.data?[index].status}"),
                                              const SizedBox(
                                                height: 5,
                                              ),

                                              Text(
                                                  "Order Date: ${snapshot.data?[index].createdAt.toString().split(' ').first}"),
                                              const SizedBox(
                                                height: 5,
                                              ),

                                              Text(
                                                  "Address: ${snapshot.data?[index].address}"),

                                              // const Text(""),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  )
                                ],
                              )
                            ],
                          ),
                          const SizedBox(
                            height: 30,
                          ),
                          Column(
                            children: [
                              const Text(
                                "DEFAULT ADDRESS",
                                style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w800,
                                    letterSpacing: 1),
                              ),
                              const SizedBox(height: 5),
                              Text(
                                textAlign: TextAlign.center,
                                "${pctrl.userDetails?.address.first.address.toUpperCase()}, ${pctrl.userDetails?.address.first.area.toUpperCase()}, ${pctrl.userDetails?.address.first.city.toUpperCase()}, ${pctrl.userDetails?.address.first.pincode}",
                                style: const TextStyle(fontSize: 12),
                              ),
                              // const SizedBox(height: 10),
                              // ElevatedButton(
                              //     style: const ButtonStyle(
                              //       padding: WidgetStatePropertyAll(
                              //           EdgeInsets.only(
                              //               top: 3,
                              //               bottom: 3,
                              //               right: 20,
                              //               left: 20)),
                              //       shape: WidgetStatePropertyAll(
                              //           RoundedRectangleBorder()),
                              //       backgroundColor:
                              //           WidgetStatePropertyAll(themeColor),
                              //     ),
                              //     onPressed: () async {
                              //       showDialog(
                              //         context: context,
                              //         builder: (context) {
                              //           return AlertDialog(
                              //             title: const Text('Logout?'),
                              //             actions: <Widget>[
                              //               TextButton(
                              //                 style: TextButton.styleFrom(
                              //                   textStyle: Theme.of(context)
                              //                       .textTheme
                              //                       .labelLarge,
                              //                 ),
                              //                 child: const Text('NO'),
                              //                 onPressed: () {
                              //                   Navigator.of(context).pop();
                              //                 },
                              //               ),
                              //               TextButton(
                              //                 style: TextButton.styleFrom(
                              //                   textStyle: Theme.of(context)
                              //                       .textTheme
                              //                       .labelLarge,
                              //                 ),
                              //                 child: const Text('YES'),
                              //                 onPressed: () {
                              //                   FBAuth.auth.signOut();
                              //                   if (context.mounted) {
                              //                     context.go(Routes.home);
                              //                     Navigator.of(context).pop();
                              //                   }
                              //                 },
                              //               ),
                              //             ],
                              //           );
                              //         },
                              //       );

                              //       // await FBAuth.auth.signOut();
                              //       // if (context.mounted) {
                              //       //   context.go(Routes.auth);
                              //       // }
                              //     },
                              //     child: const Text(
                              //       "LOG OUT",
                              //       style: TextStyle(
                              //         fontSize: 18,
                              //         color: Colors.white,
                              //       ),
                              //     ))
                            ],
                          ),
                          const SizedBox(
                            height: 350,
                          )
                        ],
                      ),
                    ),
                    tablet: Padding(
                      padding: const EdgeInsets.all(30.0),
                      child: Column(
                        children: [
                          // const Text(
                          //   "My Account",
                          //   style: TextStyle(
                          //       fontSize: 35,
                          //       fontWeight: FontWeight.bold,
                          //       letterSpacing: 1),
                          // ),
                          const SizedBox(height: 50),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Column(
                                  children: [
                                    const Text(
                                      "ORDER HISTORY",
                                      style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w800,
                                          letterSpacing: 1),
                                    ),
                                    StaggeredGrid.extent(
                                      maxCrossAxisExtent: 300,
                                      mainAxisSpacing: 10,
                                      crossAxisSpacing: 10,
                                      children: [
                                        ...List.generate(
                                          snapshot.data?.length ?? 0,
                                          (index) {
                                            return Card(
                                              color: Colors.white,
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(20.0),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                        "Order Id: ${snapshot.data?[index].oId}"),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                        "Status: ${snapshot.data?[index].status}"),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),

                                                    Text(
                                                        "Order Date: ${snapshot.data?[index].createdAt.toString().split(' ').first}"),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),

                                                    Text(
                                                        "Address: ${snapshot.data?[index].address}"),

                                                    // const Text(""),
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              Expanded(
                                child: Column(
                                  children: [
                                    const Text(
                                      "DEFAULT ADDRESS",
                                      style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w800,
                                          letterSpacing: 1),
                                    ),
                                    const SizedBox(height: 5),
                                    Text(
                                      "${pctrl.userDetails?.address.first.address.toUpperCase()}, ${pctrl.userDetails?.address.first.area.toUpperCase()}, ${pctrl.userDetails?.address.first.city.toUpperCase()}, ${pctrl.userDetails?.address.first.pincode}",
                                      style: const TextStyle(fontSize: 16),
                                    ),
                                    const SizedBox(height: 5),
                                    ElevatedButton(
                                        style: const ButtonStyle(
                                          padding: WidgetStatePropertyAll(
                                              EdgeInsets.only(
                                                  top: 10,
                                                  bottom: 10,
                                                  right: 80,
                                                  left: 80)),
                                          shape: WidgetStatePropertyAll(
                                              RoundedRectangleBorder()),
                                          backgroundColor:
                                              WidgetStatePropertyAll(
                                                  themeColor),
                                        ),
                                        onPressed: () async {
                                          showDialog(
                                            context: context,
                                            builder: (context) {
                                              return AlertDialog(
                                                title: const Text('Logout?'),
                                                actions: <Widget>[
                                                  TextButton(
                                                    style: TextButton.styleFrom(
                                                      textStyle:
                                                          Theme.of(context)
                                                              .textTheme
                                                              .labelLarge,
                                                    ),
                                                    child: const Text('NO'),
                                                    onPressed: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                  ),
                                                  TextButton(
                                                    style: TextButton.styleFrom(
                                                      textStyle:
                                                          Theme.of(context)
                                                              .textTheme
                                                              .labelLarge,
                                                    ),
                                                    child: const Text('YES'),
                                                    onPressed: () {
                                                      FBAuth.auth.signOut();
                                                      if (context.mounted) {
                                                        context.go(Routes.home);
                                                        Navigator.of(context)
                                                            .pop();
                                                      }
                                                    },
                                                  ),
                                                ],
                                              );
                                            },
                                          );

                                          // await FBAuth.auth.signOut();
                                          // if (context.mounted) {
                                          //   context.go(Routes.auth);
                                          // }
                                        },
                                        child: const Text(
                                          "LOG OUT",
                                          style: TextStyle(
                                            fontSize: 18,
                                            color: Colors.white,
                                          ),
                                        ))
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 300,
                          )
                        ],
                      ),
                    ),
                    desktop: Padding(
                      padding: const EdgeInsets.all(50.0),
                      child: Column(
                        children: [
                          // const Text(
                          //   "MY ACCOUNT",
                          //   style: TextStyle(
                          //       fontSize: 35,
                          //       fontWeight: FontWeight.bold,
                          //       letterSpacing: 1),
                          // ),
                          const SizedBox(height: 50),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Column(
                                  children: [
                                    const Text(
                                      "ORDER HISTORY",
                                      style: TextStyle(
                                          fontSize: 30,
                                          fontWeight: FontWeight.w800,
                                          letterSpacing: 1),
                                    ),
                                    StaggeredGrid.extent(
                                      maxCrossAxisExtent: 300,
                                      mainAxisSpacing: 10,
                                      crossAxisSpacing: 10,
                                      children: [
                                        ...List.generate(
                                          snapshot.data?.length ?? 0,
                                          (index) {
                                            return Card(
                                              color: Colors.white,
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(20.0),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                        "Order Id: ${snapshot.data?[index].oId}"),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                        "Status: ${snapshot.data?[index].status}"),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),

                                                    Text(
                                                        "Order Date: ${snapshot.data?[index].createdAt.toString().split(' ').first}"),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),

                                                    Text(
                                                        "Address: ${snapshot.data?[index].address}"),

                                                    // const Text(""),
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              Expanded(
                                child: Column(
                                  children: [
                                    const Text(
                                      "DEFAULT ADDRESS",
                                      style: TextStyle(
                                          fontSize: 30,
                                          fontWeight: FontWeight.w800,
                                          letterSpacing: 1),
                                    ),
                                    const SizedBox(height: 10),
                                    Text(
                                      "${pctrl.userDetails?.address.first.address.toUpperCase()}, ${pctrl.userDetails?.address.first.area.toUpperCase()}, ${pctrl.userDetails?.address.first.city.toUpperCase()}, ${pctrl.userDetails?.address.first.pincode}",
                                      style: const TextStyle(fontSize: 16),
                                    ),
                                    const SizedBox(height: 20),
                                    ElevatedButton(
                                        style: const ButtonStyle(
                                          padding: WidgetStatePropertyAll(
                                              EdgeInsets.only(
                                                  top: 10,
                                                  bottom: 10,
                                                  right: 80,
                                                  left: 80)),
                                          shape: WidgetStatePropertyAll(
                                              RoundedRectangleBorder()),
                                          backgroundColor:
                                              WidgetStatePropertyAll(
                                                  themeColor),
                                        ),
                                        onPressed: () async {
                                          showDialog(
                                            context: context,
                                            builder: (context) {
                                              return AlertDialog(
                                                title: const Text('Logout?'),
                                                actions: <Widget>[
                                                  TextButton(
                                                    style: TextButton.styleFrom(
                                                      textStyle:
                                                          Theme.of(context)
                                                              .textTheme
                                                              .labelLarge,
                                                    ),
                                                    child: const Text('NO'),
                                                    onPressed: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                  ),
                                                  TextButton(
                                                    style: TextButton.styleFrom(
                                                      textStyle:
                                                          Theme.of(context)
                                                              .textTheme
                                                              .labelLarge,
                                                    ),
                                                    child: const Text('YES'),
                                                    onPressed: () {
                                                      FBAuth.auth.signOut();
                                                      if (context.mounted) {
                                                        context.go(Routes.home);
                                                        Navigator.of(context)
                                                            .pop();
                                                      }
                                                    },
                                                  ),
                                                ],
                                              );
                                            },
                                          );

                                          // await FBAuth.auth.signOut();
                                          // if (context.mounted) {
                                          //   context.go(Routes.auth);
                                          // }
                                        },
                                        child: const Text(
                                          "LOG OUT",
                                          style: TextStyle(
                                            fontSize: 18,
                                            color: Colors.white,
                                          ),
                                        ))
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(
                            height: 380,
                          )
                          // Spacer()
                        ],
                      ),
                    ));
              } else {
                return const SizedBox();
              }
            })
      ]));
    });
  }
}
