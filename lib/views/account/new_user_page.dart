import 'package:flutter/material.dart';
import 'package:flutter_animated_button/flutter_animated_button.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/views/registration_page.dart';
import 'package:tbb_web/wrapper.dart';

class NewUserPage extends StatefulWidget {
  const NewUserPage({super.key, required this.phoneemail});
  final String phoneemail;
  @override
  State<NewUserPage> createState() => _NewUserPageState();
}

class _NewUserPageState extends State<NewUserPage> {
  final TextEditingController fnamectrl = TextEditingController();
  final TextEditingController lnamectrl = TextEditingController();
  // final TextEditingController countryctrl = TextEditingController();
  final TextEditingController cityctrl = TextEditingController();
  final TextEditingController statectrl = TextEditingController();
  final TextEditingController pincodectrl = TextEditingController();
  final TextEditingController areactrl = TextEditingController();
  final TextEditingController addressctrl = TextEditingController();
  String? selectedarea;
  List<String>? areaList = [];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // print(widget.phoneemail);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      areaList?.clear();
      areaList = pctrl.settings?.delcharges.map((e) => e.name).toList();
      return Wrapper(
          body: SizedBox(
        height: 800,
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 500),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const CreateAcctitle(),
              Padding(
                padding: const EdgeInsets.only(
                    bottom: 10.0, top: 50, left: 50, right: 50),
                child: Container(
                  // width: MediaQuery.sizeOf(context).width,
                  width: 600,
                  decoration: BoxDecoration(
                      border: Border.all(),
                      borderRadius: const BorderRadius.all(Radius.zero)),
                  child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, right: 2, top: 2, bottom: 2),
                          child: TextField(
                            controller: fnamectrl,
                            decoration: const InputDecoration(
                              focusedBorder: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              labelText: "FIRST NAME",
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    bottom: 10.0, top: 10, left: 50, right: 50),
                child: Container(
                  // width: MediaQuery.sizeOf(context).width,
                  width: 600,
                  decoration: BoxDecoration(
                      border: Border.all(),
                      borderRadius: const BorderRadius.all(Radius.zero)),
                  child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, right: 2, top: 2, bottom: 2),
                          child: TextField(
                            controller: lnamectrl,
                            decoration: const InputDecoration(
                              focusedBorder: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              labelText: "LAST NAME",
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    bottom: 10.0, top: 10, left: 50, right: 50),
                child: Container(
                  // width: MediaQuery.sizeOf(context).width,
                  width: 600,
                  decoration: BoxDecoration(
                      border: Border.all(),
                      borderRadius: const BorderRadius.all(Radius.zero)),
                  child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, right: 2, top: 2, bottom: 2),
                          child: TextField(
                            controller: addressctrl,
                            decoration: const InputDecoration(
                              focusedBorder: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              labelText: "Address",
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              // Padding(
              //   padding: const EdgeInsets.only(
              //       bottom: 10.0, top: 10, left: 50, right: 50),
              //   child: Container(
              //     // width: MediaQuery.sizeOf(context).width,
              //     width: 600,
              //     decoration: BoxDecoration(
              //         border: Border.all(),
              //         borderRadius: const BorderRadius.all(Radius.zero)),
              //     child: const Row(
              //       children: [
              //         // Expanded(
              //         //   child: Padding(
              //         //     padding: const EdgeInsets.only(
              //         //         left: 10.0, right: 2, top: 2, bottom: 2),
              //         //     child: TextField(
              //         //       controller: countryctrl,
              //         //       decoration: const InputDecoration(
              //         //         focusedBorder: InputBorder.none,
              //         //         enabledBorder: InputBorder.none,
              //         //         labelText: "COUNTRY",
              //         //       ),
              //         //     ),
              //         //   ),
              //         // ),
              //       ],
              //     ),
              //   ),
              // ),
              // SizedBox(height: 22),
              Padding(
                padding: const EdgeInsets.only(
                    bottom: 10.0, top: 10, left: 50, right: 50),
                child: Container(
                  width: 600,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(bottom: 4.0),
                        child: Text(
                          'Area',
                          style: TextStyle(
                              fontWeight: FontWeight.w600, fontSize: 14),
                        ),
                      ),
                      DropdownButtonFormField(
                        dropdownColor: Colors.white,
                        decoration: textfieldDecoration().copyWith(
                          contentPadding: EdgeInsets.only(left: 8, right: 2),
                          hintText: 'Area',
                        ),
                        value: selectedarea,
                        items: List.generate(areaList?.length ?? 0, (index) {
                          areaList?.sort((a, b) => a.compareTo(b));
                          String area =
                              areaList?[index].trim().toLowerCase() ?? "";
                          return DropdownMenuItem(
                            value: area,
                            child: Text(area),
                          );
                        }),
                        onChanged: (value) {
                          selectedarea = value?.trim().toLowerCase();
                          setState(() {});
                          // weightrangeController.text = value?.docId ?? "";
                        },
                      ),
                    ],
                  ),
                ),
              ),

              Padding(
                padding: const EdgeInsets.only(
                    bottom: 10.0, top: 10, left: 50, right: 50),
                child: Container(
                  width: 600,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.only(bottom: 4.0),
                        child: Text(
                          'City',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                      DropdownButtonFormField(
                        decoration: textfieldDecoration().copyWith(
                          contentPadding: EdgeInsets.only(left: 8, right: 3),
                          hintText: 'City',
                        ),
                        value: 'VADODARA',
                        items: List.generate(1, (index) {
                          const String city = "VADODARA";
                          return const DropdownMenuItem(
                            value: city,
                            child: Text(city),
                          );
                        }),
                        onChanged: (value) {
                          // selectedcity = value;
                          // weightrangeController.text = value?.docId ?? "";
                        },
                      ),
                    ],
                  ),
                ),
              ),

              Padding(
                padding: const EdgeInsets.only(
                    bottom: 10.0, top: 10, left: 50, right: 50),
                child: Container(
                  width: 600,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.only(bottom: 4.0),
                        child: Text(
                          'State',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                      DropdownButtonFormField(
                        decoration: textfieldDecoration().copyWith(
                          contentPadding: EdgeInsets.only(left: 8, right: 3),
                          hintText: 'State',
                        ),
                        value: 'GUJARAT',
                        items: List.generate(1, (index) {
                          const state = "GUJARAT";
                          return const DropdownMenuItem(
                            value: state,
                            child: Text(state),
                          );
                        }),
                        onChanged: (value) {
                          // selectedstate = value;
                          // weightrangeController.text = value?.docId ?? "";
                        },
                      ),
                    ],
                  ),
                ),
              ),
              // Container(
              //   width: 600,
              //   child: Padding(
              //     padding: const EdgeInsets.only(
              //         bottom: 10.0, top: 10, left: 50, right: 50),
              //     child: Container(
              //       // width: MediaQuery.sizeOf(context).width,
              //       width: 600,
              //       decoration: BoxDecoration(
              //           border: Border.all(),
              //           borderRadius: const BorderRadius.all(Radius.zero)),
              //       child: Row(
              //         children: [
              //           Expanded(
              //             child: Padding(
              //               padding: const EdgeInsets.only(
              //                   left: 10.0, right: 2, top: 2, bottom: 2),
              //               child: TextField(
              //                 controller: statectrl,
              //                 decoration: const InputDecoration(
              //                   focusedBorder: InputBorder.none,
              //                   enabledBorder: InputBorder.none,
              //                   labelText: "STATE",
              //                 ),
              //               ),
              //             ),
              //           )
              //         ],
              //       ),
              //     ),
              //   ),
              // ),
              // Padding(
              //   padding: const EdgeInsets.only(
              //       bottom: 10.0, top: 10, left: 50, right: 50),
              //   child: Container(
              //     // width: MediaQuery.sizeOf(context).width,
              //     width: 600,
              //     decoration: BoxDecoration(
              //         border: Border.all(),
              //         borderRadius: const BorderRadius.all(Radius.zero)),
              //     child: Row(
              //       children: [
              //         Expanded(
              //           child: Padding(
              //             padding: const EdgeInsets.only(
              //                 left: 10.0, right: 2, top: 2, bottom: 2),
              //             child: TextField(
              //               controller: cityctrl,
              //               decoration: const InputDecoration(
              //                 focusedBorder: InputBorder.none,
              //                 enabledBorder: InputBorder.none,
              //                 labelText: "CITY",
              //               ),
              //             ),
              //           ),
              //         )
              //       ],
              //     ),
              //   ),
              // ),
              // Padding(
              //   padding: const EdgeInsets.only(
              //       bottom: 10.0, top: 10, left: 50, right: 50),
              //   child: Container(
              //     // width: MediaQuery.sizeOf(context).width,
              //     width: 600,
              //     decoration: BoxDecoration(
              //         border: Border.all(),
              //         borderRadius: const BorderRadius.all(Radius.zero)),
              //     child: Row(
              //       children: [
              //         Expanded(
              //           child: Padding(
              //             padding: const EdgeInsets.only(
              //                 left: 10.0, right: 2, top: 2, bottom: 2),
              //             child: TextField(
              //               controller: areactrl,
              //               decoration: const InputDecoration(
              //                 focusedBorder: InputBorder.none,
              //                 enabledBorder: InputBorder.none,
              //                 labelText: "AREA",
              //               ),
              //             ),
              //           ),
              //         )
              //       ],
              //     ),
              //   ),
              // ),

              Padding(
                padding: const EdgeInsets.only(
                    bottom: 10.0, top: 20, left: 50, right: 50),
                child: Container(
                  // width: MediaQuery.sizeOf(context).width,
                  width: 600,
                  decoration: BoxDecoration(
                      border: Border.all(),
                      borderRadius: const BorderRadius.all(Radius.zero)),
                  child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, right: 2, top: 2, bottom: 2),
                          child: TextField(
                            controller: pincodectrl,
                            decoration: const InputDecoration(
                              focusedBorder: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              labelText: "PINCODE",
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    bottom: 10.0, top: 10, left: 50, right: 50),
                child: AnimatedButton(
                  borderColor: Colors.black,
                  height: 40,
                  width: 600,
                  text: 'CREATE ACCOUNT',
                  selectedBackgroundColor: Colors.pinkAccent.shade100,
                  isReverse: true,
                  selectedTextColor: Colors.white,
                  transitionType: TransitionType.CENTER_LR_IN,
                  backgroundColor: Colors.white,
                  onPress: () async {
                    if (selectedarea == null ||
                        fnamectrl.text.isEmpty ||
                        lnamectrl.text.isEmpty ||
                        addressctrl.text.isEmpty ||
                        pincodectrl.text.isEmpty) {
                      showAppSnackBar("Enter all the details!!");
                      return;
                    }
                    // if (fnamectrl.text.isEmpty) {
                    //   showAppSnackBar("Enter name");
                    //   return;
                    // }
                    // if (lnamectrl.text.isEmpty) {
                    //   showAppSnackBar("Enter name");
                    //   return;
                    // }
                    // if (addressctrl.text.isEmpty) {
                    //   showAppSnackBar("Enter address");
                    //   return;
                    // }
                    // if (pincodectrl.text.isEmpty) {
                    //   showAppSnackBar("Enter pincode");
                    //   return;
                    // }
                    var cartdata = <String, dynamic>{};
                    final temp = Get.find<ProductCtrl>().orderProduct;
                    for (var e in temp) {
                      final cartitem = <String, dynamic>{
                        e.id: {
                          "pId": e.pId,
                          "name": e.name,
                          "delivery": e.delivery,
                          "selectedflavour": e.selectedflavour,
                          "weightSelected": e.weightSelected,
                          "deliveryTime": e.deliveryTime,
                          "price": e.price,
                          "messageOnCake": e.messageOnCake,
                          // "area": e.area,
                          "delDate": deldatefromat(e.delDate),
                          "isLateNight": e.isLateNight,
                          'qty': e.qty
                        },
                      };
                      cartdata.addAll(cartitem);
                    }

                    // final address = <String, dynamic>{
                    //   "docId": getRandomId(6),
                    //   "name": '${fnamectrl.text} ${lnamectrl.text}',
                    //   "area": areactrl.text,
                    //   "city": cityctrl.text,
                    //   "state": statectrl.text,
                    //   "pincode": pincodectrl.text,
                    //   "phone": widget.phoneemail
                    // };
                    final data = <String, dynamic>{
                      // 'country': countryctrl.text,
                      'fname': fnamectrl.text,
                      'lname': lnamectrl.text,
                      'contact': widget.phoneemail,
                      'address': {
                        getRandomId(6): {
                          "name": '${fnamectrl.text} ${lnamectrl.text}',
                          "area": selectedarea,
                          "address": addressctrl.text,
                          "city": "VADODARA",
                          "state": "GUJARAT",
                          "pincode": pincodectrl.text,
                          "phone": widget.phoneemail
                        }
                      },
                      'cartitems': cartdata,
                    };
                    // print(cartdata);
                    try {
                      await FBFireStore.users
                          .doc(FBAuth.auth.currentUser?.uid)
                          .set(data);
                    } catch (e) {
                      debugPrint(e.toString());
                    }
                    // await FBFireStore.users
                    //     .doc(FBAuth.auth.currentUser?.uid)
                    //     .update({"cartitems": cartdata});
                    Get.find<ProductCtrl>().orderProduct.clear();

                    context.go(Routes.home);
                    // FBAuth.auth.signOut;
                    // if (context.mounted) {
                    //   context.go(Routes.about);
                    // }
                  },
                  // Navigator.push(context,
                  //     MaterialPageRoute(builder: (context) => const RegistrationPage())),
                  animatedOn: AnimatedOn.onHover,
                  textStyle: const TextStyle(fontSize: 15),
                  animationDuration: const Duration(milliseconds: 300),
                ),
              ),
            ],
          ),
        ),
      ));
    });
  }
}
