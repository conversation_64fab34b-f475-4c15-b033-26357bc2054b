import 'package:flutter/material.dart';
import 'package:flutter_animated_button/flutter_animated_button.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/wrapper.dart';

class ContactUs extends StatelessWidget {
  const ContactUs({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Wrapper(
      body: Padding(
        padding: EdgeInsets.only(
            left: 40.0,
            right: 40,
            top: size.width <= 510 ? 80 : 150,
            bottom: 40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 50.0, bottom: 40),
              child: Center(
                child: Text(
                  'Contact Us'.toUpperCase(),
                  style: GoogleFonts.crimsonPro(
                    color: const Color.fromARGB(255, 216, 122, 153),
                    fontSize: size.width <= 510 ? 30 : 45,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            Container(
              constraints: const BoxConstraints(maxWidth: 800),
              child: Text(
                "Legal Name: JAI MATA DI FOODS,\nPhone: +91 9265537579\nAddress: Akota - Mujmahuda Rd, beside Samrajya 1, Opp. Maruti True Value, Pratham Avenue, Akota, Vadodara, Gujarat 390003",
                style: TextStyle(fontSize: size.width <= 510 ? 16 : 20),
                // textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
                style: ButtonStyle(
                    padding: const WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 20, vertical: 10)),
                    shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15))),
                    backgroundColor: const WidgetStatePropertyAll(
                        Color.fromARGB(255, 222, 155, 177))),
                onPressed: () {
                  context.go(Routes.home);
                },
                child: const Text(
                  "Back To Home",
                  style: TextStyle(color: Colors.white),
                )),
            SizedBox(height: size.width <= 510 ? 120 : 160),
          ],
        ),
      ),
    );
  }
}
//     return Wrapper(
//       body: ResponsiveWid(
//         //mobile
//         mobile: Column(
//           children: [
//             const ContactUsBanner(),
//             Padding(
//               padding: const EdgeInsets.only(top: 30.0, bottom: 30),
//               child: Center(
//                 child: Text(
//                   'WE ARE LOCATED HERE',
//                   style: GoogleFonts.crimsonPro(
//                     fontSize: 38,
//                     fontWeight: FontWeight.w500,
//                   ),
//                 ),
//               ),
//             ),
//             const UnderlineBar(),
//             Padding(
//               padding: const EdgeInsets.only(top: 20, bottom: 20),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Expanded(
//                     child: Wrap(
//                       alignment: WrapAlignment.center,
//                       runAlignment: WrapAlignment.start,
//                       crossAxisAlignment: WrapCrossAlignment.center,
//                       children: [
//                         ...List.generate(6, (index) {
//                           return const LocationCard1();
//                         }),
//                       ],
//                     ),
//                   )
//                 ],
//               ),
//             ),
//             Padding(
//               padding: const EdgeInsets.only(
//                   top: 30.0, bottom: 30, left: 20, right: 20),
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Center(
//                     child: Text(
//                       'GET IN TOUCH WITH US',
//                       style: GoogleFonts.crimsonPro(
//                         fontSize: 37,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ),
//                   const UnderlineBar(),
//                   const SizedBox(
//                     height: 20,
//                   ),
//                   const NameTextfield(),
//                   const SizedBox(
//                     height: 10,
//                   ),
//                   const EmailTextfield(),
//                   const SizedBox(
//                     height: 10,
//                   ),
//                   const PhoneTextfield(),
//                   const SizedBox(
//                     height: 10,
//                   ),
//                   const MessageTextfield(),
//                   const SizedBox(
//                     height: 10,
//                   ),
//                   const SendButton(),
//                   const SizedBox(
//                     height: 20,
//                   ),
//                 ],
//               ),
//             )
//           ],
//         ),
//         //tablet
//         tablet: Column(
//           children: [
//             const ContactUsBanner(),
//             Padding(
//               padding: const EdgeInsets.only(top: 30.0, bottom: 30),
//               child: Center(
//                 child: Text(
//                   'WE ARE LOCATED HERE',
//                   style: GoogleFonts.crimsonPro(
//                     fontSize: 38,
//                     fontWeight: FontWeight.w500,
//                   ),
//                 ),
//               ),
//             ),
//             const UnderlineBar(),
//             Padding(
//               padding: const EdgeInsets.only(top: 20, bottom: 20),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Expanded(
//                     child: Wrap(
//                       alignment: WrapAlignment.center,
//                       runAlignment: WrapAlignment.start,
//                       crossAxisAlignment: WrapCrossAlignment.center,
//                       children: [
//                         ...List.generate(6, (index) {
//                           return const LocationCard1();
//                         }),
//                       ],
//                     ),
//                   )
//                 ],
//               ),
//             ),
//             Column(
//               mainAxisAlignment: MainAxisAlignment.spaceAround,
//               children: [
//                 Padding(
//                   padding: const EdgeInsets.only(top: 30.0, bottom: 30),
//                   child: Center(
//                     child: Text(
//                       'GET IN TOUCH WITH US',
//                       style: GoogleFonts.crimsonPro(
//                         fontSize: 38,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ),
//                 ),
//                 const UnderlineBar(),
//                 const SizedBox(
//                   height: 20,
//                 ),
//                 const NameTextfield(),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 const EmailTextfield(),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 const PhoneTextfield(),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 const MessageTextfield(),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 const SendButton(),
//                 const SizedBox(
//                   height: 20,
//                 ),
//               ],
//             )
//           ],
//         ),
//         //desktop

//         desktop: Column(
//           children: [
//             const ContactUsBanner(),
//             Padding(
//               padding: const EdgeInsets.only(top: 30.0, bottom: 30),
//               child: Center(
//                 child: Text(
//                   'WE ARE LOCATED HERE',
//                   style: GoogleFonts.crimsonPro(
//                     fontSize: 38,
//                     fontWeight: FontWeight.w500,
//                   ),
//                 ),
//               ),
//             ),
//             const UnderlineBar(),
//             Padding(
//               padding: const EdgeInsets.only(top: 20, bottom: 20),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Expanded(
//                     child: Wrap(
//                       alignment: WrapAlignment.center,
//                       runAlignment: WrapAlignment.start,
//                       crossAxisAlignment: WrapCrossAlignment.center,
//                       children: [
//                         ...List.generate(3, (index) {
//                           return const LocationCard1();
//                         }),
//                       ],
//                     ),
//                   )
//                 ],
//               ),
//             ),
//             Column(
//               mainAxisAlignment: MainAxisAlignment.spaceAround,
//               children: [
//                 Padding(
//                   padding: const EdgeInsets.only(top: 30.0, bottom: 30),
//                   child: Center(
//                     child: Text(
//                       'GET IN TOUCH WITH US',
//                       style: GoogleFonts.crimsonPro(
//                         fontSize: 38,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ),
//                 ),
//                 const UnderlineBar(),
//                 const SizedBox(
//                   height: 20,
//                 ),
//                 const NameTextfield(),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 const EmailTextfield(),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 const PhoneTextfield(),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 const MessageTextfield(),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 const SendButton(),
//                 const SizedBox(
//                   height: 20,
//                 ),
//               ],
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }

class SendButton extends StatelessWidget {
  const SendButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft,
      width: 600,
      child: AnimatedButton(
        height: 35,
        width: 80,
        text: 'SEND',
        selectedBackgroundColor: Colors.pinkAccent.shade100,
        isReverse: true,
        selectedTextColor: Colors.white,
        transitionType: TransitionType.CENTER_LR_IN,
        backgroundColor: Colors.pink.shade200,
        onPress: () {},
        animatedOn: AnimatedOn.onHover,
        textStyle: const TextStyle(fontSize: 15),
        animationDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}

class MessageTextfield extends StatelessWidget {
  const MessageTextfield({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 600,
      height: 200,
      decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: const BorderRadius.all(Radius.circular(6))),
      child: const Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 10.0, right: 2, top: 2, bottom: 2),
              child: TextField(
                decoration: InputDecoration(
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  labelText: "Message",
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class PhoneTextfield extends StatelessWidget {
  const PhoneTextfield({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 600,
      decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: const BorderRadius.all(Radius.circular(6))),
      child: const Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 10.0, right: 2, top: 2, bottom: 2),
              child: TextField(
                decoration: InputDecoration(
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  labelText: "Phone No.",
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class EmailTextfield extends StatelessWidget {
  const EmailTextfield({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 600,
      decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: const BorderRadius.all(Radius.circular(6))),
      child: const Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 10.0, right: 2, top: 2, bottom: 2),
              child: TextField(
                decoration: InputDecoration(
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  labelText: "E-mail",
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class NameTextfield extends StatelessWidget {
  const NameTextfield({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 600,
      decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: const BorderRadius.all(Radius.circular(6))),
      child: const Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 10.0, right: 2, top: 2, bottom: 2),
              child: TextField(
                decoration: InputDecoration(
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  labelText: "Name",
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class LocationCard1 extends StatelessWidget {
  const LocationCard1({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return const SizedBox(
      height: 280,
      width: 250,
      child: Card(
        elevation: 0,
        shape: ContinuousRectangleBorder(),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(Icons.location_on_rounded),
            Text("Gurugaon"),
            Padding(
              padding: EdgeInsets.only(left: 10.0, right: 10),
              child: Text(
                  "CREME CASTLE , 142 , 1F , DLF CITY COURT , MG ROAD , SIKENDARPUR GHOSI , GURUGRAM"),
            )
          ],
        ),
      ),
    );
  }
}

class ContactUsBanner extends StatelessWidget {
  const ContactUsBanner({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: Stack(
        children: [
          Image.asset(
            "assets/images/banner2.jpeg",
            fit: BoxFit.cover,
            width: double.maxFinite,
            height: 400,
          ),
          const Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: EdgeInsets.only(right: 60.0),
              child: Image(
                  fit: BoxFit.cover,
                  height: 220,
                  image: AssetImage("assets/images/tbblogo.jpeg")),
            ),
          )
        ],
      ),
    );
  }
}
