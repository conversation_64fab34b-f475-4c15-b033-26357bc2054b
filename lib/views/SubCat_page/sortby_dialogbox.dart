import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SortByDialogBox extends StatefulWidget {
  const SortByDialogBox({super.key, required this.onTapSort});
  final Function(String) onTapSort;
  @override
  _SortByDialogBoxState createState() => _SortByDialogBoxState();
}

class _SortByDialogBoxState extends State<SortByDialogBox> {
  String? _selectedOption = 'Best Selling';

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Padding(
        padding: const EdgeInsets.only(right: 80, top: 25, bottom: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              children: [
                IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onPressed: () => showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Material(
                            child: Container(
                              color: Colors.white,
                              width: 300,
                              height: 320,
                              child: Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 10, top: 15),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          "SORT BY",
                                          style: GoogleFonts.aBeeZee(
                                              fontSize: 25,
                                              fontWeight: FontWeight.w800),
                                        ),
                                        IconButton(
                                          hoverColor: Colors.transparent,
                                          highlightColor: Colors.transparent,
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          icon:
                                              const Icon(CupertinoIcons.xmark),
                                        ),
                                      ],
                                    ),
                                  ),
                                  _buildSortOption(
                                      context, 'Featured', _selectedOption),
                                  _buildSortOption(
                                      context, 'Best Selling', _selectedOption),
                                  _buildSortOption(context,
                                      'Alphabetically, A-Z', _selectedOption),
                                  _buildSortOption(context,
                                      'Alphabetically, Z-A', _selectedOption),
                                  _buildSortOption(context,
                                      'Price, Low to High', _selectedOption),
                                  _buildSortOption(context,
                                      'Price, High to Low', _selectedOption),
                                  _buildSortOption(context, 'Date, Old to New',
                                      _selectedOption),
                                  _buildSortOption(context, 'Date, New to Old',
                                      _selectedOption),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  icon: const Icon(CupertinoIcons.sort_down),
                ),
                InkWell(
                  hoverColor: Colors.transparent,
                  overlayColor:
                      const WidgetStatePropertyAll(Colors.transparent),
                  child: Text(
                    "SORT BY",
                    style: GoogleFonts.aBeeZee(
                        fontSize: 18, fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(
      BuildContext context, String title, String? selectedOption) {
    return Row(
      children: [
        Radio<String>(
          fillColor:
              const WidgetStatePropertyAll(Color.fromARGB(255, 192, 99, 130)),
          value: title,
          groupValue: _selectedOption,
          onChanged: (value) {
            setState(() {
              _selectedOption = value;
              Navigator.pop(context);
              widget.onTapSort(_selectedOption ?? "");
            });
          },
        ),
        InkWell(
          onTap: () {
            setState(() {
              _selectedOption = title;
              Navigator.pop(context);
              widget.onTapSort;
            });
          },
          child: Text(
            title,
            style: const TextStyle(fontSize: 13),
          ),
        ),
      ],
    );
  }
}
