import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/views/common/responsive.dart';
import 'package:tbb_web/views/homepage/display_card1.dart';

class SubcatProducts extends StatefulWidget {
  const SubcatProducts({
    super.key,
    required this.productlist,
    // required this.scrolCtrl,

    // this.nextPage,
    // this.previospage,
  });
  final List<ProductModel> productlist;
  // final ScrollController scrolCtrl;

  // final Function? nextPage;
  // final Function? previospage;

  @override
  State<SubcatProducts> createState() => _SubcatProductsState();
}

class _SubcatProductsState extends State<SubcatProducts> {
  bool typeis = true;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      return ResponsiveWid(
        mobile: widget.productlist.isEmpty
            ? const Center(
                child: Padding(
                padding: EdgeInsets.symmetric(vertical: 300.0),
                child: Text(
                  "Empty!!",
                  style: TextStyle(fontSize: 24),
                ),
              ))
            : Padding(
                padding: const EdgeInsets.only(
                  left: 15.0,
                  right: 15,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      // spacing: 10,
                      runSpacing: 10,
                      children: [
                        ...List.generate(widget.productlist.length, (index) {
                          typeis =
                              (widget.productlist[index].type == "Flower" ||
                                  widget.productlist[index].type == "Add-On");
                          return DisplayCard1(
                            isCake: !typeis,
                            product: widget.productlist[index],
                          );
                        }),
                      ],
                    ),
                  ],
                ),
              ),
        tablet: widget.productlist.isEmpty
            ? const Center(
                child: Padding(
                padding: EdgeInsets.symmetric(vertical: 285.0),
                child: Text(
                  "Empty!!",
                  style: TextStyle(fontSize: 24),
                ),
              ))
            : Padding(
                padding: const EdgeInsets.only(
                    left: 20.0, bottom: 60, right: 20, top: 40),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      runSpacing: 10,
                      // spacing: 10,
                      children: [
                        ...List.generate(widget.productlist.length, (index) {
                          typeis =
                              (widget.productlist[index].type == "Flower" ||
                                  widget.productlist[index].type == "Add-On");
                          return DisplayCard1(
                            isCake: !typeis,
                            product: widget.productlist[index],
                          );
                        }),
                      ],
                    ),
                  ],
                ),
              ),
        desktop: widget.productlist.isEmpty
            ? const Center(
                child: Padding(
                padding: EdgeInsets.symmetric(vertical: 215.0),
                child: Text(
                  "Empty!!",
                  style: TextStyle(fontSize: 24),
                ),
              ))
            : Padding(
                padding: const EdgeInsets.all(50),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Wrap(
                      runSpacing: 10,
                      // spacing: 10,
                      children: [
                        ...List.generate(widget.productlist.length, (index) {
                          typeis =
                              (widget.productlist[index].type == "Flower" ||
                                  widget.productlist[index].type == "Add-On");
                          return DisplayCard1(
                            isCake: !typeis,
                            product: widget.productlist[index],
                          );
                        }),
                      ],
                    ),
                    // widget.productlist.isEmpty
                    //     ? const SizedBox()
                    //     : Padding(
                    //         padding: const EdgeInsets.only(right: 100.0),
                    //         child: Row(
                    //           children: [
                    //             Padding(
                    //               padding: const EdgeInsets.only(
                    //                   left: 10.0, top: 10),
                    //               child: IconButton(
                    //                   onPressed: widget.previospage!(),
                    //                   icon:
                    //                       Icon(Icons.arrow_back_ios_new_sharp)),
                    //             ),
                    //             Spacer(),
                    //             Padding(
                    //               padding: const EdgeInsets.only(
                    //                   right: 10.0, top: 10),
                    //               child: IconButton(
                    //                   onPressed: widget.nextPage!(),
                    //                   icon:
                    //                       Icon(Icons.arrow_forward_ios_sharp)),
                    //             )
                    //           ],
                    //         ),
                    // ),
                  ],
                ),
              ),
      );
    });
  }
}
