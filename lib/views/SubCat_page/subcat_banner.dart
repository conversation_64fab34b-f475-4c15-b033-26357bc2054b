import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/views/common/responsive.dart';

class SubCatBanner extends StatelessWidget {
  const SubCatBanner({
    super.key,
    required this.name,
  });
  final String name;
  @override
  Widget build(BuildContext context) {
    return ResponsiveWid(
      mobile: SizedBox(
        height: 120,
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.asset(
              // "assets/images/common-banner 3.png",
              "assets/images/banner2.jpeg",
              fit: BoxFit.cover,
              width: double.maxFinite,
              height: 200,
            ),
            <PERSON><PERSON>(
              alignment: Alignment.center,
              child: Text(
                name.toUpperCase(),
                style: GoogleFonts.crimsonPro(
                    fontSize: 24, fontWeight: FontWeight.w700),
              ),
            ),
          ],
        ),
      ),
      tablet: SizedBox(
        height: 200,
        child: Stack(
          children: [
            Image.asset(
              "assets/images/common-banner 3.png",
              // "assets/images/banner2.jpeg",
              fit: BoxFit.cover,
              width: double.maxFinite,
              height: 300,
            ),
            Align(
              child: Text(
                name.toUpperCase(),
                style: GoogleFonts.crimsonPro(
                    fontSize: 40, fontWeight: FontWeight.w700),
              ),
            ),
          ],
        ),
      ),
      desktop: SizedBox(
        height: 200,
        child: Stack(
          children: [
            Image.asset(
              "assets/images/common-banner 3.png",
              // "assets/images/banner2.jpeg",
              fit: BoxFit.cover,
              width: double.maxFinite,
              height: 400,
            ),
            Align(
              child: Text(
                name.toUpperCase(),
                style: GoogleFonts.crimsonPro(
                    fontSize: 50, fontWeight: FontWeight.w700),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
