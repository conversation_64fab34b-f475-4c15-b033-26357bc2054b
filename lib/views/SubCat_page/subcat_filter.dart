import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:input_quantity/input_quantity.dart';
import 'package:tbb_web/shared/theme.dart';

class SubCatFilter extends StatelessWidget {
  const SubCatFilter({
    super.key,
    required this.isCake,
  });
  final String isCake;
  @override
  Widget build(BuildContext context) {
    // print(isCake);
    return Padding(
      padding: const EdgeInsets.only(left: 100.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Divider(
            height: 0,
          ),
          if ((isCake != "Add-On" && isCake != "Flower") &&
              (isCake != "Dessert"))
            const Weightfilter(),
          if ((isCake != "Add-On" && isCake != "Flower") &&
              (isCake != "Dessert"))
            const Divider(
              height: 0,
            ),
          const Availabilityfilter(),
          const Divider(
            height: 0,
          ),
          const Pricefilter(),
          const Divider(
            height: 0,
          ),
          const Typefilter(),
          const Divider(
            height: 0,
          ),
          // ElevatedButton(
          //     style: ButtonStyle(
          //         shape: WidgetStatePropertyAll(RoundedRectangleBorder(
          //             borderRadius: BorderRadius.circular(3)))),
          //     onPressed: () {},
          //     child: Text("Clear All"))
        ],
      ),
    );
  }
}

class Typefilter extends StatefulWidget {
  const Typefilter({
    super.key,
  });

  @override
  State<Typefilter> createState() => _TypefilterState();
}

class _TypefilterState extends State<Typefilter> {
  bool checked = false;
  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      childrenPadding: EdgeInsets.all(10),
      tilePadding: const EdgeInsets.all(5),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(0), side: BorderSide.none),
      title: Text(
        "PRODUCT TYPE",
        style: GoogleFonts.aBeeZee(fontSize: 14, fontWeight: FontWeight.bold),
      ),
      trailing: const Icon(CupertinoIcons.add),
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 6.0),
          child: Row(
            children: [
              const Text(
                "DESIGNER CAKES",
                style: TextStyle(fontWeight: FontWeight.w700, fontSize: 14),
              ),
              Checkbox(
                side: BorderSide(color: Color.fromARGB(255, 88, 87, 87)),
                checkColor: Color.fromARGB(255, 221, 87, 131),
                overlayColor: const WidgetStatePropertyAll(Colors.transparent),
                activeColor: Color.fromARGB(255, 245, 235, 238),
                hoverColor: Colors.transparent,
                value: checked,
                onChanged: (value) {
                  checked = value!;
                  setState(() {});
                },
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 8,
        ),
      ],
    );
  }
}

class Pricefilter extends StatelessWidget {
  const Pricefilter({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      childrenPadding: EdgeInsets.all(10),
      tilePadding: const EdgeInsets.all(5),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(0), side: BorderSide.none),
      title: Text(
        "PRICE",
        style: GoogleFonts.aBeeZee(fontSize: 14, fontWeight: FontWeight.bold),
      ),
      trailing: const Icon(CupertinoIcons.add),
      children: const [
        Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text("Min"),
                      InputQty(
                          initVal: 0,
                          decoration: QtyDecorationProps(
                              plusBtn: Icon(
                                CupertinoIcons.arrowtriangle_up_fill,
                                size: 8,
                              ),
                              minusBtn: Icon(
                                CupertinoIcons.arrowtriangle_down_fill,
                                size: 8,
                              ),
                              border: InputBorder.none,
                              qtyStyle: QtyStyle.btnOnRight)),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text("Max"),
                      InputQty(
                          initVal: 0,
                          decoration: QtyDecorationProps(
                              plusBtn: Icon(
                                CupertinoIcons.arrowtriangle_up_fill,
                                size: 8,
                              ),
                              minusBtn: Icon(
                                CupertinoIcons.arrowtriangle_down_fill,
                                size: 8,
                              ),
                              border: InputBorder.none,
                              qtyStyle: QtyStyle.btnOnRight)),
                    ],
                  ),
                ),
                SizedBox(
                  height: 50,
                )
              ],
            )
          ],
        )
      ],
    );
  }
}

class Availabilityfilter extends StatefulWidget {
  const Availabilityfilter({
    super.key,
  });

  @override
  State<Availabilityfilter> createState() => _AvailabilityfilterState();
}

class _AvailabilityfilterState extends State<Availabilityfilter> {
  List<bool> checkedValues = [false, false];
  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      childrenPadding: EdgeInsets.all(10),
      tilePadding: const EdgeInsets.all(5),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(0), side: BorderSide.none),
      title: Text(
        "AVAILABILITY",
        style: GoogleFonts.aBeeZee(fontSize: 14, fontWeight: FontWeight.bold),
      ),
      trailing: const Icon(CupertinoIcons.add),
      children: [
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 6.0),
              child: Row(
                children: [
                  const Text(
                    "IN STOCK",
                    style: TextStyle(fontWeight: FontWeight.w700, fontSize: 13),
                  ),
                  Checkbox(
                    side: BorderSide(color: Color.fromARGB(255, 88, 87, 87)),
                    checkColor: Color.fromARGB(255, 221, 87, 131),
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    activeColor: Color.fromARGB(255, 245, 235, 238),
                    hoverColor: Colors.transparent,
                    value: checkedValues[0],
                    onChanged: (value) {
                      checkedValues[0] = value!;
                      setState(() {});
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 6.0),
              child: Row(
                children: [
                  const Text(
                    "OUT OF STOCK",
                    style: TextStyle(fontWeight: FontWeight.w700, fontSize: 13),
                  ),
                  Checkbox(
                    side: BorderSide(color: Color.fromARGB(255, 88, 87, 87)),
                    checkColor: Color.fromARGB(255, 221, 87, 131),
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    activeColor: Color.fromARGB(255, 245, 235, 238),
                    hoverColor: Colors.transparent,
                    value: checkedValues[1],
                    onChanged: (value) {
                      checkedValues[1] = value!;
                      setState(() {});
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
          ],
        ),
      ],
    );
  }
}

class Weightfilter extends StatefulWidget {
  const Weightfilter({
    super.key,
  });

  @override
  State<Weightfilter> createState() => _WeightfilterState();
}

class _WeightfilterState extends State<Weightfilter> {
  final List<String> weights = [
    '0.25',
    '0.5',
    '0.75',
    '1',
    '1.5',
    '2',
    '2.5',
    '3',
    '3.5',
    '4',
    '4.5',
    '5',
    '6',
    '7'
  ];
  List<bool> checkValue = [
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false
  ];
  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      childrenPadding: EdgeInsets.all(10),
      tilePadding: const EdgeInsets.all(5),
      collapsedBackgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(0), side: BorderSide.none),
      backgroundColor: Colors.transparent,
      title: Text(
        "WEIGHT",
        style: GoogleFonts.aBeeZee(fontSize: 14, fontWeight: FontWeight.bold),
      ),
      trailing: const Icon(
        CupertinoIcons.plus,
      ),
      children: [
        StaggeredGrid.extent(
          maxCrossAxisExtent: 90,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          children: [
            ...List.generate(weights.length, (index) {
              return Row(
                children: [
                  Text(
                    "${weights[index]} Kg",
                    style: TextStyle(fontWeight: FontWeight.w700, fontSize: 13),
                  ),
                  Spacer(),
                  Checkbox(
                    side: BorderSide(color: Color.fromARGB(255, 88, 87, 87)),
                    checkColor: Color.fromARGB(255, 221, 87, 131),
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    activeColor: Color.fromARGB(255, 245, 235, 238),
                    hoverColor: Colors.transparent,
                    value: checkValue[index],
                    onChanged: (value) {
                      checkValue[index] = value!;
                      setState(() {});
                    },
                  ),
                ],
              );
            }),
            // const SizedBox(
            //   height: 50,
            // ),
          ],
        ),
      ],
    );
  }
}
