import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/const.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/views/SubCat_page/subcat_Products.dart';
import 'package:tbb_web/views/SubCat_page/subcat_banner.dart';
import 'package:tbb_web/views/common/responsive.dart';
import 'package:tbb_web/wrapper.dart';

Future<List<ProductModel>> fromSnapshot(
    List<DocumentSnapshot<Map<String, dynamic>>> value) async {
  // Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
  return value.map((data) {
    print(data.id);

    return ProductModel(
      docId: data.id,
      name: data['name'],
      type: data['type'],
      tags: List<String>.from(data['tags']),
      titleTag: List<String>.from(data['titleTag'] ?? []),
      lower: data['lower'],
      // isdessert: data['isdessert'],
      // isflower: data['isflower'],
      fixedprice: data['fixedprice'],
      flavour: List<String>.from(data['flavour'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      sku: data['sku'],
      weightRangeDocId: data['weightRangeDocId'],
      catNameList: (data.data()?.containsKey('catNameList') ?? false)
          ? List<String>.from(data['catNameList'] ?? [])
          : <String>[],
      images: List<String>.from(data['images']),
      desc: data['desc'],
      notavailable: data['notavailable'],
      minOrderTime: data['minOrderTime'],
      designCostdocId: data['designCostdocId'],
      tier: data['tier'] ?? false,
      hyperlink: data['hyperlink'],
    );
  }).toList();
}

const onePageCount = 24;

class SubcatPage extends StatefulWidget {
  const SubcatPage({super.key, required this.name});
  final String name;

  @override
  State<SubcatPage> createState() => _SubcatPageState();
}

class _SubcatPageState extends State<SubcatPage> {
  final ScrollController scrolCtrl = ScrollController();
  @override
  Widget build(BuildContext context) {
    return Wrapper(
      scrollController: scrolCtrl,
      body: CatUiWid(name: widget.name, scrolCtrl: scrolCtrl),
      key: ValueKey(DateTime.now()),
    );
  }
}

class CatUiWid extends StatefulWidget {
  const CatUiWid({super.key, required this.name, required this.scrolCtrl});
  final String name;
  final ScrollController scrolCtrl;
  @override
  State<CatUiWid> createState() => _CatUiWidState();
}

class _CatUiWidState extends State<CatUiWid> {
  List<String> namelist = [];
  List<String> combi = [];
  List<ProductModel> products = [];
  List<ProductModel> allproducts = [];
  bool loaded = true;
  // ValueNotifier<bool> isLoading = ValueNotifier(true);
  String? previousName;
  num totalCount = 0;
  final NumberPaginatorController paginatorController =
      NumberPaginatorController();
  int _currentPage = 0;
  int pageNum = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    print("object");
    namelist = splitString(widget.name);
    namelist.remove("cakes");
    namelist.remove("cake");
    namelist.removeWhere((element) => element == "");
    previousName = widget.name;

    fetch();
  }

  fetch() async {
    try {
      // isLoading.value = true;
      // if (mounted) setState(() {});
      final data = await FBFireStore.product
          .where('notavailable', isEqualTo: false)
          .where('catNameList', arrayContains: widget.name.trim().toLowerCase())
          .get();
      // print(data.docs.map((e) => e.id).toList());
      allproducts = await compute(fromSnapshot, data.docs);

      if (allproducts.isNotEmpty) {
        final firstProduct = allproducts.first;
        allproducts.removeAt(0);
        allproducts.add(firstProduct);
      }

      if (byFlavours.contains(widget.name.toLowerCase())) {
        allproducts.addAll(await FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where('type', isEqualTo: "Designer Cake")
            .where('titleTag', arrayContainsAny: namelist)
            .get()
            .then((value) async {
          List<DocumentSnapshot<Map<String, dynamic>>> val = [];
          for (var doc in value.docs) {
            val.addIf(
                allproducts
                    .where((element) => element.docId.contains(doc.id))
                    .isEmpty,
                doc);
          }

          return await compute(fromSnapshot, val);
        }));
      }
      allproducts.sort((a, b) {
        // Determine if 'a' matches any element in the 'namelist'
        final bool aMatches =
            a.titleTag?.any((element) => namelist.contains(element)) ?? false;
        // Determine if 'b' matches any element in the 'namelist'
        final bool bMatches =
            b.titleTag?.any((element) => namelist.contains(element)) ?? false;

        // If both match or both don't match, return 0 (no change in order)
        if (aMatches == bMatches) {
          return 0;
        }

        // If 'a' matches and 'b' doesn't, return -1 to prioritize 'a'
        // If 'b' matches and 'a' doesn't, return 1 to prioritize 'b'
        return aMatches ? -1 : 1;
      });
      // isLoading.value = false;
      loaded = false;

      if (mounted) setState(() {});
    } catch (e) {
      debugPrint(e.toString());
      // TODO
    }
  }

  cal() async {
    int x = (await FBFireStore.product
                .where('catNameList', arrayContains: widget.name)
                .count()
                .get())
            .count ??
        0;
    int y = (await FBFireStore.product
                .where('tags', arrayContainsAny: namelist)
                .count()
                .get())
            .count ??
        0;
    totalCount = x + y;
  }

  List<String> splitString(String myString) {
    List<String> splittedString =
        myString.split(" ").map((str) => str.trim()).toList();
    splittedString.remove("cakes");

    return splittedString;
  }

  temp() async {
    await Future.delayed(Duration(milliseconds: 400));
    paginatorController.navigateToPage(Get.find<ProductCtrl>().wasOnPageNo);
    _currentPage = Get.find<ProductCtrl>().wasOnPageNo;
    paginatorController.currentPage = Get.find<ProductCtrl>().wasOnPageNo;

    setState(() {});
  }

  bool once = false;
  @override
  Widget build(BuildContext context) {
    if (!once) {
      WidgetsBinding.instance.addPostFrameCallback(
        (timeStamp) {
          once = true;
          temp();
          // print("-------${Get.find<ProductCtrl>().recentScrollOffset}");
        },
      );
    }

    // print(paginatorController.currentPage);
    // if (widget.name != previousName) {
    //   _currentPage = 0;
    //   namelist = splitString(widget.name);
    //   namelist.remove("cakes");
    //   namelist.remove("cake");
    //   namelist.removeWhere((element) => element == "");
    //   previousName = widget.name;

    //   fetch();
    // }

    final noofPages = (allproducts.length / onePageCount).ceil();

    List<ProductModel> subList = allproducts.isEmpty
        ? allproducts
        : allproducts.length <= onePageCount
            ? allproducts.getRange(0, allproducts.length).toList()
            : allproducts
                .getRange(
                    (_currentPage * onePageCount),
                    _currentPage == (noofPages - 1)
                        ? allproducts.length
                        : (_currentPage * onePageCount) + onePageCount)
                .toList();
    final size = MediaQuery.sizeOf(context);

    return loaded
        ? const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 100.0),
              child: Text(
                "Loading...",
                style: TextStyle(
                    fontSize: 24, color: Color.fromARGB(255, 217, 137, 132)),
              ),
            ),
          )
        :
        // ValueListenableBuilder<bool>(
        //     valueListenable: isLoading,
        //     builder: (context, value, child) {
        //       return value
        //           ? const Center(
        //               child: Padding(
        //                 padding: EdgeInsets.symmetric(vertical: 100.0),
        //                 child: Text(
        //                   "Loading...",
        //                   style: TextStyle(
        //                       fontSize: 24,
        //                       color: Color.fromARGB(255, 217, 137, 132)),
        //                 ),
        //               ),
        //             )
        //           :
        ResponsiveWid(
            mobile: subList.isEmpty
                ? Column(
                    children: [
                      SubCatBanner(
                          name: widget.name == "discount"
                              ? "FLASH DEAL"
                              : widget.name),
                      const SizedBox(height: 65),
                      Padding(
                        padding: EdgeInsets.only(
                            top: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                    ? 130
                                    : 170,
                            bottom: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                    ? 130
                                    : 160),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "No Product Available!",
                              style: TextStyle(
                                  fontSize: size.width <= 510 ? 18 : 28),
                            ),
                            //     SizedBox(
                            //       width: 10,
                            //     ),
                            //     Image.asset(
                            //       height: 40,
                            //       'assets/images/shopping-cart.png',
                            //     )
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 65,
                      ),
                    ],
                  )
                : Column(
                    children: [
                      SubCatBanner(
                          name: widget.name == "discount"
                              ? "FLASH DEAL"
                              : widget.name),
                      const Row(
                        children: [],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SubcatProducts(
                        productlist: subList,
                        // scrolCtrl: scrolCtrl,
                      ),
                      subList.isEmpty
                          ? const SizedBox()
                          : Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20.0, vertical: 10),
                              child: NumberPaginator(
                                onPageChange: (p0) {
                                  _currentPage = p0;
                                  Get.find<ProductCtrl>().wasOnPageNo = p0;
                                  Get.find<ProductCtrl>().recentScrollOffset =
                                      widget.scrolCtrl.offset;
                                  widget.scrolCtrl.jumpTo(0);

                                  if (mounted) setState(() {});
                                },
                                prevButtonBuilder: (context) {
                                  return IconButton(
                                      onPressed: () {
                                        if (_currentPage > 0) {
                                          // _currentPage = _currentPage + 1;
                                          paginatorController.prev();
                                          Get.find<ProductCtrl>().wasOnPageNo =
                                              paginatorController.currentPage;
                                          Get.find<ProductCtrl>()
                                                  .recentScrollOffset =
                                              widget.scrolCtrl.offset;
                                          widget.scrolCtrl.jumpTo(0);
                                        }
                                        if (mounted) setState(() {});
                                      },
                                      icon: const Icon(
                                          CupertinoIcons.chevron_back));
                                },
                                nextButtonBuilder: (context) {
                                  return IconButton(
                                      onPressed: () {
                                        if (_currentPage != noofPages - 1) {
                                          // _currentPage = _currentPage + 1;
                                          paginatorController.next();
                                          Get.find<ProductCtrl>().wasOnPageNo =
                                              paginatorController.currentPage;
                                          Get.find<ProductCtrl>()
                                                  .recentScrollOffset =
                                              widget.scrolCtrl.offset;
                                          widget.scrolCtrl.jumpTo(0);
                                        }
                                        if (mounted) setState(() {});
                                      },
                                      icon: const Icon(
                                          CupertinoIcons.chevron_forward));
                                },

                                // initialPage: 1,
                                controller: paginatorController,
                                numberPages: noofPages,
                                // onPageChange: (int index) {
                                //       if (mounted)  setState(() {
                                //     _currentPage = index;
                                //     _fetchNextPage();
                                //   });
                                // },
                              ),
                            ),
                    ],
                  ),

            //tablet

            tablet: subList.isEmpty
                ? Column(
                    children: [
                      SubCatBanner(
                          name: widget.name == "discount"
                              ? "FLASH DEAL"
                              : widget.name),
                      const SizedBox(
                        height: 105,
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                    ? 180
                                    : 170,
                            bottom: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                    ? 180
                                    : 160),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "No Product Available!",
                              style: TextStyle(
                                  fontSize: size.width <= 510 ? 18 : 28),
                            ),
                            //     SizedBox(
                            //       width: 10,
                            //     ),
                            //     Image.asset(
                            //       height: 40,
                            //       'assets/images/shopping-cart.png',
                            //     )
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 105,
                      ),
                    ],
                  )
                : Column(
                    children: [
                      SubCatBanner(
                          name: widget.name == "discount"
                              ? "FLASH DEAL"
                              : widget.name),
                      const Row(
                        children: [],
                      ),
                      SubcatProducts(
                        // widget.scrolCtrl: widget.scrolCtrl,
                        productlist: subList,
                      ),
                      subList.isEmpty
                          ? const SizedBox()
                          : Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 50.0, vertical: 10),
                              child: NumberPaginator(
                                onPageChange: (p0) {
                                  _currentPage = p0;
                                  Get.find<ProductCtrl>().wasOnPageNo = p0;
                                  Get.find<ProductCtrl>().recentScrollOffset =
                                      widget.scrolCtrl.offset;
                                  widget.scrolCtrl.jumpTo(0);

                                  if (mounted) setState(() {});
                                },
                                prevButtonBuilder: (context) {
                                  return IconButton(
                                      onPressed: () {
                                        if (_currentPage > 0) {
                                          // _currentPage = _currentPage + 1;
                                          paginatorController.prev();
                                          Get.find<ProductCtrl>().wasOnPageNo =
                                              paginatorController.currentPage;
                                          Get.find<ProductCtrl>()
                                                  .recentScrollOffset =
                                              widget.scrolCtrl.offset;
                                          widget.scrolCtrl.jumpTo(0);
                                        }
                                        if (mounted) setState(() {});
                                      },
                                      icon: const Icon(
                                          CupertinoIcons.chevron_back));
                                },
                                nextButtonBuilder: (context) {
                                  return IconButton(
                                      onPressed: () {
                                        if (_currentPage != noofPages - 1) {
                                          // _currentPage = _currentPage + 1;
                                          paginatorController.next();
                                          Get.find<ProductCtrl>().wasOnPageNo =
                                              paginatorController.currentPage;
                                          Get.find<ProductCtrl>()
                                                  .recentScrollOffset =
                                              widget.scrolCtrl.offset;
                                          widget.scrolCtrl.jumpTo(0);
                                        }
                                        if (mounted) setState(() {});
                                      },
                                      icon: const Icon(
                                          CupertinoIcons.chevron_forward));
                                },

                                // initialPage: 1,
                                controller: paginatorController,
                                numberPages: noofPages,
                                // onPageChange: (int index) {
                                //       if (mounted)  setState(() {
                                //     _currentPage = index;
                                //     _fetchNextPage();
                                //   });
                                // },
                              ),
                            ),
                      const SizedBox(
                        height: 30,
                      ),
                    ],
                  ),

            //desktop

            desktop: subList.isEmpty
                ? Column(
                    children: [
                      SubCatBanner(
                          name: widget.name == "discount"
                              ? "FLASH DEAL"
                              : widget.name),
                      const SizedBox(
                        height: 55,
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                    ? 130
                                    : 170,
                            bottom: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                    ? 130
                                    : 160),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "No Product Available!",
                              style: TextStyle(
                                  fontSize: size.width <= 510 ? 18 : 28),
                            ),
                            //     SizedBox(
                            //       width: 10,
                            //     ),
                            //     Image.asset(
                            //       height: 40,
                            //       'assets/images/shopping-cart.png',
                            //     )
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 55,
                      ),
                    ],
                  )
                : Column(
                    children: [
                      SubCatBanner(
                          name: widget.name == "discount"
                              ? "FLASH DEAL"
                              : widget.name),
                      // SortByDialogBox(
                      //   onTapSort: (p0) {
                      //     products
                      //         .sort((a, b) => a.name.compareTo(b.name));
                      //         if (mounted)  setState(() {});
                      //   },
                      // ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Expanded(
                          //     child: SubCatFilter(
                          //   isCake: "Cake",
                          // )),
                          Expanded(
                              flex: 4,
                              child: SubcatProducts(
                                productlist: subList,
                                // widget.scrolCtrl: widget.scrolCtrl,
                              )),
                        ],
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      if (allproducts.length > onePageCount)
                        allproducts.isEmpty
                            ? const SizedBox()
                            : Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 50.0, vertical: 10),
                                child: NumberPaginator(
                                  onPageChange: (p0) {
                                    _currentPage = p0;

                                    Get.find<ProductCtrl>().recentScrollOffset =
                                        widget.scrolCtrl.offset;
                                    Get.find<ProductCtrl>().wasOnPageNo = p0;

                                    widget.scrolCtrl.jumpTo(0);
                                    if (mounted) setState(() {});
                                  },
                                  prevButtonBuilder: (context) {
                                    return IconButton(
                                        onPressed: () {
                                          if (_currentPage > 0) {
                                            // _currentPage = _currentPage + 1;
                                            paginatorController.prev();
                                            Get.find<ProductCtrl>()
                                                    .wasOnPageNo =
                                                paginatorController.currentPage;
                                            Get.find<ProductCtrl>()
                                                    .recentScrollOffset =
                                                widget.scrolCtrl.offset;

                                            widget.scrolCtrl.jumpTo(0);
                                          }
                                          if (mounted) setState(() {});
                                        },
                                        icon: const Icon(
                                            CupertinoIcons.chevron_back));
                                  },
                                  nextButtonBuilder: (context) {
                                    return IconButton(
                                        onPressed: () {
                                          if (_currentPage != noofPages - 1) {
                                            // _currentPage = _currentPage + 1;
                                            paginatorController.next();

                                            Get.find<ProductCtrl>()
                                                    .wasOnPageNo =
                                                paginatorController.currentPage;
                                            Get.find<ProductCtrl>()
                                                    .recentScrollOffset =
                                                widget.scrolCtrl.offset;
                                            widget.scrolCtrl.jumpTo(0);
                                          }
                                          if (mounted) setState(() {});
                                        },
                                        icon: const Icon(
                                            CupertinoIcons.chevron_forward));
                                  },

                                  // initialPage: 1,
                                  controller: paginatorController,
                                  numberPages: noofPages,
                                  // onPageChange: (int index) {
                                  //       if (mounted)  setState(() {
                                  //     _currentPage = index;
                                  //     _fetchNextPage();
                                  //   });
                                  // },
                                ),
                              ),
                    ],
                  ),
            //         );
            // }
          );
  }
}
