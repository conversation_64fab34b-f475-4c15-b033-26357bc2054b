import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/shared/theme.dart';
import 'package:tbb_web/wrapper.dart';

class PrivacyPolicy extends StatelessWidget {
  const PrivacyPolicy({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Wrapper(
      body: Padding(
        padding: EdgeInsets.only(
            left: 40.0,
            right: 40,
            top: size.width <= 510 ? 80 : 150,
            bottom: 40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 50.0, bottom: 40),
              child: Center(
                child: Text(
                  'PRIVACY POLICY',
                  style: GoogleFonts.crimsonPro(
                    color: Color.fromARGB(255, 216, 122, 153),
                    fontSize: size.width <= 510 ? 30 : 45,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            Container(
              constraints: BoxConstraints(maxWidth: 700),
              child: Text(
                "We ensure that any and all data provided by you will not be shared with any third party, and will only be used for communications with you by The Baker's Bar.",
                style: TextStyle(fontSize: size.width <= 510 ? 16 : 22),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
                style: ButtonStyle(
                    padding: WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 20, vertical: 10)),
                    shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15))),
                    backgroundColor: WidgetStatePropertyAll(
                        Color.fromARGB(255, 222, 155, 177))),
                onPressed: () {
                  context.go(Routes.home);
                },
                child: Text(
                  "Back To Home",
                  style: TextStyle(color: Colors.white),
                )),
            SizedBox(height: size.width <= 510 ? 120 : 160),
          ],
        ),
      ),
    );
  }
}
