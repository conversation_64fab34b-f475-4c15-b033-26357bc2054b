import 'package:flutter/material.dart';
import 'package:flutter_animated_button/flutter_animated_button.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/wrapper.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      body: SizedBox(
        height: 600,
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 500),
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              LoginTitle(),
              EmailTextfield(),
              PasswordTextfield(),
              ForgotYourPassText(),
              Signinbutton(),
              CreateAccbutton(),
            ],
          ),
        ),
      ),
    );
  }
}

class CreateAccbutton extends StatelessWidget {
  const CreateAccbutton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.only(bottom: 10.0, top: 10, left: 50, right: 50),
      child: AnimatedButton(
        borderColor: Colors.black,
        height: 40,
        width: 600,
        text: 'CREATE ACCOUNT',
        selectedBackgroundColor: Colors.pinkAccent.shade100,
        isReverse: true,
        selectedTextColor: Colors.white,
        transitionType: TransitionType.CENTER_LR_IN,
        backgroundColor: Colors.white,
        onPress: () async {
          // FBAuth.auth.signOut;
          // if (context.mounted) {
          //   context.go(Routes.about);
          // }
        },
        // Navigator.push(context,
        //     MaterialPageRoute(builder: (context) => const RegistrationPage())),
        animatedOn: AnimatedOn.onHover,
        textStyle: const TextStyle(fontSize: 15),
        animationDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}

class Signinbutton extends StatelessWidget {
  const Signinbutton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.only(bottom: 10.0, top: 10, left: 50, right: 50),
      child: AnimatedButton(
        height: 40,
        width: 600,
        text: 'SIGN IN',
        selectedBackgroundColor: Colors.pinkAccent.shade100,
        isReverse: true,
        selectedTextColor: Colors.white,
        transitionType: TransitionType.CENTER_LR_IN,
        backgroundColor: Colors.pink.shade200,
        onPress: () {},
        animatedOn: AnimatedOn.onHover,
        textStyle: const TextStyle(fontSize: 15),
        animationDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}

class ForgotYourPassText extends StatelessWidget {
  const ForgotYourPassText({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.only(bottom: 10.0, top: 10, left: 50, right: 50),
      child: Container(
          width: 600,
          alignment: Alignment.centerRight,
          child: InkWell(
            hoverColor: Colors.transparent,
            overlayColor: const WidgetStatePropertyAll(Colors.transparent),
            onTap: () {},
            child: Text(
              "FORGOT YOUR PASSWORD?",
              style: GoogleFonts.aBeeZee(color: Colors.grey),
            ),
          )),
    );
  }
}

class PasswordTextfield extends StatelessWidget {
  const PasswordTextfield({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.only(bottom: 10.0, top: 10, left: 50, right: 50),
      child: Container(
        width: 600,
        decoration: BoxDecoration(
            border: Border.all(),
            borderRadius: const BorderRadius.all(Radius.zero)),
        child: const Row(
          children: [
            Expanded(
              child: Padding(
                padding:
                    EdgeInsets.only(left: 10.0, right: 2, top: 2, bottom: 2),
                child: TextField(
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    labelText: "Password",
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class EmailTextfield extends StatelessWidget {
  const EmailTextfield({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.only(bottom: 10.0, top: 50, left: 50, right: 50),
      child: Container(
        width: 600,
        decoration: BoxDecoration(
            border: Border.all(),
            borderRadius: const BorderRadius.all(Radius.zero)),
        child: const Row(
          children: [
            Expanded(
              child: Padding(
                padding:
                    EdgeInsets.only(left: 10.0, right: 2, top: 2, bottom: 2),
                child: TextField(
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    labelText: "E-mail",
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class LoginTitle extends StatelessWidget {
  const LoginTitle({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        child: Text(
          "SIGN UP",
          style: GoogleFonts.roboto(
              color: Colors.black, fontSize: 40, fontWeight: FontWeight.w700),
        ));
  }
}
