import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';
import 'package:tbb_web/Models/order_model.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/views/common/responsive.dart';

class ThankyouPage extends StatefulWidget {
  const ThankyouPage({super.key, required this.order});
  final OrderModel? order;
  @override
  State<ThankyouPage> createState() => _ThankyouPageState();
}

class _ThankyouPageState extends State<ThankyouPage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    trackFacebookEvent('Purchase', {
      'value': widget.order?.total, // Total purchase amount
      'currency': 'IND',
      'content_ids': widget.order?.orderProductsMap
          .map((e) => e.id)
          .toList(), // IDs of purchased products
      'order_data': widget.order?.orderProductsMap
          .map((e) => e.name)
          .toList(), // IDs of purchased products
      'content_type': 'order'
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<ProductCtrl>(builder: (pctrl) {
        return widget.order == null
            ? Center(
                child: ElevatedButton(
                    style: const ButtonStyle(
                        padding: WidgetStatePropertyAll(
                            EdgeInsetsDirectional.symmetric(
                                horizontal: 40, vertical: 20)),
                        backgroundColor: WidgetStatePropertyAll(Colors.black)),
                    onPressed: () {
                      context.go(Routes.home);
                    },
                    child: const Text(
                      "Homepage",
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    )),
              )
            : ResponsiveWid(
                desktop: Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 50),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.greenAccent.shade100,
                            shape: BoxShape.circle,
                          ),
                          height: 180,
                          child: const Padding(
                            padding: EdgeInsets.all(30),
                            child: Image(
                                fit: BoxFit.fitHeight,
                                height: 90,
                                image: AssetImage(
                                    'assets/images/verify_9458915.png')),
                          ),
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          "Payment successful",
                          style: TextStyle(
                              fontSize: 45, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          "Successful Paid Rs. ${widget.order!.total}",
                          style: const TextStyle(
                              fontSize: 20, fontWeight: FontWeight.w300),
                        ),
                        const SizedBox(height: 50),
                        const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Payment methods",
                              style: TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        Container(
                          width: 400,
                          height: 400,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  style: BorderStyle.solid,
                                  color: Colors.grey.shade200)),
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text("Transaction ID"),
                                    Text(widget.order!.transId),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text("Date"),
                                    Text(widget.order!.createdAt
                                        .toString()
                                        .split(" ")
                                        .first),
                                  ],
                                ),
                                // Row(
                                //   mainAxisAlignment:
                                //       MainAxisAlignment.spaceBetween,
                                //   children: [
                                //     Text("Order No"),
                                //     Text(widget.order!.orderNo.toString()),
                                //   ],
                                // ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text("Amount"),
                                    Text(widget.order!.total.toString()),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text("Paid"),
                                    Text(widget.order!.isPaid ? "Yes" : "No"),
                                  ],
                                ),
                                // const Row(
                                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                //   children: [
                                //     Text("Status"),
                                //   ],
                                // ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    ElevatedButton(
                                        style: const ButtonStyle(
                                            padding: WidgetStatePropertyAll(
                                                EdgeInsetsDirectional.symmetric(
                                                    horizontal: 40,
                                                    vertical: 20)),
                                            backgroundColor:
                                                WidgetStatePropertyAll(
                                                    Colors.black)),
                                        onPressed: () {
                                          context.go(Routes.home);
                                        },
                                        child: const Text(
                                          "Homepage",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14),
                                        )),
                                    ElevatedButton(
                                        style: const ButtonStyle(
                                            padding: WidgetStatePropertyAll(
                                                EdgeInsetsDirectional.symmetric(
                                                    horizontal: 50,
                                                    vertical: 20)),
                                            backgroundColor:
                                                WidgetStatePropertyAll(
                                                    Colors.black)),
                                        onPressed: () async {
                                          context.go(Routes.account);
                                        },
                                        child: const Text(
                                          "Account",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14),
                                        )),
                                  ],
                                )
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                mobile: SingleChildScrollView(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 50, bottom: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.greenAccent.shade100,
                              shape: BoxShape.circle,
                            ),
                            height: 180,
                            child: const Padding(
                              padding: EdgeInsets.all(30),
                              child: Image(
                                  fit: BoxFit.fitHeight,
                                  height: 90,
                                  image: AssetImage(
                                      'assets/images/verify_9458915.png')),
                            ),
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            "Payment successful",
                            style: TextStyle(
                                fontSize: 30, fontWeight: FontWeight.bold),
                          ),
                          Text(
                            "Successful Paid Rs. ${pctrl.finalPrice}",
                            style: const TextStyle(
                                fontSize: 20, fontWeight: FontWeight.w300),
                          ),
                          const SizedBox(height: 50),
                          const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "Payment methods",
                                style: TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Container(
                            width: 350,
                            height: 380,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                    style: BorderStyle.solid,
                                    color: Colors.grey.shade200)),
                            child: Padding(
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Transaction ID"),
                                      Text(widget.order!.transId),
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Date"),
                                      Text(widget.order!.createdAt
                                          .toString()
                                          .split(" ")
                                          .first),
                                    ],
                                  ),
                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Text("Order No"),
                                  //     Text(widget.order!.orderNo.toString()),
                                  //   ],
                                  // ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Amount"),
                                      Text(widget.order!.total.toString()),
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Paid"),
                                      Text(widget.order!.isPaid ? "Yes" : "No"),
                                    ],
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: [
                                      ElevatedButton(
                                          style: const ButtonStyle(
                                              padding: WidgetStatePropertyAll(
                                                  EdgeInsetsDirectional
                                                      .symmetric(
                                                          horizontal: 40,
                                                          vertical: 20)),
                                              backgroundColor:
                                                  WidgetStatePropertyAll(
                                                      Colors.black)),
                                          onPressed: () {
                                            context.go(Routes.home);
                                          },
                                          child: const Text(
                                            "Homepage",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14),
                                          )),
                                      ElevatedButton(
                                          style: const ButtonStyle(
                                              padding: WidgetStatePropertyAll(
                                                  EdgeInsetsDirectional
                                                      .symmetric(
                                                          horizontal: 50,
                                                          vertical: 20)),
                                              backgroundColor:
                                                  WidgetStatePropertyAll(
                                                      Colors.black)),
                                          onPressed: () {
                                            context.go(Routes.account);
                                          },
                                          child: const Text(
                                            "Account",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14),
                                          )),
                                    ],
                                  )
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                tablet: SingleChildScrollView(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 50, bottom: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.greenAccent.shade100,
                              shape: BoxShape.circle,
                            ),
                            height: 180,
                            child: const Padding(
                              padding: EdgeInsets.all(30),
                              child: Image(
                                  fit: BoxFit.fitHeight,
                                  height: 90,
                                  image: AssetImage(
                                      'assets/images/verify_9458915.png')),
                            ),
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            "Payment successful",
                            style: TextStyle(
                                fontSize: 30, fontWeight: FontWeight.bold),
                          ),
                          Text(
                            "Successful Paid Rs. ${pctrl.finalPrice}",
                            style: const TextStyle(
                                fontSize: 20, fontWeight: FontWeight.w300),
                          ),
                          const SizedBox(height: 50),
                          const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "Payment methods",
                                style: TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Container(
                            width: 350,
                            height: 380,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                    style: BorderStyle.solid,
                                    color: Colors.grey.shade200)),
                            child: Padding(
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Transaction ID"),
                                      Text(widget.order!.transId),
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Date"),
                                      Text(widget.order!.createdAt
                                          .toString()
                                          .split(" ")
                                          .first),
                                    ],
                                  ),
                                  // Row(
                                  //   mainAxisAlignment:
                                  //       MainAxisAlignment.spaceBetween,
                                  //   children: [
                                  //     Text("Order No"),
                                  //     Text(widget.order!.orderNo.toString()),
                                  //   ],
                                  // ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Amount"),
                                      Text(widget.order!.total.toString()),
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Paid"),
                                      Text(widget.order!.isPaid ? "Yes" : "No"),
                                    ],
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: [
                                      ElevatedButton(
                                          style: const ButtonStyle(
                                              padding: WidgetStatePropertyAll(
                                                  EdgeInsetsDirectional
                                                      .symmetric(
                                                          horizontal: 40,
                                                          vertical: 20)),
                                              backgroundColor:
                                                  WidgetStatePropertyAll(
                                                      Colors.black)),
                                          onPressed: () {
                                            context.go(Routes.home);
                                          },
                                          child: const Text(
                                            "Homepage",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14),
                                          )),
                                      ElevatedButton(
                                          style: const ButtonStyle(
                                              padding: WidgetStatePropertyAll(
                                                  EdgeInsetsDirectional
                                                      .symmetric(
                                                          horizontal: 50,
                                                          vertical: 20)),
                                              backgroundColor:
                                                  WidgetStatePropertyAll(
                                                      Colors.black)),
                                          onPressed: () {
                                            context.go(Routes.account);
                                          },
                                          child: const Text(
                                            "Account",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14),
                                          )),
                                    ],
                                  )
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              );
      }),
    );
  }
}
