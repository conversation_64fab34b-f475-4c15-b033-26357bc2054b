import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:tbb_web/Models/catsetmodel.dart';
import 'package:tbb_web/Models/maincategorymodel.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/Models/search_result_model.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/shared/theme.dart';
import 'package:tbb_web/views/common/responsive.dart';

class MenuAppBar extends StatefulWidget {
  const MenuAppBar({
    super.key,
    this.onTapFunct,
    this.name,
    this.catSet,
    this.tags,
    required this.drawerOpen,
  });

  final MainCategoryModel? name;
  final MainCategoryModel? catSet;
  final MainCategoryModel? tags;
  final Function()? onTapFunct;
  final bool drawerOpen;

  @override
  State<MenuAppBar> createState() => _MenuAppBarState();
}

class _MenuAppBarState extends State<MenuAppBar> {
  final overlayController = OverlayPortalController();
  final overlayController3 = OverlayPortalController();
  final overlayController2 = OverlayPortalController();
  final overlayController4 = OverlayPortalController();
  int selectedIndex = 8;

  @override
  void initState() {
    super.initState();
    // Get.put(CategoryCtrl());
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Container(
      color: Colors.white,
      alignment: Alignment.center,
      child: GetBuilder<ProductCtrl>(
        builder: (ctrl) {
          int maxLen = 0;
          for (var element in ctrl.settings?.catset ?? <CatSetModel>[]) {
            if (element.subCats.length > maxLen) {
              maxLen = element.subCats.length;
            }
          }
          bool exceed = 20 * maxLen + 340 > size.height;
          return ResponsiveWid(
            //mobile
            mobile: Container(
              color: themeColor,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      right: 0.0,
                      bottom: 8,
                      top: 8,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Transform.scale(
                            scale: .8,
                            child: MenuButton(
                              drawerOpen: widget.drawerOpen,
                              onTapFunct: widget.onTapFunct,
                            ),
                          ),
                        ),
                        const SizedBox(height: 50, child: TbbLogo()),
                        // const Spacer(),
                        // const ProfileButton(),
                        const Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: CartButton(),
                        ),
                      ],
                    ),
                  ),
                  const SearchField(height: 10),
                ],
              ),
            ),

            //tablet
            tablet: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    MenuButton(
                      drawerOpen: widget.drawerOpen,
                      onTapFunct: widget.onTapFunct,
                    ),
                    const SizedBox(width: 15),
                    const TbbLogo(),
                    const SizedBox(width: 20),
                    const SearchField(height: 40),
                    const Padding(
                      padding: EdgeInsets.all(20.0),
                      child: ProfileButton(),
                    ),
                    const CartButton(),
                  ],
                ),
              ],
            ),

            //desktop
            desktop: Row(
              children: [
                const Padding(
                  padding: EdgeInsets.only(left: 50.0),
                  child: TbbLogo(),
                ),
                const SizedBox(width: 15),
                Expanded(
                  flex: 2,
                  child: Container(
                    color: Colors.white,
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Wrap(
                            alignment: WrapAlignment.start,
                            runAlignment: WrapAlignment.start,
                            crossAxisAlignment: WrapCrossAlignment.center,
                            children: List.generate(ctrl.maincategory.length, (
                              index,
                            ) {
                              return AppbarMenuItem(
                                selectedIndex: selectedIndex,
                                openedIndex: (selectedInd) {
                                  selectedIndex = selectedInd;
                                  setState(() {});
                                },
                                index: index,
                                overlayControllerList: [
                                  overlayController,
                                  overlayController2,
                                  overlayController3,
                                  overlayController4,
                                ],
                                name: ctrl.maincategory[index].name,
                                catsetid: ctrl.maincategory[index].catSet,
                                exceed: exceed,
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SearchField(height: 40),
                      Padding(
                        padding: EdgeInsets.all(20.0),
                        child: ProfileButton(),
                      ),
                      CartButton(),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class MenuButton extends StatelessWidget {
  const MenuButton({super.key, this.onTapFunct, required this.drawerOpen});
  final Function()? onTapFunct;
  final bool drawerOpen;
  @override
  Widget build(BuildContext context) {
    // print(drawerOpen);
    return IconButton(
      // padding: EdgeInsets.only(left: 25),
      style: const ButtonStyle(iconSize: WidgetStatePropertyAll(33)),
      icon: Icon(drawerOpen ? CupertinoIcons.xmark : Icons.menu),
      onPressed: onTapFunct,
      // onPressed: onTap,
    );
  }
}

class SearchField extends StatefulWidget {
  const SearchField({super.key, required this.height});
  final double height;

  @override
  State<SearchField> createState() => _SearchFieldState();
}

class _SearchFieldState extends State<SearchField> {
  // late List<ProductModel> _lastOptions = <ProductModel>[];
  List<SearchRequirementModel> requirementSearchResult = [];
  String? searchedRequirement;
  String? searchedRequirementDocId;
  String? searchedRequirementType;
  String? searchedLocationType;
  String? searchedLocation;
  String? searchedLocationDocId;
  getRequirmentSearchData(String str) async {
    final searchLimit = (str.isEmpty || str == "") ? 5 : 15;
    List<SearchRequirementModel> results = [];

    results.addAllIf(
      str.isEmpty || str == "",
      Get.find<ProductCtrl>().subCategoriesList
          .sublist(0, 5)
          .map((e) => SearchRequirementModel(subCategoriesModel: e)),
    );
    results.addAllIf(
      str.isEmpty || str == "",
      await FBFireStore.product
          .where('notavailable', isEqualTo: false)
          // .where('name', isGreaterThanOrEqualTo: str)
          // .where('name', isLessThanOrEqualTo: "$str\uf7ff")
          .limit(5)
          .get()
          .then(
            (value) => value.docs
                .map((e) => ProductModel.fromSnapshot(e))
                .map((e) => SearchRequirementModel(product: e)),
          ),
    );

    if (results.length < 3) {
      results.addAll(
        Get.find<ProductCtrl>().subCategoriesList
            .where(
              (element) => element.toLowerCase().contains(str.toLowerCase()),
            )
            .map((e) => SearchRequirementModel(subCategoriesModel: e)),
      );
    }
    if (results.length < searchLimit) {
      results.addAll(
        await FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where(
              Filter.and(
                Filter('lower', isGreaterThanOrEqualTo: str),
                Filter('lower', isLessThan: '$str\uf8ff'),
              ),
            )
            .limit(5)
            .get()
            .then((event) {
              return event.docs
                  .map((e) => ProductModel.fromSnapshot(e))
                  .map((e) => SearchRequirementModel(product: e));
            }),
      );
    }
    if (results.length < 3) {
      results.addAll(
        await FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where(
              'titleTag',
              arrayContainsAny: str.split(' ').map((e) => e).toList(),
            )
            .limit(5)
            .get()
            .then((event) {
              return event.docs
                  .map((e) => ProductModel.fromSnapshot(e))
                  .map((e) => SearchRequirementModel(product: e));
            }),
      );
    }
    if (results.length < 3) {
      results.addAll(
        await FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where(
              'tags',
              arrayContainsAny: str.split(' ').map((e) => e).toList(),
            )
            .limit(5)
            .get()
            .then((event) {
              return event.docs
                  .map((e) => ProductModel.fromSnapshot(e))
                  .map((e) => SearchRequirementModel(product: e));
            }),
      );
    }

    List<SearchRequirementModel> filteredList = [];
    for (var element in results) {
      if (!(filteredList.contains(element))) {
        filteredList.add(element);
      }
    }
    requirementSearchResult.clear();
    requirementSearchResult.addAll(filteredList);
    requirementSearchResult;
  }

  Widget getTile(SearchRequirementModel option) {
    if (option.type() == SearchResultTypes.mainCategory) {
      return SearchedCategoryTile(option: option);
    } else if (option.type() == SearchResultTypes.subCategoriesModel) {
      return Text('${option.subCategoriesModel?.capitalizeFirst}');
    } else if (option.type() == SearchResultTypes.product) {
      return SearchedProductTile(option: option);
    }
    return const SizedBox();
  }

  String? assignValue(SearchRequirementModel option) {
    if (option.type() == SearchResultTypes.mainCategory) {
      searchedRequirement = option.mainCategory?.name;
      searchedRequirementType = option.type();
      searchedRequirementDocId = option.mainCategory?.docId;
      if (context.mounted) {
        context.go('${Routes.category}/${searchedRequirement?.toLowerCase()}');
      }
      return searchedRequirement;
    } else if (option.type() == SearchResultTypes.subCategoriesModel) {
      searchedRequirement = option.subCategoriesModel;
      searchedRequirementType = option.type();
      if (context.mounted) {
        context.go('${Routes.category}/${searchedRequirement?.toLowerCase()}');
      }
      // searchedRequirementDocId = option.subCategoriesModel?.id;
      return searchedRequirement;
    } else if (option.type() == SearchResultTypes.product) {
      searchedRequirement = option.product?.name;
      searchedRequirementType = option.type();
      searchedRequirementDocId = option.product?.docId;

      if (context.mounted) {
        context.go('${Routes.product}/$searchedRequirementDocId');
      }

      return searchedRequirement;
    }
    return null;
  }

  String? searchedString;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return size.width <= 510
        ? Padding(
            padding: const EdgeInsets.only(left: 20.0, right: 20, bottom: 10),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(3),
              ),
              height: 30,
              child: autoComplete(size),
            ),
          )
        : Expanded(
            child: Container(
              color: Colors.white,
              height: widget.height,
              child: autoComplete(size),
            ),
          );
  }

  Autocomplete<SearchRequirementModel> autoComplete(Size size) {
    return Autocomplete<SearchRequirementModel>(
      optionsMaxHeight: 700,
      onSelected: (option) {
        assignValue(option);
      },
      displayStringForOption: (option) =>
          capitalizeFirstLetters(assignValue(option) ?? ""),
      fieldViewBuilder:
          (context, textEditingController, focusNode, onFieldSubmitted) {
            return TextFormField(
              decoration: InputDecoration(
                contentPadding: EdgeInsets.only(
                  right: 10,
                  left: 10,
                  top: size.width <= 510 ? 0 : 10,
                  bottom: size.width <= 510 ? 10 : 10,
                ),
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(0.0),
                  child: size.width <= 510
                      ? SearchButton(searchedString: searchedString ?? "")
                      : Container(
                          height: 50,
                          width: 45,
                          color: themeColor,
                          child: SearchButton(
                            searchedString: searchedString ?? "",
                          ),
                        ),
                ),
                border: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: size.width <= 510 ? Colors.white : themeColor,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: size.width <= 510 ? Colors.white : themeColor,
                  ),
                ),
                hintText: 'What are you looking for?',
              ),
              controller: textEditingController,
              focusNode: focusNode,
              onChanged: (value) {
                searchedString = value;
                setState(() {});
              },
              onFieldSubmitted: (value) {
                if (context.mounted) {
                  context.go('${Routes.search}/$value');
                }
                // onFieldSubmitted();
              },
            );
          },
      optionsViewBuilder: (context, onSelected, options) {
        return Align(
          alignment: AlignmentDirectional.topStart,
          child: Material(
            color: Colors.white,
            elevation: 4.0,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: 600,
                maxWidth: size.width <= 510 ? 340 : 460.toDouble(),
              ),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final option = options.elementAt(index);
                  return InkWell(
                    onTap: () {
                      onSelected(option);
                    },
                    child: Builder(
                      builder: (BuildContext context) {
                        final bool highlight =
                            AutocompleteHighlightedOption.of(context) == index;
                        if (highlight) {
                          SchedulerBinding.instance.addPostFrameCallback((
                            Duration timeStamp,
                          ) {
                            Scrollable.ensureVisible(context, alignment: 0.5);
                          }, debugLabel: 'AutocompleteOptions.ensureVisible');
                        }
                        return Container(
                          color: highlight
                              ? Theme.of(context).focusColor
                              : null,
                          // color: Colors.white,
                          padding: const EdgeInsets.all(16.0),
                          child: getTile(option),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
      optionsBuilder: (textEditingValue) async {
        // setState(() {});
        // //  final Iterable<String>? options=await
        // await getFormsByName(textEditingValue.text);
        // return _lastOptions.map((e) => e.name);
        // // return _lastOptions;
        await getRequirmentSearchData(textEditingValue.text.toLowerCase());
        return requirementSearchResult;
      },
    );
  }
}

class SearchedCategoryTile extends StatelessWidget {
  const SearchedCategoryTile({super.key, required this.option});
  final SearchRequirementModel option;
  @override
  Widget build(BuildContext context) {
    return Text(option.mainCategory?.name.capitalizeFirst ?? "");
  }
}

class SearchedProductTile extends StatelessWidget {
  const SearchedProductTile({super.key, required this.option});
  final SearchRequirementModel option;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return
    // ListTile(
    //   leading: AspectRatio(
    //     aspectRatio: .75,
    //     child: Stack(
    //       children: [
    //         Align(
    //             alignment: Alignment(0, 0),
    //             child: Image.asset('assets/images/TBB-Fix-BG.png')),
    //         Align(
    //           alignment: Alignment(0, 0),
    //           child: Padding(
    //             padding: const EdgeInsets.all(5.0),
    //             child: Image.network('${option.product?.images.first}'),
    //           ),
    //         ),
    //       ],
    //     ),
    //   ),
    //   title: Text(
    //     '${option.product?.name.capitalizeFirst}',
    //     style: TextStyle(fontWeight: FontWeight.w600),
    //   ),
    //   subtitle: Text(
    //     maxLines: 3,
    //     overflow: TextOverflow.ellipsis,
    //     '${option.product?.desc}',
    //     style: TextStyle(fontWeight: FontWeight.w500),
    //   ),
    // );
    Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color.fromARGB(255, 192, 191, 191)),
        ),
      ),
      height: 80,
      width: 200,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: AspectRatio(
              aspectRatio: .75,
              child: SizedBox(
                height: 60,
                child: Stack(
                  children: [
                    Align(
                      alignment: const Alignment(0, 0),
                      child: Image.asset('assets/images/TBB-Fix-BG.png'),
                    ),
                    Align(
                      alignment: const Alignment(0, 0),
                      child: Padding(
                        padding: const EdgeInsets.all(5.0),
                        child: Image.network('${option.product?.images.first}'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(
            width: size.width <= 510 ? 240 : 360,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${option.product?.name.capitalizeFirst}',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: Text(
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    '${option.product?.desc}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class TbbLogo extends StatelessWidget {
  const TbbLogo({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.go(Routes.home);
      },
      child: const Image(
        // width: 200,
        // height: 60,
        fit: BoxFit.cover,
        alignment: Alignment.center,
        image: AssetImage('assets/images/logo2.png'),
      ),
    );
  }
}

class SearchButton extends StatelessWidget {
  const SearchButton({super.key, required this.searchedString});
  final String searchedString;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return IconButton(
      padding: const EdgeInsets.all(0),
      iconSize: size.width <= 510 ? 20 : 25,
      color: size.width <= 510 ? themeColor : Colors.white,
      onPressed: () async {
        if (searchedString.isNotEmpty) {
          if (context.mounted) {
            context.go('${Routes.search}/$searchedString');
          }
        }
      },
      icon: Icon(
        Icons.search,
        color: size.width > 510
            ? Colors.white
            : const Color.fromARGB(255, 218, 149, 149),
      ),
    );
  }
}

class CartButton extends StatefulWidget {
  const CartButton({super.key});

  @override
  State<CartButton> createState() => _CartButtonState();
}

class _CartButtonState extends State<CartButton> {
  int count = 0;
  countItem(pctrl) async {
    count = isLoggedIn()
        ? pctrl.userDetails?.cartitems.length ?? 0
        : pctrl.orderProduct.length;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(
      builder: (pctrl) {
        countItem(pctrl);
        return Padding(
          padding: const EdgeInsets.only(right: 10.0),
          child: IconButton(
            icon: SizedBox(
              height: 30,
              width: 30,
              child: Stack(
                children: [
                  const Icon(CupertinoIcons.bag),
                  Positioned(
                    bottom: 3,
                    right: 0,
                    child: Container(
                      height: 16,
                      width: 16,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Color.fromARGB(255, 207, 138, 138),
                      ),
                      child: Center(
                        child: Text(
                          "$count",
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            onPressed: () => context.go(Routes.cart),
            // Navigator.push(
            //     context,
            //     MaterialPageRoute(
            //       builder: (context) => const CartPage(),
            //     )),
            hoverColor: Colors.transparent,
            highlightColor: Colors.transparent,
          ),
        );
      },
    );
  }
}

class ProfileButton extends StatelessWidget {
  const ProfileButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      onPressed: () => context.go(Routes.auth),
      //  Navigator.push(
      //     context,
      //     MaterialPageRoute(
      //       builder: (context) => const LoginPage(),
      //     )),
      icon: const Icon(CupertinoIcons.person),
    );
    // icon: const Icon(CupertinoIcons.person)));
  }
}

class CakesMenu extends StatefulWidget {
  const CakesMenu({
    super.key,
    // required this.index,
    required this.catsetid,
    required this.overlayController,
  });
  // final int index;
  final List<String> catsetid;
  final OverlayPortalController overlayController;

  @override
  State<CakesMenu> createState() => _CakesMenuState();
}

class _CakesMenuState extends State<CakesMenu> {
  @override
  void initState() {
    super.initState();
  }

  int? hoveredindex;
  int? catindex;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(
      builder: (ctrl) {
        List<CatSetModel> catset = [];
        for (var x in widget.catsetid) {
          catset.addAll(
            ctrl.settings?.catset
                    .where((element) => element.id == x)
                    .toList() ??
                [],
          );
        }
        return Container(
          alignment: Alignment.centerLeft,
          margin: const EdgeInsets.all(50),
          // height: 100,
          // width: MediaQuery.sizeOf(context).width - 200,
          child: Wrap(
            runSpacing: 50,
            spacing: 100,
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(widget.catsetid.length, (index) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    catset[index].name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...List.generate(
                    catset[index].subCats.length,
                    (index2) => Padding(
                      padding: const EdgeInsets.only(bottom: 5.0),
                      child: InkWell(
                        focusColor: const Color(0xff2596be),
                        onHover: (value) {
                          hoveredindex = index2;
                          catindex = index;
                          setState(() {});
                        },
                        onTap: () {
                          context.go(
                            '${Routes.category}/${catset[index].subCats[index2].toLowerCase()}',
                          );
                          widget.overlayController.hide();
                          // overlayController
                          //     .hide();
                          // Navigator.push(
                          //     context,
                          //     MaterialPageRoute(
                          //         builder: (context) => SubcatPage(
                          //             name: catset[index]
                          //                 .subCats[index2])));
                        },
                        child: Text(
                          catset[index].subCats[index2],
                          style: TextStyle(
                            fontSize: 15,
                            color: hoveredindex == index2 && catindex == index
                                ? const Color(0xff30a59e)
                                : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
        );
      },
    );
  }
}

class AppbarMenuItem extends StatefulWidget {
  const AppbarMenuItem({
    super.key,
    required this.name,
    required this.exceed,
    required this.catsetid,
    required this.overlayControllerList,
    required this.index,
    required this.openedIndex,
    required this.selectedIndex,
  });
  final String name;
  final int index;
  final int selectedIndex;
  final Function(int i) openedIndex;
  final bool exceed;
  final List<String> catsetid;
  final List<OverlayPortalController> overlayControllerList;
  @override
  State<AppbarMenuItem> createState() => _AppbarMenuItemState();
}

class _AppbarMenuItemState extends State<AppbarMenuItem> {
  // final overlayController = OverlayPortalController();
  GlobalKey key = GlobalKey();
  int? hoveredindex;
  int? catindex;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        widget.openedIndex(widget.index);
        if (widget.index != widget.selectedIndex) {
          widget.overlayControllerList[0].hide();
          widget.overlayControllerList[1].hide();
          widget.overlayControllerList[2].hide();
          widget.overlayControllerList[3].hide();
          widget.overlayControllerList[widget.index].show();
        } else {
          widget.openedIndex(8);
          widget.overlayControllerList[widget.index].hide();
        }
      },
      child: MouseRegion(
        onEnter: (event) => widget.overlayControllerList[widget.index].show(),
        onExit: (event) => widget.overlayControllerList[widget.index].hide(),
        child: OverlayPortal(
          controller: widget.overlayControllerList[widget.index],
          overlayChildBuilder: (BuildContext context) {
            RenderBox box = key.currentContext?.findRenderObject() as RenderBox;
            Offset position = box.localToGlobal(Offset.zero);
            return Positioned(
              top: position.dy + 40,
              left: position.dx - 45,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 50.0),
                child: MouseRegion(
                  onEnter: (event) =>
                      widget.overlayControllerList[widget.index].show(),
                  onExit: (event) =>
                      widget.overlayControllerList[widget.index].hide(),
                  child: Container(
                    height: widget.exceed
                        ? MediaQuery.sizeOf(context).height - 100
                        : null,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 2,
                          offset: Offset(1, 1.5),
                        ),
                      ],
                    ),
                    child: SingleChildScrollView(
                      child: widget.exceed
                          ? Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CakesMenu(
                                  catsetid: widget.catsetid,
                                  overlayController: widget
                                      .overlayControllerList[widget.index],
                                ),
                              ],
                            )
                          // ? Column(
                          //     mainAxisSize: MainAxisSize.min,
                          //     children: [
                          //       widget.child,
                          //     ],
                          //   )
                          : CakesMenu(
                              catsetid: widget.catsetid,
                              overlayController:
                                  widget.overlayControllerList[widget.index],
                            ),
                    ),
                  ),
                ),
              ),
            );
          },
          child: Container(
            key: key,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            height: 40,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Row(
                    // mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        widget.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const Icon(Icons.keyboard_arrow_down_rounded),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SubCatHoverButton extends StatefulWidget {
  const SubCatHoverButton({super.key, required this.name});

  final String name;

  @override
  State<SubCatHoverButton> createState() => _SubCatHoverButtonState();
}

class _SubCatHoverButtonState extends State<SubCatHoverButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
      },
      child: GestureDetector(
        onTap: () {},
        child: Text(
          widget.name,
          style: TextStyle(color: _isHovered ? Colors.blue : Colors.black),
        ),
      ),
    );
  }

  // class SearchField extends StatefulWidget {
  //   const SearchField({
  //     super.key,
  //     required this.height,
  //   });
  //   final double height;

  //   @override
  //   State<SearchField> createState() => _SearchFieldState();
  // }

  // class _SearchFieldState extends State<SearchField> {
  //   final overlayController = OverlayPortalController();

  //   GlobalKey key = GlobalKey();
  //   @override
  //   Widget build(BuildContext context) {
  //     return Expanded(
  //       child: SizedBox(
  //         height: widget.height,
  //         child: OverlayPortal(
  //           controller: overlayController,
  //           overlayChildBuilder: (BuildContext context) {
  //             RenderBox box = key.currentContext?.findRenderObject() as RenderBox;
  //             Offset position = box.localToGlobal(Offset.zero);
  //             return Positioned(
  //               top: position.dy + 40,
  //               left: position.dx - 45,
  //               child: Padding(
  //                 padding: const EdgeInsets.symmetric(horizontal: 50.0),
  //                 child: MouseRegion(
  //                   onEnter: (event) => overlayController.show(),
  //                   onExit: (event) => overlayController.hide(),
  //                   child: Container(
  //                     height: MediaQuery.sizeOf(context).height - 100,
  //                     decoration: const BoxDecoration(
  //                         color: Colors.black,
  //                         boxShadow: [
  //                           BoxShadow(
  //                               color: Colors.black12,
  //                               blurRadius: 2,
  //                               offset: Offset(1, 1.5))
  //                         ]),
  //                     child: Column(
  //                       children: [
  //                         Expanded(
  //                             child: Scrollbar(
  //                           thumbVisibility: true,
  //                           child:
  //                               SingleChildScrollView(child: Text("dvjvfhefv")),
  //                         )),
  //                       ],
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //             );
  //           },
  //           child: TextFormField(
  //             onChanged: (value) {},
  //             decoration: InputDecoration(
  //               contentPadding:
  //                   const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
  //               suffixIcon: Container(
  //                 height: 50,
  //                 color: themeColor, // Colors.blueGrey,
  //                 width: 45,
  //                 child: const SearchButton(),
  //               ),
  //               border: const OutlineInputBorder(),
  //               enabledBorder: const OutlineInputBorder(
  //                   borderSide: BorderSide(color: themeColor)),
  //               focusedBorder: const OutlineInputBorder(
  //                   borderSide: BorderSide(color: themeColor)),
  //               hintText: 'What are you looking for?',
  //             ),
  //           ),
  //         ),
  //       ),
  //     );
  //   }
}

// getFormsByName(String value) async {
//   try {
//     // await Future.delayed(Duration(milliseconds: 800));
//     // if (searchController.text != value) return;

//     _lastOptions.clear();
//     final searchText = value..toLowerCase();

//     await FBFireStore.product
//         .where(
//           Filter.and(Filter('lower', isGreaterThanOrEqualTo: searchText),
//               Filter('lower', isLessThan: '$searchText\uf8ff')),
//         )
//         .limit(10)
//         .get()
//         .then((event) {
//       print(event.size);
//       final res =
//           event.docs.map((e) => ProductModel.fromSnapshot(e)).toList();

//       for (var element in res) {
//         if (_lastOptions
//                 .firstWhereOrNull((el) => el.docId == element.docId) ==
//             null) {
//           _lastOptions.add(element);
//         }
//       }
//     });

//     if (_lastOptions.length < 3) {
//       if (value != value) return;
//       await FBFireStore.product
//           // .where('tags', arrayContains: searchText)
//           .where('tags',
//               arrayContainsAny: searchText.split(' ').map((e) => e).toList())
//           .limit(8)
//           .get()
//           .then((event) {
//         final res =
//             event.docs.map((e) => ProductModel.fromSnapshot(e)).toList();

//         for (var element in res) {
//           if (_lastOptions
//                   .firstWhereOrNull((el) => el.docId == element.docId) ==
//               null) {
//             _lastOptions.add(element);
//           }
//         }
//       });
//     }
//     setState(() {});
//     return _lastOptions;
//   } catch (e) {
//     debugPrint(e.toString());
//   }
// }
