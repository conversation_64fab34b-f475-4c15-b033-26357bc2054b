import 'package:flutter/material.dart';

const double mobileMinsize = 510; //425
const double tabletMinsize = 768;
const double desktopMinSize = 1024;

class ResponsiveWid extends StatelessWidget {
  const ResponsiveWid({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        // if (constraints.maxWidth <= tabletMinsize) return tablet ?? mobile;
        if (constraints.maxWidth >= desktopMinSize) {
          return desktop ?? tablet ?? mobile;
        }
        if (constraints.maxWidth < desktopMinSize &&
            constraints.maxWidth > mobileMinsize) return tablet ?? mobile;
        return mobile;
      },
    );
  }
}
