import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class CopyRightBar extends StatelessWidget {
  const CopyRightBar({super.key, required this.size, this.fontSize});
  final Size size;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: const Color.fromARGB(255, 27, 27, 28),
      padding: EdgeInsets.symmetric(
          horizontal: size.width * .04, vertical: size.width * .02),
      child: Row(
        children: [
          Expanded(
            child: Text.rich(
              TextSpan(
                text: "© 2024 TBB | Made with ❤ by ",
                style: appTextStyleOne.copyWith(
                  fontSize: fontSize,
                  // fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                children: [
                  developedByText(fontSize),
                  // TextSpan(
                  //   text: " & ",
                  //   style: appTextStyleOne.copyWith(
                  //     // fontWeight: FontWeight.bold,
                  //     color: Colors.white,
                  //   ),
                  // ),
                  // TextSpan(
                  //   text: "Digital Cruze",
                  //   style: appTextStyleOne.copyWith(
                  //     color: Colors.orangeAccent.shade700,
                  //     fontWeight: FontWeight.bold,
                  //   ),
                  //   recognizer: TapGestureRecognizer()..onTap = _onDCTap,
                  // ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

TextSpan developedByText(double? fontsize) {
  return TextSpan(
    text: "Diwizon",
    style: appTextStyleOne.copyWith(
        fontWeight: FontWeight.bold, color: Colors.white, fontSize: fontsize
        // color: Colors.redAccent.shade400,
        ),
    recognizer: TapGestureRecognizer()..onTap = _onTap,
  );
}

_onTap() async {
  try {
    final url = Uri.parse("https://www.diwizon.com");
    if (await canLaunchUrl(url)) {
      launchUrl(url);
    }
  } catch (e) {
    debugPrint(e.toString());
  }
}

const appTextStyleOne = TextStyle(
  letterSpacing: 1,
  fontSize: 16,
);
