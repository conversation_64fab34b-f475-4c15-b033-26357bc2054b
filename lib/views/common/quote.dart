import 'package:flutter/material.dart';
import 'package:tbb_web/shared/theme.dart';
import 'package:tbb_web/views/common/responsive.dart';

class Quote extends StatelessWidget {
  const Quote({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveWid(
      mobile: const SizedBox(),

      tablet: Container(
        height: 80,
        color: themeColor,
        child: const Center(
          child: Text(
            'Your favourite desserts & delights !',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.white,
            ),
          ),
        ),
      ),
      //
      desktop: Container(
        height: 80,
        color: themeColor,
        child: const Center(
          child: Text(
            'Your favourite desserts & delights !',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
