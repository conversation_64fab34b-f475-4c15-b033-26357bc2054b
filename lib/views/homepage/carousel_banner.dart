import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:flutter_animated_button/flutter_animated_button.dart';
import 'package:tbb_web/views/common/responsive.dart';

class CarouselBanner extends StatefulWidget {
  const CarouselBanner({
    super.key,
    required this.scrollCtrl,
    required this.globalkey,
  });
  final ScrollController scrollCtrl;
  final GlobalKey globalkey;
  @override
  State<CarouselBanner> createState() => _CarouselBannerState();
}

class _CarouselBannerState extends State<CarouselBanner> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: ResponsiveWid(
        //mobile
        mobile:
            // Container(
            //   constraints: const BoxConstraints(maxHeight: 160),
            //   child: const Stack(
            //     fit: StackFit.expand,
            //     children: [
            Image(
                // alignment: Alignment(1, 0),
                fit: BoxFit.cover,
                width: double.maxFinite,
                image: AssetImage('assets/images/banner-min.png')),
        // Align(
        //   alignment: const Alignment(.9, .6),
        //   child: AnimatedButton(
        //     height: 35,
        //     width: 100,
        //     text: 'SHOP NOW',
        //     selectedBackgroundColor: Colors.pinkAccent.shade100,
        //     isReverse: true,
        //     selectedTextColor: Colors.white,
        //     transitionType: TransitionType.CENTER_LR_IN,
        //     backgroundColor: Color(0xff7b5049),
        //     onPress: () {},
        //     animatedOn: AnimatedOn.onHover,
        //     textStyle:
        //         const TextStyle(fontSize: 15, color: Colors.white),
        //     animationDuration: const Duration(milliseconds: 300),
        //   ),
        // ),
        //   ],
        // ),
        // ),

        //tablet
        tablet: Image(
            fit: BoxFit.contain,
            width: double.maxFinite,
            image: AssetImage('assets/images/banner-min.png')),
        //dekstop
        desktop: CarouselSlider(
            items: [
              Stack(
                children: [
                  Image.asset(
                    'assets/images/banner-min.png',
                    fit: BoxFit.fitWidth,
                    width: double.maxFinite,
                  ),
                  // Align(
                  //   alignment: const Alignment(-0.9, 0.75),
                  //   child: AnimatedButton(
                  //     height: 35,
                  //     width: 250,
                  //     text: 'SHOP NOW',
                  //     selectedBackgroundColor: Colors.pinkAccent.shade100,
                  //     isReverse: true,
                  //     selectedTextColor: Colors.white,
                  //     transitionType: TransitionType.CENTER_LR_IN,
                  //     backgroundColor: Color(0xff7b5049),
                  //     onPress: () async {
                  //       const dura = Duration(milliseconds: 400);
                  //       await widget.scrollCtrl.animateTo(300,
                  //           duration: dura, curve: Curves.linear);
                  //       await Future.delayed(dura);
                  //       await Future.delayed(const Duration(milliseconds: 10));
                  //       await Scrollable.ensureVisible(
                  //           widget.globalkey.currentContext!,
                  //           curve: Curves.decelerate,
                  //           duration: const Duration(milliseconds: 700));
                  //     },
                  //     animatedOn: AnimatedOn.onHover,
                  //     textStyle:
                  //         const TextStyle(fontSize: 20, color: Colors.white),
                  //     animationDuration: const Duration(milliseconds: 300),
                  //   ),
                  // ),
                ],
              ),
            ],
            options: CarouselOptions(
                viewportFraction: 1.0,
                aspectRatio: 3,
                scrollPhysics: const NeverScrollableScrollPhysics())),
      ),
    );
  }
}
