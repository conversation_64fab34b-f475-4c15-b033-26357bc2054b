import 'dart:math';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/views/common/responsive.dart';
import 'package:tbb_web/views/homepage/display_card1.dart';

class AddOn extends StatefulWidget {
  const AddOn({super.key});

  @override
  State<AddOn> createState() => _AddOnState();
}

List<ProductModel> productlist = [];
List<ProductModel> addonlist = [];
final scrollCTRL = ScrollController();

class _AddOnState extends State<AddOn> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return StreamBuilder(
        stream: FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where('type', isEqualTo: ProductType.addon)
            .limit(10)
            .snapshots()
            .map((event) =>
                event.docs.map((e) => ProductModel.fromSnapshot(e)).toList()),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return const Text("Facing some issue");
          }
          if (snapshot.hasData) {
            addonlist = snapshot.data ?? [];
            return ResponsiveWid(
              mobile: Column(
                children: [
                  SingleChildScrollView(
                    controller: scrollCTRL,
                    physics: const NeverScrollableScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    child: GestureDetector(
                      onHorizontalDragUpdate: (details) {
                        // Handle horizontal drag here if needed
                        // You can use details.primaryDelta to get the horizontal movement
                        try {
                          if (details.primaryDelta != null) {
                            scrollCTRL.position
                                .pointerScroll(-details.primaryDelta!);
                          }
                        } catch (e) {
                          debugPrint(e.toString());
                        }
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: size.width <= mobileMinsize ? 20 : 60),
                        child: Row(
                          children: [
                            ...List.generate(
                                min(addonlist.length, 6),
                                (index) => DisplayCard1(
                                      isCake: false,
                                      product: addonlist[index],
                                    )),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              tablet: Column(
                children: [
                  SingleChildScrollView(
                    controller: scrollCTRL,
                    physics: const NeverScrollableScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    child: GestureDetector(
                      onHorizontalDragUpdate: (details) {
                        // Handle horizontal drag here if needed
                        // You can use details.primaryDelta to get the horizontal movement
                        try {
                          if (details.primaryDelta != null) {
                            scrollCTRL.position
                                .pointerScroll(-details.primaryDelta!);
                          }
                        } catch (e) {
                          debugPrint(e.toString());
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 60),
                        child: Row(
                          children: [
                            ...List.generate(
                                addonlist.length,
                                (index) => DisplayCard1(
                                      isCake: false,
                                      product: addonlist[index],
                                    )),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Card(
                    color: Colors.transparent,
                    shadowColor: Colors.transparent,
                    surfaceTintColor: Colors.transparent,
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(30.0),
                            child: ElevatedButton(
                              onPressed: () {
                                context.go('${Routes.allproducts}/Add-On');
                              },
                              style: ButtonStyle(
                                backgroundColor:
                                    WidgetStatePropertyAll(Colors.pink[300]),
                                shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none)),
                              ),
                              child: const Text(
                                "VIEW ALL",
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          )
                        ]),
                  ),
                ],
              ),
              desktop: Column(
                children: [
                  SingleChildScrollView(
                    controller: scrollCTRL,
                    physics: const NeverScrollableScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    child: GestureDetector(
                      onHorizontalDragUpdate: (details) {
                        // Handle horizontal drag here if needed
                        // You can use details.primaryDelta to get the horizontal movement
                        try {
                          if (details.primaryDelta != null) {
                            scrollCTRL.position
                                .pointerScroll(-details.primaryDelta!);
                          }
                        } catch (e) {
                          debugPrint(e.toString());
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 60),
                        child: Row(
                          children: [
                            ...List.generate(
                                addonlist.length,
                                (index) => DisplayCard1(
                                      isCake: false,
                                      product: addonlist[index],
                                    )),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Card(
                    color: Colors.transparent,
                    shadowColor: Colors.transparent,
                    surfaceTintColor: Colors.transparent,
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(30.0),
                            // padding: const EdgeInsets.all(100.0),
                            child: ElevatedButton(
                              onPressed: () {
                                context.go('${Routes.allproducts}/Add-On');
                              },
                              style: ButtonStyle(
                                backgroundColor:
                                    WidgetStatePropertyAll(Colors.pink[300]),
                                shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none)),
                              ),
                              child: const Text(
                                "VIEW ALL",
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          )
                        ]),
                  ),
                ],
              ),
            );
          } else {
            return const Center(
              child: SizedBox(
                  height: 30, width: 30, child: CircularProgressIndicator()),
            );
          }
        });
  }
}
