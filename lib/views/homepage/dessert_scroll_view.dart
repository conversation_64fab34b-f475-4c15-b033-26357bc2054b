import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/views/homepage/display_card1.dart';
import 'package:tbb_web/views/common/responsive.dart';

class DessertScrollView extends StatefulWidget {
  const DessertScrollView({
    super.key,
  });

  @override
  State<DessertScrollView> createState() => _DessertScrollViewState();
}

class _DessertScrollViewState extends State<DessertScrollView> {
  List<ProductModel> productlistdessert = [];
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      // productlistdessert = pctrl.products;
      // productlistdessert = pctrl.products.where(
      //   (element) {
      //     return element.isdessert == true;
      //   },
      // ).toList();
      productlistdessert = pctrl.products
          .where((element) => element.type == ProductType.dessert)
          .toList();
//       for(ProductModel x in productlistdessert){
// print(x.)
//       }
      // productlistdessert
      // .removeWhere((element) => element.type==ProductType.dessert);
      // print("dessert-${productlistdessert.length}");
      // print("dessertttt-${pctrl.products.length}");
      return ResponsiveWid(
        //mobile
        mobile: Column(
          children: [
            SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  ...List.generate(
                      productlistdessert.length,
                      (index) => DisplayCard1(
                            isCake: true,
                            product: productlistdessert[index],
                          )),
                ],
              ),
            ),
            Card(
              color: Colors.transparent,
              shadowColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: ElevatedButton(
                        onPressed: () {},
                        style: ButtonStyle(
                          backgroundColor:
                              WidgetStatePropertyAll(Colors.pink[300]),
                          shape: const WidgetStatePropertyAll(
                              LinearBorder(side: BorderSide.none)),
                        ),
                        child: const Text(
                          "VIEW ALL",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                  ]),
            ),
          ],
        ),
        //tablet
        tablet: Column(
          children: [
            SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 25),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  ...List.generate(
                      productlistdessert.length,
                      (index) => DisplayCard1(
                            isCake: true,
                            product: productlistdessert[index],
                          )),
                ],
              ),
            ),
            Card(
              color: Colors.transparent,
              shadowColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(30.0),
                      child: ElevatedButton(
                        onPressed: () {},
                        style: ButtonStyle(
                          backgroundColor:
                              WidgetStatePropertyAll(Colors.pink[300]),
                          shape: const WidgetStatePropertyAll(
                              LinearBorder(side: BorderSide.none)),
                        ),
                        child: const Text(
                          "VIEW ALL",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                  ]),
            ),
          ],
        ),
        //desktop
        desktop: Column(
          children: [
            SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 60),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  ...List.generate(
                      productlistdessert.length,
                      (index) => DisplayCard1(
                          product: productlistdessert[index], isCake: true)),
                ],
              ),
            ),
            Card(
              color: Colors.transparent,
              shadowColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(30.0),
                      child: ElevatedButton(
                        onPressed: () {},
                        style: ButtonStyle(
                          backgroundColor:
                              WidgetStatePropertyAll(Colors.pink[300]),
                          shape: const WidgetStatePropertyAll(
                              LinearBorder(side: BorderSide.none)),
                        ),
                        child: const Text(
                          "VIEW ALL",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                  ]),
            ),
          ],
        ),
      );
    });
  }
}
