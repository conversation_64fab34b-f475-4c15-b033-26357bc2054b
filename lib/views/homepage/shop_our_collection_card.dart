import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/Models/our_collection.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/views/common/responsive.dart';

class SOCcard extends StatefulWidget {
  const SOCcard({
    super.key,
    this.product,
    required this.index,
    this.ourcollection,
  });
  final int index;
  final ProductModel? product;
  final OurCollectionModel? ourcollection;

  @override
  State<SOCcard> createState() => _SOCcardState();
}

class _SOCcardState extends State<SOCcard> {
  String name = "";

  @override
  Widget build(BuildContext context) {
    return ResponsiveWid(
      mobile: InkWell(
        onTap: () {
          return context.go(
              '${Routes.category}/${widget.ourcollection?.id.toLowerCase()}');
        },
        mouseCursor: SystemMouseCursors.click,
        child: Card(
          color: Colors.white,
          elevation: 0,
          // margin: const EdgeInsets.only(right: 20, left: 20, bottom: 15),
          shape: const ContinuousRectangleBorder(),
          child: SizedBox(
            height: 200,
            width: 150,
            child: AspectRatio(
              // height: 400,
              aspectRatio: 4 / 5,
              // width: 250,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Image(
                  //     // height: 400,
                  //     // width: 300,
                  //     fit: BoxFit.cover,
                  //     image: NetworkImage(widget.ourcollection?.image ?? ""
                  //         // widget.product?.images.first ?? ""

                  //         )),
                  Image.network(
                    widget.ourcollection?.image ?? "",
                    fit: BoxFit.cover,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        width: double.maxFinite,
                        // padding: const EdgeInsets.all(10),
                        // margin: const EdgeInsets.only(left: 10, right: 10),
                        color: Colors.white70,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.ourcollection?.name.toUpperCase() ?? "",
                              // Get.find<CategoryCtrl>()
                              //         .settings
                              //         ?.catset[widget.index]
                              //         .subCats
                              //         .last
                              //         .toUpperCase() ??
                              //     "",
                              // product?.name.toUpperCase() ?? "",
                              style: GoogleFonts.roboto(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16),
                            ),
                            ElevatedButton(
                              onPressed: () => context.go(
                                  '${Routes.category}/${widget.ourcollection?.id.toLowerCase()}'),
                              style: ButtonStyle(
                                elevation: const WidgetStatePropertyAll(0),
                                backgroundColor: WidgetStatePropertyAll(
                                    Colors.pinkAccent.shade100),
                                shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none)),
                              ),
                              child: Text(
                                "SHOP NOW",
                                style: GoogleFonts.roboto(
                                  fontSize: 10,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      tablet: InkWell(
        onTap: () {
          return context.go(
              '${Routes.category}/${widget.ourcollection?.id.toLowerCase()}');
        },
        mouseCursor: SystemMouseCursors.click,
        child: Card(
          color: Colors.white,
          elevation: 0,
          // margin: const EdgeInsets.only(right: 20, left: 20, bottom: 15),
          shape: const ContinuousRectangleBorder(),
          child: SizedBox(
            height: 300,
            width: 240,
            child: AspectRatio(
              // height: 400,
              aspectRatio: 4 / 5,
              // width: 250,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Image(
                  //     // height: 400,
                  //     // width: 300,
                  //     fit: BoxFit.fill,
                  //     image: NetworkImage(widget.ourcollection?.image ?? ""
                  //         // widget.product?.images.first ?? ""

                  //         )),
                  Image.network(
                    widget.ourcollection?.image ?? "",
                    fit: BoxFit.cover,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        width: double.maxFinite,
                        // padding: const EdgeInsets.all(10),
                        // margin: const EdgeInsets.only(left: 10, right: 10),
                        color: Colors.white70,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.ourcollection?.name.toUpperCase() ?? "",
                              // Get.find<CategoryCtrl>()
                              //         .settings
                              //         ?.catset[widget.index]
                              //         .subCats
                              //         .last
                              //         .toUpperCase() ??
                              //     "",
                              // product?.name.toUpperCase() ?? "",
                              style: GoogleFonts.roboto(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 20),
                            ),
                            ElevatedButton(
                              onPressed: () => context.go(
                                  '${Routes.category}/${widget.ourcollection?.id.toLowerCase()}'),
                              style: ButtonStyle(
                                elevation: const WidgetStatePropertyAll(0),
                                backgroundColor: WidgetStatePropertyAll(
                                    Colors.pinkAccent.shade100),
                                shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none)),
                              ),
                              child: Text(
                                "SHOP NOW",
                                style: GoogleFonts.roboto(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      desktop: InkWell(
        onTap: () {
          return context.go(
              '${Routes.category}/${widget.ourcollection?.id.toLowerCase()}');
        },
        mouseCursor: SystemMouseCursors.click,
        child: Card(
          color: Colors.white,
          elevation: 0,
          // margin: const EdgeInsets.only(right: 20, left: 20, bottom: 15),
          shape: const ContinuousRectangleBorder(),
          child: SizedBox(
            height: 340,
            width: 270,
            child: Stack(
              fit: StackFit.expand,
              children: [
                // Image(
                //     // height: 500,
                //     // width: 300,
                //     fit: BoxFit.cover,
                //     image: NetworkImage(widget.ourcollection?.image ?? ""
                //         // widget.product?.images.first ?? ""

                //         )),
                Image.network(
                  widget.ourcollection?.image ?? "",
                  fit: BoxFit.cover,
                ),
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      width: double.maxFinite,
                      // padding: const EdgeInsets.all(10),
                      // margin: const EdgeInsets.only(left: 10, right: 10),
                      color: Colors.white70,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.ourcollection?.name.toUpperCase() ?? "",
                            // Get.find<CategoryCtrl>()
                            //         .settings
                            //         ?.catset[widget.index]
                            //         .subCats
                            //         .last
                            //         .toUpperCase() ??
                            //     "",
                            // product?.name.toUpperCase() ?? "",
                            style: GoogleFonts.roboto(
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                                fontSize: 20),
                          ),
                          ElevatedButton(
                            onPressed: () => context.go(
                                '${Routes.category}/${widget.ourcollection?.id.toLowerCase()}'),
                            style: ButtonStyle(
                              elevation: const WidgetStatePropertyAll(0),
                              backgroundColor: WidgetStatePropertyAll(
                                  Colors.pinkAccent.shade100),
                              shape: const WidgetStatePropertyAll(
                                  LinearBorder(side: BorderSide.none)),
                            ),
                            child: Text(
                              "SHOP NOW",
                              style: GoogleFonts.roboto(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
