import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/views/homepage/display_card1.dart';

class TcScrollView extends StatefulWidget {
  const TcScrollView({
    super.key,
  });

  @override
  State<TcScrollView> createState() => _TcScrollViewState();
}

class _TcScrollViewState extends State<TcScrollView> {
  final scrollCtrl = ScrollController();
  // List<ProductModel> productlist = [];

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return FutureBuilder(
        future: FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where('type', isEqualTo: ProductType.trendingcake)
            .limit(8)
            .get()
            .then((value) =>
                value.docs.map((e) => ProductModel.fromSnapshot(e)).toList()),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return Text("Facng some issue!");
          }
          if (snapshot.hasData) {
            return Column(
              children: [
                SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: scrollCtrl,
                  padding: EdgeInsets.symmetric(
                      vertical: 0, horizontal: size.width <= 510 ? 20 : 60),
                  scrollDirection: Axis.horizontal,
                  child: GestureDetector(
                    onHorizontalDragUpdate: (details) {
                      try {
                        if (details.primaryDelta != null) {
                          scrollCtrl.position
                              .pointerScroll(-details.primaryDelta!);
                        }
                      } catch (e) {
                        debugPrint(e.toString());
                      }
                    },
                    child: Row(
                      children: [
                        ...List.generate(min(snapshot.data?.length ?? 0, 10),
                            (index) {
                          return DisplayCard1(
                              product: snapshot.data?[index], isCake: true);
                        }),
                      ],
                    ),
                  ),
                ),
                if (size.width > 510)
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: ElevatedButton(
                      onPressed: () {
                        context.go('${Routes.allproducts}/Trending Cake');
                        ;
                      },
                      style: ButtonStyle(
                        backgroundColor:
                            WidgetStatePropertyAll(Colors.pink[300]),
                        shape: const WidgetStatePropertyAll(
                            LinearBorder(side: BorderSide.none)),
                      ),
                      child: const Text(
                        "VIEW ALL",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  )
              ],
            );
          } else {
            return Center(
                child: SizedBox(
              height: 30,
              width: 30,
              child: CircularProgressIndicator(),
            ));
          }
        });
  }
}
