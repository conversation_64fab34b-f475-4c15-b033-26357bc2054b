import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/Models/discount_coupon.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/shared/theme.dart';
import 'package:tbb_web/views/common/responsive.dart';
import 'package:tbb_web/views/homepage/home_page.dart';

String getFlashDealPrice(num prodPriceNum) {
  {
    DiscountCouponModel? coupon = Get.find<ProductCtrl>()
        .settings
        ?.coupons
        .firstWhereOrNull((element) => element.couponCode == "FLASHDEAL");
    if (coupon != null) {
      num discount = 0;
      if (coupon.isPercentage) {
        if (coupon.minOrderValue <= prodPriceNum) {
          discount = (prodPriceNum * (coupon.value / 100)).ceil();

          if (discount > coupon.maxDiscount) {
            discount = coupon.maxDiscount;
          } else {
            discount = discount;
          }
        }
      } else {
        if (coupon.minOrderValue <= prodPriceNum) {
          discount = coupon.value;
        }
      }

      return (prodPriceNum - discount).toString();
    } else {
      return prodPriceNum.toString();
    }
  }
}

class DisplayCard1 extends StatefulWidget {
  const DisplayCard1({
    super.key,
    this.product,
    required this.isCake,
  });
  final ProductModel? product;
  final bool isCake;

  @override
  State<DisplayCard1> createState() => _DisplayCard1State();
}

class _DisplayCard1State extends State<DisplayCard1> {
  // bool isdessert = true;
  bool staring75 = false;
  bool inDeal = false;
  // void trackFacebookEvent(String eventName,
  //     [Map<String, dynamic>? parameters]) {
  //   if (parameters != null) {
  //     js.context.callMethod('fbq', ['track', eventName, parameters]);
  //   } else {
  //     js.context.callMethod('fbq', ['track', eventName]);
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      final size = MediaQuery.sizeOf(context);
      final designCost = pctrl.designCost.firstWhereOrNull(
          (element) => element.docId == widget.product?.designCostdocId);
      final weightrange = pctrl.weightRange.firstWhereOrNull(
          (element) => element.docId == widget.product?.weightRangeDocId);
      final intialFlavour = pctrl.flavours
          .firstWhereOrNull((element) => element.name == "Chocolate Truffle");
      staring75 = weightrange?.weightlist.first == 0.75 ? true : false;
      String prodPrice = widget.isCake
          ? "${(designCost?.basePrice ?? 0) + ((widget.product?.type == "Regular Cake" ? ((widget.product?.fixedprice ?? 0) * (weightrange?.weightlist[0].toString() == "0.5" ? 2 : 1)) : ((intialFlavour?.perKgPrice ?? 0) * 2)) * ((staring75 ? weightrange?.weightlist[1] : weightrange?.weightlist.first) ?? 0)) + (staring75 ? designCost?.increment ?? 0 : 0)}"
          : "${widget.product?.fixedprice}";
      String originalPrice = prodPrice;

      if (widget.product?.catNameList?.contains("discount") ?? false) {
        inDeal = true;
        prodPrice = getFlashDealPrice(num.tryParse(prodPrice) ?? 0);
      }
      return InkWell(
        onTap: () {
          context.go('${Routes.product}/${widget.product?.docId}');
        },
        mouseCursor: SystemMouseCursors.click,
        child: Card(
          color: themeColorLite,
          elevation: 0,
          shape: const ContinuousRectangleBorder(),
          child: SizedBox(
            height: size.width <= mobileMinsize
                ? (!widget.isCake)
                    ? 280
                    : 260
                : (!widget.isCake)
                    ? 430
                    : 410,
            // height: isCake ? 470 : 390,
            //temporary width given because of the cake name extending the container
            width: size.width <= mobileMinsize ? 150 : 250,
            // width: isCake ? 300 : 250,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AspectRatio(
                  aspectRatio: 0.75,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      Image.asset(
                        "assets/images/TBB-Fix-BG.png",
                        fit: BoxFit.cover,
                        // width: size.width <= mobileMinsize ? 150 : 250,
                        // height: size.width <= mobileMinsize ? 200 : 334,
                      ),
                      Image.network(
                        widget.product?.images.isEmpty ?? true
                            ? ""
                            : widget.product?.images.first ?? "",
                        fit: BoxFit.cover,
                      )
                      // Padding(
                      //   padding: const EdgeInsets.all(0.0),
                      //   child:
                      //   Image(
                      //     // width: 300,
                      //     // height: 400,
                      //     fit: BoxFit.cover,
                      //     image: NetworkImage(
                      //         widget.product?.images.isEmpty ?? true
                      //             ? ""
                      //             : widget.product?.images.first ?? ""),
                      //   ),
                      // ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10, top: 5),
                  child: Text(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                    widget.product?.name.toUpperCase() ?? "",
                    style: GoogleFonts.rethinkSans(
                      color: const Color(0xff000000),
                      fontSize: size.width <= mobileMinsize ? 14 : 16,
                      letterSpacing: .5,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (!widget.isCake)
                  Padding(
                    padding: const EdgeInsets.only(left: 10, bottom: 3),
                    child: Text(
                      overflow: TextOverflow.ellipsis,
                      widget.product?.desc ?? "",
                      style: GoogleFonts.rethinkSans(
                          color: const Color(0xff000000),
                          fontSize: size.width <= mobileMinsize ? 14 : 18,
                          // letterSpacing: 1,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                Padding(
                  padding: const EdgeInsets.only(left: 10, bottom: 10),
                  child: Row(
                    children: [
                      if (inDeal) ...[
                        Text(
                          "Rs.$originalPrice",
                          // ? "Rs.${product?.fixedprice.toString() ?? ""}"
                          // : "Rs.${designCost?.basePrice.toString() ?? ""}",
                          // "Rs.${product?.fixedprice}",
                          // "Rs.${designCost?.basePrice.toString() ?? ""}",
                          style: GoogleFonts.rethinkSans(
                              decoration: TextDecoration.lineThrough,
                              color: const Color.fromARGB(255, 126, 126, 126),
                              fontSize: size.width <= mobileMinsize ? 12 : 16,
                              letterSpacing: 1,
                              fontWeight: FontWeight.w700),
                        ),
                        SizedBox(
                          width: 5,
                        )
                      ],
                      Text(
                        "Rs.$prodPrice",
                        // ? "Rs.${product?.fixedprice.toString() ?? ""}"
                        // : "Rs.${designCost?.basePrice.toString() ?? ""}",
                        // "Rs.${product?.fixedprice}",
                        // "Rs.${designCost?.basePrice.toString() ?? ""}",
                        style: GoogleFonts.rethinkSans(
                            color: const Color(0xff000000),
                            fontSize: size.width <= mobileMinsize ? 14 : 18,
                            letterSpacing: 1,
                            fontWeight: FontWeight.w700),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
