import 'dart:math';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/views/homepage/display_card1.dart';
import 'package:tbb_web/views/common/responsive.dart';

class CakesScrollView extends StatefulWidget {
  const CakesScrollView({
    super.key,
  });

  @override
  State<CakesScrollView> createState() => _CakesScrollViewState();
}

class _CakesScrollViewState extends State<CakesScrollView> {
  List<ProductModel> productlist = [];
  dataFetch() async {
    productlist = await FBFireStore.product
        .where('notavailable', isEqualTo: false)
        .where('type', isEqualTo: ProductType.regularCake)
        .limit(8)
        .get()
        .then((value) =>
            value.docs.map((e) => ProductModel.fromSnapshot(e)).toList());
  }

  final scrlCtrl = ScrollController();
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: dataFetch(),
        builder: (context, snapshot) {
          // productlist = pctrl.products
          //     .where((element) => element.type == ProductType.regularCake)
          //     .toList();
          // print(" Regular cakes length - ${productlist.length}");
          return ResponsiveWid(
            //mobile
            mobile: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  ...List.generate(min(productlist.length, 10), (index) {
                    return DisplayCard1(
                        product: productlist[index], isCake: true);
                  }),
                ],
              ),
            ),
            //tablet
            tablet: Column(
              children: [
                SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  padding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 25),
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      ...List.generate(productlist.length, (index) {
                        return DisplayCard1(
                            product: productlist[index], isCake: true);
                      }),
                    ],
                  ),
                ),
                Card(
                  color: Colors.transparent,
                  shadowColor: Colors.transparent,
                  surfaceTintColor: Colors.transparent,
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: ElevatedButton(
                            onPressed: () {
                              context.go('${Routes.allproducts}/Regular Cake');
                            },
                            style: ButtonStyle(
                              backgroundColor:
                                  WidgetStatePropertyAll(Colors.pink[300]),
                              shape: const WidgetStatePropertyAll(
                                  LinearBorder(side: BorderSide.none)),
                            ),
                            child: const Text(
                              "VIEW ALL",
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        )
                      ]),
                ),
              ],
            ),
            //desktop
            desktop: Column(
              children: [
                SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: scrlCtrl,
                  padding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 60),
                  scrollDirection: Axis.horizontal,
                  child: GestureDetector(
                    onHorizontalDragUpdate: (details) {
                      try {
                        if (details.primaryDelta != null) {
                          scrlCtrl.position
                              .pointerScroll(-details.primaryDelta!);
                        }
                      } catch (e) {
                        debugPrint(e.toString());
                      }
                    },
                    child: Row(
                      children: [
                        ...List.generate(productlist.length, (index) {
                          return DisplayCard1(
                              product: productlist[index], isCake: true);
                        }),
                      ],
                    ),
                  ),
                ),
                Card(
                  color: Colors.transparent,
                  shadowColor: Colors.transparent,
                  surfaceTintColor: Colors.transparent,
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 100.0, vertical: 30),
                          child: ElevatedButton(
                            onPressed: () {
                              context.go('${Routes.allproducts}/Regular Cake');
                            },
                            style: ButtonStyle(
                              backgroundColor:
                                  WidgetStatePropertyAll(Colors.pink[300]),
                              shape: const WidgetStatePropertyAll(
                                  LinearBorder(side: BorderSide.none)),
                            ),
                            child: const Text(
                              "VIEW ALL",
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        )
                      ]),
                ),
              ],
            ),
          );
        });
  }
}
