import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:tbb_web/Models/catsetmodel.dart';
import 'package:tbb_web/Models/maincategorymodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/router.dart';

class CustomDrawer extends StatefulWidget {
  const CustomDrawer({
    super.key,
    required GlobalKey<ScaffoldState> scafKey,
    this.name,
    this.catSet,
    this.tags,
    this.onTapFunct,
  });

  final MainCategoryModel? name;
  final Function()? onTapFunct;
  final MainCategoryModel? catSet;
  final MainCategoryModel? tags;

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  ExpansionTileController expCtrl = ExpansionTileController();
  ExpansionTileController expCtrl2 = ExpansionTileController();
  int selectedTile = 1;
  // int selectedTile2 = 1;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(
      builder: (ctrl) {
        MediaQuery.sizeOf(context);
        return Drawer(
          shape: Border.all(style: BorderStyle.none),
          // width: 300,-
          width: double.maxFinite,
          shadowColor: Colors.transparent,
          backgroundColor: Colors.white,
          child: ListView(
            children: [
              const SizedBox(height: 20),
              Theme(
                data: ThemeData(dividerColor: Colors.transparent),
                child: ExpansionTile(
                  backgroundColor: Colors.white,
                  childrenPadding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 15,
                  ),
                  dense: true,
                  trailing: const Icon(Icons.keyboard_arrow_down_rounded),
                  title: const Text(
                    "SHOP NOW",
                    style: TextStyle(
                      fontWeight: FontWeight.w900,
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                  controller: expCtrl,
                  onExpansionChanged: (value) {
                    if (value) {
                      expCtrl2.collapse();
                    }
                  },
                  children: [
                    ...List.generate(ctrl.maincategory.length, (index) {
                      return DrawerExpanisonTile(
                        selectedTile: selectedTile,
                        index: index,
                        text: ctrl.maincategory[index].name,
                        onExp: (value) {
                          if (value) {
                            setState(() {
                              selectedTile = index;
                            });
                          }
                        },
                        children: [
                          DrwawerCat(
                            onTapFunct: widget.onTapFunct,
                            catsetid: ctrl.maincategory[index].catSet,
                            refresh: () {
                              setState(() {});
                            },
                          ),
                        ],
                      );
                    }),
                  ],
                ),
              ),
              Theme(
                data: ThemeData(dividerColor: Colors.transparent),
                child: ExpansionTile(
                  controller: expCtrl2,
                  onExpansionChanged: (value) {
                    if (value) {
                      expCtrl.collapse();
                    }
                  },
                  expandedAlignment: const Alignment(-1, 0),
                  backgroundColor: Colors.white,
                  childrenPadding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 10,
                  ),
                  dense: true,
                  trailing: const Icon(Icons.keyboard_arrow_down_rounded),
                  title: const Text(
                    "CUSTOMER SERVICE",
                    style: TextStyle(
                      fontWeight: FontWeight.w900,
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                  children: [const CUTOMERSERVICEEXPANSION()],
                ),
              ),
              const SizedBox(height: 30),
              if (!isLoggedIn())
                const _DrawerTile(name: "SIGN IN/REGISTER", route: Routes.auth),
              if (isLoggedIn())
                const _DrawerTile(name: "MY ACCOUNT", route: Routes.auth),
              // const _DrawerTile(
              //   name: "ALL PRODUCTS",
              //   route: '${Routes.allproducts}/All',
              // ),
              const _DrawerTile(name: "TRACK ORDER", route: Routes.account),
              if (isLoggedIn())
                ListTile(
                  onTap: () {
                    Scaffold.of(context).closeDrawer();
                    showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          title: const Text('Logout?'),
                          actions: <Widget>[
                            TextButton(
                              style: TextButton.styleFrom(
                                textStyle: Theme.of(
                                  context,
                                ).textTheme.labelLarge,
                              ),
                              child: const Text('NO'),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                            TextButton(
                              style: TextButton.styleFrom(
                                textStyle: Theme.of(
                                  context,
                                ).textTheme.labelLarge,
                              ),
                              child: const Text('YES'),
                              onPressed: () {
                                FBAuth.auth.signOut();
                                if (context.mounted) {
                                  context.go(Routes.home);
                                  Navigator.of(context).pop();
                                }
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                  title: const Text(
                    "SIGN OUT",
                    style: TextStyle(
                      fontWeight: FontWeight.w900,
                      color: Colors.black,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

class CUTOMERSERVICEEXPANSION extends StatefulWidget {
  const CUTOMERSERVICEEXPANSION({super.key});

  @override
  State<CUTOMERSERVICEEXPANSION> createState() =>
      _CUTOMERSERVICEEXPANSIONState();
}

class _CUTOMERSERVICEEXPANSIONState extends State<CUTOMERSERVICEEXPANSION> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 20.0),
              child: InkWell(
                onTap: () {
                  // context.go(
                  // '${Routes.category}/${catset[index].subCats[index2]}');
                },
                child: const Text(
                  "CONTACT US",
                  style: TextStyle(
                    fontWeight: FontWeight.w900,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 20.0),
              child: InkWell(
                onTap: () {
                  // context.go(
                  // '${Routes.category}/${catset[index].subCats[index2]}');
                },
                child: const Text(
                  "SHIPPING DELIVERY",
                  style: TextStyle(
                    fontWeight: FontWeight.w900,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 20.0),
              child: InkWell(
                onTap: () {
                  // context.go(
                  // '${Routes.category}/${catset[index].subCats[index2]}');
                },
                child: const Text(
                  "RETURNS & EXCHANGE",
                  style: TextStyle(
                    fontWeight: FontWeight.w900,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 10.0),
              child: InkWell(
                onTap: () {
                  // context.go(
                  // '${Routes.category}/${catset[index].subCats[index2]}');
                },
                child: const Text(
                  "REFUND POLICY",
                  style: TextStyle(
                    fontWeight: FontWeight.w900,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DrawerExpanisonTile extends StatefulWidget {
  const DrawerExpanisonTile({
    super.key,
    required this.text,
    required this.children,
    required this.index,
    required this.onExp,
    required this.selectedTile,
  });
  final String text;
  final int index;
  final int selectedTile;
  final List<Widget> children;
  final Function(bool) onExp;

  @override
  State<DrawerExpanisonTile> createState() => _DrawerExpanisonTileState();
}

class _DrawerExpanisonTileState extends State<DrawerExpanisonTile> {
  final eCtrl = ExpansionTileController();
  @override
  Widget build(BuildContext context) {
    // print(widget.selectedTile == widget.index);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      try {
        // print("object ${widget.selectedTile}");

        if (widget.selectedTile != widget.index && eCtrl.isExpanded) {
          eCtrl.collapse();
        }
      } catch (e) {
        debugPrint(e.toString());
      }
    });
    return Theme(
      data: ThemeData(dividerColor: Colors.transparent),
      child: ExpansionTile(
        // initiallyExpanded: widget.selectedTile == widget.index,
        // key: Key(widget.index.toString()),
        onExpansionChanged: widget.onExp,
        maintainState: true,
        controller: eCtrl,
        backgroundColor: Colors.white,
        childrenPadding: const EdgeInsets.symmetric(vertical: 10),
        dense: true,
        trailing: const Icon(Icons.keyboard_arrow_down_rounded),
        title: Text(
          widget.text.toUpperCase(),
          style: const TextStyle(
            fontWeight: FontWeight.w900,
            fontSize: 16,
            color: Colors.black,
          ),
        ),
        children: widget.children,
      ),
    );
  }
}

class _DrawerTile extends StatelessWidget {
  const _DrawerTile({required this.name, required this.route});

  final String name;
  final String route;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {
        Scaffold.of(context).closeDrawer();
        context.go(route);
      },
      title: Text(
        name.toUpperCase(),
        style: const TextStyle(
          fontWeight: FontWeight.w900,
          color: Colors.black,
        ),
      ),
      // trailing: const CupertinoListTileChevron(),
    );
  }
}

class DrwawerCat extends StatefulWidget {
  const DrwawerCat({
    super.key,
    required this.catsetid,
    required this.refresh,
    this.onTapFunct,
  });
  final List<String> catsetid;
  final Function refresh;
  final Function()? onTapFunct;

  @override
  State<DrwawerCat> createState() => _DrwawerCatState();
}

class _DrwawerCatState extends State<DrwawerCat> {
  @override
  void initState() {
    super.initState();
  }

  int? catindex;
  int selectedTile = 1;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(
      builder: (ctrl) {
        List<CatSetModel> catset = [];
        for (var x in widget.catsetid) {
          catset.addAll(
            ctrl.settings?.catset
                    .where((element) => element.id == x)
                    .toList() ??
                [],
          );
        }
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
          child: Wrap(
            runSpacing: 10,
            spacing: 20,
            children: List.generate(widget.catsetid.length, (index) {
              return ThirdLevelExpansion(
                onTapFunct: widget.onTapFunct,
                catsetid: widget.catsetid,
                index: index,
                refresh: widget.refresh,
                catset: catset,
                onExp: (value) {
                  if (value) {
                    setState(() {
                      selectedTile = index;
                    });
                  }
                },
                selectedTile: selectedTile,
              );
            }),
          ),
        );
      },
    );
  }
}

class ThirdLevelExpansion extends StatefulWidget {
  const ThirdLevelExpansion({
    super.key,
    required this.catsetid,
    required this.refresh,
    required this.index,
    required this.catset,
    required this.onExp,
    required this.selectedTile,
    this.onTapFunct,
  });
  final List<String> catsetid;
  final Function refresh;
  final int index;
  final List<CatSetModel> catset;
  final Function(bool) onExp;
  final int selectedTile;
  final Function()? onTapFunct;

  @override
  State<ThirdLevelExpansion> createState() => _ThirdLevelExpansionState();
}

class _ThirdLevelExpansionState extends State<ThirdLevelExpansion> {
  final eCtrl = ExpansionTileController();

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      try {
        // print("object ${widget.selectedTile}");

        if (widget.selectedTile != widget.index && eCtrl.isExpanded) {
          eCtrl.collapse();
        }
      } catch (e) {
        debugPrint(e.toString());
      }
    });
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ExpansionTile(
          // key: ValueKey(widget..index),
          controller: eCtrl,
          onExpansionChanged: (value) {
            widget.onExp(value);
            widget.refresh();
          },
          expandedAlignment: const Alignment(-1, 0),
          backgroundColor: Colors.white,
          childrenPadding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 10,
          ),
          dense: true,
          trailing: const Icon(Icons.keyboard_arrow_down_rounded),
          title: Text(
            widget.catset[widget.index].name.toUpperCase(),
            style: const TextStyle(
              fontWeight: FontWeight.w900,
              fontSize: 15,
              color: Colors.black,
            ),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 20.0,
                vertical: 10,
              ),
              child: Container(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ...List.generate(
                      widget.catset[widget.index].subCats.length,
                      (index2) => Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: InkWell(
                          onTap: () {
                            context.go(
                              '${Routes.category}/${widget.catset[widget.index].subCats[index2].toLowerCase()}',
                            );
                            widget.onTapFunct!();
                          },
                          child: Text(
                            widget.catset[widget.index].subCats[index2],
                            style: const TextStyle(
                              fontSize: 15,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
