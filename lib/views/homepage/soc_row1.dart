import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/const.dart';
import 'package:tbb_web/views/homepage/shop_our_collection_card.dart';
import 'package:tbb_web/views/common/responsive.dart';

class SocRow1 extends StatelessWidget {
  const SocRow1({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      // print("category-${pctrl.products.length}");
      return ResponsiveWid(
        //mobile
        mobile: Padding(
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 15),
          child: Wrap(
            // crossAxisAlignment: CrossAxisAlignment.center,
            // mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...List.generate(
                homeCatList.length,
                (index) => SOCcard(
                  // product: homeCatList[index],
                  index: index, ourcollection: homeCatList[index],
                ),
              ),
            ],
          ),
        ),
        //tablet
        tablet: Padding(
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
          child: Wrap(
            // crossAxisAlignment: CrossAxisAlignment.center,
            // mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...List.generate(
                homeCatList.length,
                (index) => SOCcard(
                  // product: homeCatList[index],
                  ourcollection: homeCatList[index],
                  index: index,
                ),
              ),
            ],
          ),
        ),
        //desktop
        desktop: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 200.0, vertical: 20),
          child: Wrap(
            children: [
              ...List.generate(homeCatList.length, (index) {
                return SOCcard(
                  index: index,
                  // product: homeCatList[index],
                  ourcollection: homeCatList[index],
                );
              }),
            ],
          ),
        ),
      );
    });
  }
}
