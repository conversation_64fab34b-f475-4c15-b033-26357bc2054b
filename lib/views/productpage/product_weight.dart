import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tbb_web/Models/weight_range_model.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/theme.dart';

class ProductWeight extends StatefulWidget {
  const ProductWeight({
    super.key,
    required this.productlist,
    required this.onChanged,
  });
  // final ProductModel? product;
  final WeightRangeModel? productlist;
  final Function(num?, int) onChanged;

  @override
  State<ProductWeight> createState() => _ProductWeightState();
}

class _ProductWeightState extends State<ProductWeight> {
  int selectedindexweight = 0;
  bool isthird = true;
  bool isintial75 = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    isintial75 = widget.productlist?.weightlist.first == 0.75;
    if (isintial75) {
      isthird = false;
      selectedindexweight = 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 10.0, bottom: 10),
        child: Wrap(
          children: List.generate(
            widget.productlist?.weightlist.length ?? 0,
            (index) => Padding(
              padding: const EdgeInsets.only(right: 10, bottom: 10, top: 10),
              child: OutlinedButton(
                onPressed: () {
                  setState(() {
                    selectedindexweight = index;
                    isthird = false;
                  });
                  widget.onChanged(
                      widget.productlist?.weightlist[selectedindexweight],
                      index);
                  // selectedweight =
                  // widget.productlist?.weightlist[selectedindexweight];
                  //  indexcountweight = selectedindexweight;

                  pctrl.update();
                },
                style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                        horizontal: size.width <= 510 ? 5 : 20,
                        vertical: size.width <= 510 ? 5 : 10),
                    backgroundColor: index == selectedindexweight
                        ? themeColor.withOpacity(.05)
                        : isthird && index == 0
                            ? themeColor.withOpacity(.05)
                            : null,
                    side: BorderSide(
                        color: index == selectedindexweight
                            ? themeColor
                            : Colors.grey.shade600),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(3))),
                child: Text(
                  '${widget.productlist?.weightlist[index].toString()} KG',
                  style: TextStyle(
                      fontSize: size.width <= 510 ? 12 : 14,
                      color: index == selectedindexweight
                          ? Colors.pink[200]
                          : isthird && index == 0
                              ? Colors.pink[200]
                              : Colors.black,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}
