import 'package:flutter/material.dart';
import 'package:flutter_animated_button/flutter_animated_button.dart';
import 'package:tbb_web/views/cartpage/cart_page.dart';

class AddToCartButton extends StatelessWidget {
  const AddToCartButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedButton(
      animationDuration: const Duration(milliseconds: 300),
      text: "ADD TO CART",
      animatedOn: AnimatedOn.onHover,
      transitionType: TransitionType.RIGHT_TO_LEFT,
      backgroundColor: Colors.black,
      selectedBackgroundColor: Colors.pinkAccent,
      selectedTextColor: Colors.black,
      onPress: () => Navigator.push(
          context, MaterialPageRoute(builder: (context) => const CartPage())),
    );
  }
}
