import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:url_launcher/url_launcher_string.dart';

class ProductHelpContainer extends StatelessWidget {
  const ProductHelpContainer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            "NEED HELP?",
            style: TextStyle(
                color: Color.fromARGB(255, 218, 118, 151),
                fontSize: 20,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          const Text(
            "For design modification or any query",
            style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          const Text(
            "Please feel free to contact us",
            style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                  style: const ButtonStyle(
                      overlayColor: WidgetStatePropertyAll(Colors.transparent)),
                  onPressed: () {
                    launchUrlString('tel:${pctrl.settings?.contact}');
                  },
                  child: const Image(
                      height: 28,
                      image: AssetImage("assets/images/telephone.png"))),
              // IconButton(
              //   onPressed: () {},
              //   icon: const Icon(Icons.call),
              // ),
              TextButton(
                  style: const ButtonStyle(
                      overlayColor: WidgetStatePropertyAll(Colors.transparent)),
                  onPressed: () {
                    final String url =
                        'https://wa.me/${pctrl.settings?.contact}';
                    launchUrlString(url);
                  },
                  child: const Image(
                      height: 24,
                      image: AssetImage("assets/images/whatsappgreen.png")))
              // Text("+91-${pctrl.settings?.contact ?? ""}"),
            ],
          ),
        ],
      );
    });
  }
}
