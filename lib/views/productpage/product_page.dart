import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/views/common/responsive.dart';
import 'package:tbb_web/views/productpage/product_details.dart';
import 'package:tbb_web/views/productpage/product_help_container.dart';
import 'package:tbb_web/views/productpage/product_image.dart';
import 'package:tbb_web/wrapper.dart';

class Productpage extends StatefulWidget {
  const Productpage({super.key, required this.product});
  // final ProductModel? product;
  final String product;

  @override
  State<Productpage> createState() => _ProductpageState();
}

class _ProductpageState extends State<Productpage> {
  int? selectedindex;
  ProductModel? pduct;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    trackFacebookEvent('ViewContent', {
      'content_id': widget.product,
      'content_type': 'product',
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where(FieldPath.documentId, isEqualTo: widget.product)
            .get()
            .then((value) => value.docs.isEmpty
                ? null
                : ProductModel.fromSnapshot(value.docs.first)),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return Text("Error");
          }
          if (snapshot.hasData) {
            pduct = snapshot.data;
            return Wrapper(
              body: ResponsiveWid(
                //mobile
                mobile: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                            ),
                            ProductImage(product: pduct),
                            IconButton(
                                padding: EdgeInsets.all(0),
                                onPressed: () async {
                                  final url = '${Uri.base}';
                                  print(url);
                                  await Share.share(
                                      'Check out this product $url');
                                },
                                icon: Icon(Icons.share_outlined)),
                          ],
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Row(
                          children: [
                            ProductDetails(
                              product: pduct,
                              isSmall: true,
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                        const ProductHelpContainer(),
                      ],
                    ),
                  ),
                ),

                //tablet

                tablet: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(25.0),
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ProductImage(product: pduct),
                            IconButton(
                                padding: EdgeInsets.all(0),
                                onPressed: () async {
                                  final url = '${Uri.base}';
                                  print(url);
                                  await Share.share(
                                      'Check out this product $url');
                                },
                                icon: Icon(Icons.share_outlined)),
                          ],
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Row(
                          children: [
                            ProductDetails(
                              isSmall: true,
                              product: pduct,
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                        const ProductHelpContainer(),
                      ],
                    ),
                  ),
                ),

                //desktop

                desktop: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(25.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              ProductImage(
                                product: pduct,
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              const ProductHelpContainer(),
                            ],
                          ),
                        ),
                        ProductDetails(
                          isSmall: false,
                          product: pduct,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        });
  }
}
// Padding(
//                                     padding: const EdgeInsets.only(right: 15.0),
//                                     child: IconButton(
//                                         onPressed: () async {
//                                           final url = '${Uri.base}';
//                                           print(url);
//                                           await Share.share(
//                                               'Check out this product $url');
//                                         },
//                                         icon: Icon(Icons.share_outlined)),
//                                   )
