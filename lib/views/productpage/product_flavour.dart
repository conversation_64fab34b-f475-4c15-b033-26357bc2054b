import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tbb_web/Models/flavourmodel.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/theme.dart';

class ProductFlavour extends StatefulWidget {
  const ProductFlavour({
    super.key,
    required this.product,
    required this.onChanged,
    required this.isSmall,
  });
  final ProductModel? product;
  final Function(FlavourModel) onChanged;
  final bool isSmall;

  @override
  State<ProductFlavour> createState() => _ProductWeightState();
}

class _ProductWeightState extends State<ProductFlavour> {
  bool isFourth = true;
  bool onselected = true;

  List<FlavourModel?> chocolateList = [];
  List<FlavourModel?> nonChocolatelist = [];
  bool isChocolateSelected = false;
  bool isNonChocolateSelected = false;
  FlavourModel? selectedFlavour = Get.find<ProductCtrl>()
      .flavours
      .firstWhereOrNull((element) => element.name == "Chocolate Truffle");
  FlavourModel? selectedNonchocolateFlavour;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      chocolateList.clear();
      nonChocolatelist.clear();
      for (var flr in widget.product?.flavour ?? []) {
        FlavourModel? flavour =
            pctrl.flavours.firstWhereOrNull((element) => element.docId == flr);
        if (flavour != null) {
          if (flavour.ischocolate) {
            chocolateList.add(flavour);
          } else {
            nonChocolatelist.add(flavour);
          }
        }
      }

      return Padding(
        padding: const EdgeInsets.only(top: 0.0, bottom: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            widget.isSmall
                ? Column(
                    children: [
                      if (chocolateList.isNotEmpty)
                        Container(
                          width: 250,
                          decoration: BoxDecoration(
                            shape: BoxShape.rectangle,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: DropdownButtonFormField<FlavourModel>(
                            style: const TextStyle(color: Colors.black),
                            focusColor: Colors.white,
                            dropdownColor: Colors.white,
                            decoration: InputDecoration(
                              filled: true,
                              fillColor: themeColor.withOpacity(.05),
                              labelStyle: const TextStyle(color: Colors.black),
                              labelText:
                                  // isChocolateSelected
                                  'Chocolate Flavours',
                              // : 'Non-Chocolate Flavours',
                              border: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color:
                                      onselected ? Colors.black : Colors.black,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color:
                                      onselected ? Colors.black : Colors.black,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color:
                                      onselected ? Colors.black : Colors.black,
                                ),
                              ),
                            ),
                            value: selectedFlavour,
                            onChanged: (FlavourModel? newValue) {
                              // pctrl.selectedFlavour = newValue;
                              if (newValue == null) return;
                              selectedNonchocolateFlavour = null;

                              widget.onChanged(newValue);
                              selectedFlavour = newValue;
                              setState(() {});
                              // setState(() {
                              // ProductDetails.indexcountflavour = newValue?.name;
                              // });
                            },
                            items: (chocolateList)
                                // (isChocolateSelected ? chocolateList : nonChocolatelist)
                                // .where((flavour) => flavour != null)
                                .map(
                                    (flavour) => DropdownMenuItem<FlavourModel>(
                                          value: flavour,
                                          child: Text(flavour?.name ?? ''),
                                        ))
                                .toList(),
                          ),
                        ),
                      const SizedBox(height: 15),
                      if (nonChocolatelist.isNotEmpty)
                        Container(
                          width: 250,
                          decoration: BoxDecoration(
                            shape: BoxShape.rectangle,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: DropdownButtonFormField<FlavourModel>(
                            style: const TextStyle(color: Colors.black),
                            focusColor: Colors.white,
                            dropdownColor: Colors.white,
                            decoration: InputDecoration(
                              filled: true,
                              fillColor: themeColor.withOpacity(.05),
                              labelStyle: const TextStyle(color: Colors.black),
                              labelText:
                                  //  isChocolateSelected
                                  //     ?
                                  // 'Chocolate Flavours'
                                  // :
                                  'Non-Chocolate Flavours',
                              border: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color:
                                      onselected ? Colors.black : Colors.black,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color:
                                      onselected ? Colors.black : Colors.black,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color:
                                      onselected ? Colors.black : Colors.black,
                                ),
                              ),
                            ),
                            value: selectedNonchocolateFlavour,
                            onChanged: (FlavourModel? newValue) {
                              // pctrl.selectedFlavour = newValue;
                              if (newValue == null) return;
                              selectedFlavour = null;
                              widget.onChanged(newValue);
                              selectedNonchocolateFlavour = newValue;
                              setState(() {});
                              // setState(() {
                              // ProductDetails.indexcountflavour = newValue?.name;
                              // });
                            },
                            items: (nonChocolatelist)
                                // (isChocolateSelected ? chocolateList : nonChocolatelist)
                                // .where((flavour) => flavour != null)
                                .map(
                                    (flavour) => DropdownMenuItem<FlavourModel>(
                                          value: flavour,
                                          child: Text(flavour?.name ?? ''),
                                        ))
                                .toList(),
                          ),
                        ),
                    ],
                  )
                : Row(children: [
                    if (chocolateList.isNotEmpty)
                      Container(
                        width: 250,
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: DropdownButtonFormField<FlavourModel>(
                          style: const TextStyle(color: Colors.black),
                          focusColor: Colors.white,
                          dropdownColor: Colors.white,
                          decoration: InputDecoration(
                            filled: true,
                            fillColor: themeColor.withOpacity(.05),
                            labelStyle: const TextStyle(color: Colors.black),
                            labelText:
                                // isChocolateSelected
                                'Chocolate Flavours',
                            // : 'Non-Chocolate Flavours',
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: onselected ? Colors.black : Colors.black,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: onselected ? Colors.black : Colors.black,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: onselected ? Colors.black : Colors.black,
                              ),
                            ),
                          ),
                          value: selectedFlavour,
                          onChanged: (FlavourModel? newValue) {
                            // pctrl.selectedFlavour = newValue;
                            if (newValue == null) return;
                            selectedNonchocolateFlavour = null;

                            widget.onChanged(newValue);
                            selectedFlavour = newValue;
                            setState(() {});
                            // setState(() {
                            // ProductDetails.indexcountflavour = newValue?.name;
                            // });
                          },
                          items: (chocolateList)
                              // (isChocolateSelected ? chocolateList : nonChocolatelist)
                              // .where((flavour) => flavour != null)
                              .map((flavour) => DropdownMenuItem<FlavourModel>(
                                    value: flavour,
                                    child: Text(flavour?.name ?? ''),
                                  ))
                              .toList(),
                        ),
                      ),
                    const SizedBox(width: 15),
                    if (nonChocolatelist.isNotEmpty)
                      Container(
                        width: 250,
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: DropdownButtonFormField<FlavourModel>(
                          style: const TextStyle(color: Colors.black),
                          focusColor: Colors.white,
                          dropdownColor: Colors.white,
                          decoration: InputDecoration(
                            filled: true,
                            fillColor: themeColor.withOpacity(.05),
                            labelStyle: const TextStyle(color: Colors.black),
                            labelText:
                                //  isChocolateSelected
                                //     ?
                                // 'Chocolate Flavours'
                                // :
                                'Non-Chocolate Flavours',
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: onselected ? Colors.black : Colors.black,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: onselected ? Colors.black : Colors.black,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: onselected ? Colors.black : Colors.black,
                              ),
                            ),
                          ),
                          value: selectedNonchocolateFlavour,
                          onChanged: (FlavourModel? newValue) {
                            // pctrl.selectedFlavour = newValue;
                            if (newValue == null) return;
                            selectedFlavour = null;
                            widget.onChanged(newValue);
                            selectedNonchocolateFlavour = newValue;
                            setState(() {});
                            // setState(() {
                            // ProductDetails.indexcountflavour = newValue?.name;
                            // });
                          },
                          items: (nonChocolatelist)
                              // (isChocolateSelected ? chocolateList : nonChocolatelist)
                              // .where((flavour) => flavour != null)
                              .map((flavour) => DropdownMenuItem<FlavourModel>(
                                    value: flavour,
                                    child: Text(flavour?.name ?? ''),
                                  ))
                              .toList(),
                        ),
                      ),
                  ]),
          ],
        ),
      );
    });
  }
}




 // if (chocolateList.isNotEmpty)
              //   OutlinedButton(
              //       style: OutlinedButton.styleFrom(
              //           shape: RoundedRectangleBorder(
              //               side: BorderSide(
              //                   color:
              //                       onselected ? Colors.black : Colors.black),
              //               borderRadius: BorderRadius.circular(3))),
              //       onPressed: () {
              //         selectedFlavour = null;
              //         setState(() {
              //           isChocolateSelected = true;
              //           isNonChocolateSelected = false;
              //         });
              //       },
              //       child: Text(
              //         "Chocolate",
              //         style: TextStyle(
              //           color: onselected ? Colors.black : Colors.black,
              //         ),
              //       )),
              // const SizedBox(width: 9),
              // if (nonChocolatelist.isNotEmpty)
              //   OutlinedButton(
              //       style: OutlinedButton.styleFrom(
              //           shape: RoundedRectangleBorder(
              //               side: BorderSide(
              //                   color: onselected ? themeColor : Colors.black),
              //               borderRadius: BorderRadius.circular(3))),
              //       onPressed: () {
              //         selectedFlavour = null;
              //         setState(() {
              //           isChocolateSelected = false;
              //           isNonChocolateSelected = true;

              //           // pctrl.update();
              //         });
              // },
              // child: Text(
              //   "Non-Chocolate",
              //   style: TextStyle(
              //     color: onselected ? Colors.black : Colors.black,
              //   ),
              // )),


 // if (isChocolateSelected || isNonChocolateSelected)

// chocolateList.clear();
// productlist.clear();
// nonChocolateist.clear();
// for (var flr in widget.product?.flavour ?? []) {
//   productlist.add(
//       pctrl.flavours.firstWhereOrNull((element) => element.docId == flr));
//   chocolateList.addAll(pctrl.flavours
//       .where((element) =>
//           element.docId == flr && element.ischocolate == true)
//       .toList());
//   nonChocolateist.addAll(pctrl.flavours.where(
//       (element) => element.docId == flr && element.ischocolate == false));
// }
// if (chocolateList.isNotEmpty) {
//   dropdownValuechcolate = chocolateList.first;
// }
// if (nonChocolateist.isNotEmpty) {
//   dropdownValuenonchoclate = nonChocolateist.first;
// }

// Container(
//   width: 180,
//   decoration: BoxDecoration(
//     shape: BoxShape.rectangle,
//     borderRadius: BorderRadius.circular(10),
//     // color: const Color(0xfffbedbd),
//   ),
//   child: DropdownButtonFormField<FlavourModel>(
//     style: TextStyle(color: Colors.pink[200]),
//     focusColor: Colors.white,
//     dropdownColor: Colors.white,
//     decoration: InputDecoration(
//       labelText: 'Non-Chocolate',
//       filled: true,
//       fillColor: themeColor.withOpacity(.05),
//       border: OutlineInputBorder(
//           borderSide: BorderSide(
//               color: onselected ? themeColor : Colors.black)),
//       enabledBorder: OutlineInputBorder(
//           borderSide: BorderSide(
//               color: onselected ? themeColor : Colors.black)),
//       focusedBorder: OutlineInputBorder(
//           borderSide: BorderSide(
//               color: onselected ? themeColor : Colors.black)),
//     ),
//     value: dropdownValuenonchoclate,
//     onChanged: (FlavourModel? newValue) {
//       setState(() {
//         dropdownValuenonchoclate = newValue;
//         dropdownValuechcolate = null;
//         pctrl.indexcountflavour = newValue?.name;
//       });
//     },
// items: pctrl.flavours
//     .where((flavour) => !flavour.ischocolate)
//     .map((flavour) => DropdownMenuItem<FlavourModel>(
//           value: flavour,
//           child: Text(flavour.name.toString()),
//         ))
//     .toList(),
//   ),
// ),

// pctrl.flavours
//         .where((flavour) => !flavour.ischocolate)
//         .map((flavour) => DropdownMenuItem<FlavourModel>(
//               value: flavour,
//               child: Text(flavour.name.toString()),
//             ))
//         .toList(),
// Row(
//   children: [
//     chocolateList.isNotEmpty
//         ? DropdownMenu<FlavourModel>(
//             onSelected: (FlavourModel? value) {
//               // This is called when the user selects an item.
//               setState(() {
//                 dropdownValuechcolate = value!;
//               });
//             },
//             dropdownMenuEntries: chocolateList
//                 .map<DropdownMenuEntry<FlavourModel>>(
//                     (FlavourModel? value) {
//               return DropdownMenuEntry<FlavourModel>(
//                   value: value!, label: value.name.toString());
//             }).toList(),
//           )
//         : SizedBox(),
//     const SizedBox(width: 10),
//     nonChocolateist.isNotEmpty
//         ? DropdownMenu<FlavourModel>(
//             onSelected: (FlavourModel? value) {
//               // This is called when the user selects an item.
//               setState(() {
//                 dropdownValuenonchoclate = value!;
//               });
//             },
//             dropdownMenuEntries: nonChocolateist
//                 .map<DropdownMenuEntry<FlavourModel>>(
//                     (FlavourModel? value) {
//               return DropdownMenuEntry<FlavourModel>(
//                   value: value!, label: value.name.toString());
//             }).toList(),
//           )
//         : SizedBox(),
//   ],
// )

//     Wrap(
//   children: List.generate(
//     productlist.length,
//     (index) => Padding(
//       padding: const EdgeInsets.only(right: 10, bottom: 10, top: 10),
//       // child:
//       // InkWell(
//       //   onTap: () {
//       //     setState(() {
//       //       selectedindex = index;
//       //     });
//       //   },
//       child: OutlinedButton(
//         onPressed: () {
//           setState(() {
//             selectedindexflavour = index;
//             isFourth = false;
//           });
//           pctrl.flavourprice = productlist[index]?.perKgPrice;
//           pctrl.indexcountflavour = index;
//           pctrl.update();
//         },
//         style: OutlinedButton.styleFrom(
//             backgroundColor: index == selectedindexflavour
//                 ? themeColor.withOpacity(.05)
//                 : isFourth && index == 0
//                     ? themeColor.withOpacity(.05)
//                     : null,
//             side: BorderSide(
//                 color: index == selectedindexflavour
//                     ? themeColor
//                     : Colors.grey.shade600),
//             shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(3))),
//         child: Text(
//           '${productlist[index]?.name}',
//           style: TextStyle(
//               color: index == selectedindexflavour
//                   ? Colors.pink[200]
//                   : isFourth && index == 0
//                       ? Colors.pink[200]
//                       : Colors.black,
//               fontWeight: FontWeight.bold),
//         ),
//       ),
//       // child: Container(
//       //   decoration: BoxDecoration(
//       //     color: index == selectedindex ? themeColor : Colors.white,
//       //     shape: BoxShape.rectangle,
//       //     border: const Border.fromBorderSide(BorderSide()),
//       //     borderRadius: const BorderRadius.all(Radius.circular(6)),
//       //   ),
//       //   width: 80,
//       //   height: 40,
//       //   child: Center(
//       //       child:
//       //       Text(
//       //     '${productlist?.weightlist[index].toString()} KG',
//       //     style: TextStyle(
//       //         color:
//       //             index == selectedindex ? Colors.white : Colors.black),
//       //   )),
//       // ),
//       // ),
//     ),
//   ),
// ),
