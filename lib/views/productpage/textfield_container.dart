import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class TextFieldContainer extends StatefulWidget {
  const TextFieldContainer({
    super.key,
    required this.ctrlMessage,
  });

  final TextEditingController ctrlMessage;
  @override
  State<TextFieldContainer> createState() => _TextFieldContainerState();
}

class _TextFieldContainerState extends State<TextFieldContainer> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0, top: 10),
      child: Container(
        // width: 500,
        decoration: BoxDecoration(
            boxShadow: List.empty(),
            border: Border.all(),
            borderRadius: const BorderRadius.all(Radius.circular(8))),
        child: Row(
          children: [
            const Padding(
              padding: EdgeInsets.all(20.0),
              child: Icon(CupertinoIcons.pen),
            ),
            Expanded(
              child: TextField(
                controller: widget.ctrlMessage,
                decoration: const InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    hintText: "Message on cake(OPTIONAL)"),
              ),
            )
          ],
        ),
      ),
    );
  }
}
