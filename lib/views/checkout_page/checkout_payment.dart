import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:razorpay_web/razorpay_web.dart';
import 'package:tbb_web/Models/addressmodel.dart';
import 'package:tbb_web/Models/cart_items.dart';
import 'package:tbb_web/Models/order_model.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/Models/user_details.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/razorpay.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/shared/theme.dart';
import 'package:tbb_web/views/checkout_page/checkout_items_col.dart';
import 'package:tbb_web/views/checkout_page/checkout_shipping.dart';
import 'package:tbb_web/views/common/responsive.dart';

class CheckOutPayment extends StatefulWidget {
  const CheckOutPayment({
    super.key,
    required this.id,
    required this.instructions,
  });
  final String id;
  final String instructions;
  @override
  State<CheckOutPayment> createState() => _CheckOutPaymentState();
}

class _CheckOutPaymentState extends State<CheckOutPayment> {
  // OrderModel? orderm;
  ProductModel? productm;
  bool isPaid = false;
  Userdetails? userdetails;
  AddressModel? addressDetail;
  final List<String> pricechargelist = ["A"];
  String? selectedprice = 'A';
  CartItemsModel? cartitems;
  int shippingPrice = 0;
  num shippinareaperice = 0;
  String? orderId;
  bool loading = false;
  num finalPrice = 0;
  num couponvalue = 0;
  String coupon = "";
  int tax = 0;
  bool paying = false;
  @override
  void initState() {
    super.initState();

    trackFacebookEvent('AddPaymentInfo', {
      'value': "Payment info added", // No direct monetary value
    });
  }

  paymentListners(RazorpayPG pg, String orderDocId, OrderModel order) {
    pg.razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS,
        (PaymentSuccessResponse response) {
      log('Success Response: $response');
      showAppSnack("PAYMENT SUCCESSFUL");

      if (context.mounted) {
        context.go(Routes.thankyouPage, extra: order);
      }
      // Get.find<ProductCtrl>().onPaymentSuccess(orderDocId, response);
      // FBFunctions.ff.httpsCallable('verifyPayment');
    });
    pg.razorpay.on(Razorpay.EVENT_PAYMENT_ERROR,
        (PaymentFailureResponse response) {
      log('Error Response: ${response.code} - ${response.message}');
      setState(() => orderId = null);
      showAppSnack("PAYMENT FAILED: ${response.message}");
    });
    pg.razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, (response) {
      log('External SDK Response: $response');
      // showAppSnack("EXTERNAL SDK");
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      addressDetail = pctrl.userDetails?.address
          .firstWhere((element) => element.docId == widget.id);
      shippinareaperice = pctrl.settings?.delcharges
              .firstWhereOrNull((element) =>
                  element.name.toLowerCase().trim() ==
                  addressDetail?.area.toLowerCase().trim())
              ?.price ??
          0;

      final x = (1920 - size.width) / 2;
      userdetails = pctrl.userDetails;
      List<CartItemsModel> test = [];
      test.addAll(Get.find<ProductCtrl>().userDetails?.cartitems ?? []);
      List<CartItem> cartItems = [];
      for (int i = 0; i < test.length; i++) {
        cartItems.add(CartItem(
            (i + 1).toString(),
            'Product$i',
            DateTime(test[i].delDate.year, test[i].delDate.month,
                test[i].delDate.day),
            test[i].deliveryTime,
            test[i].delivery));
      } // Standard Delivery
      DeliveryPricing pricing =
          DeliveryPricing(areaPrice: shippinareaperice.toInt());

      shippingPrice = pricing.calculateShippingPrice(cartItems);

      getProductPrice();
      tax = ((pctrl.totalPrice * 5) / 100).ceil();
      pctrl.finalPrice = pctrl.totalPrice + (shippingPrice.toInt()) + (tax);
      pctrl.finalPrice = pctrl.finalPrice - pctrl.couponvalue;
      return Scaffold(
          body: userdetails == null
              ? const Center(
                  child: SizedBox(
                      height: 30,
                      width: 30,
                      child: CircularProgressIndicator()))
              : Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            SizedBox(
                                height: size.width <= 510 ? 100 : 200,
                                width: double.infinity,
                                child: Stack(
                                  fit: StackFit.expand,
                                  children: [
                                    Image.asset(
                                      "assets/images/common-banner 3.png",
                                      fit: BoxFit.fill,
                                    ),
                                    Align(
                                      child: Text(
                                        "CHECK OUT PAYMENT",
                                        style: GoogleFonts.crimsonPro(
                                            fontSize:
                                                size.width <= 510 ? 24 : 34,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    )
                                  ],
                                )),
                            ResponsiveWid(
                              mobile: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left: x < 280 ? 300.0 - x : 50,
                                        top: 20,
                                        bottom: 20,
                                        right: 50),
                                    child: shippingCol(context),
                                  ),
                                  ExpansionTile(
                                    initiallyExpanded: true,
                                    title: Row(
                                      children: [
                                        Text("Show Order Summary",
                                            style: GoogleFonts.aBeeZee(
                                                fontSize: 14)),
                                        // const Icon(Icons.keyboard_arrow_down),
                                      ],
                                    ),
                                    leading: const Icon(
                                      CupertinoIcons.cart,
                                      color: Colors.black,
                                      weight: Checkbox.width,
                                    ),
                                    // trailing: Text(
                                    //   "Rs.",
                                    //   style: GoogleFonts.b612Mono(fontSize: 20),
                                    // ),
                                    children: [
                                      Container(
                                        color: const Color(0xfff5f5f5),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                              left: 20.0,
                                              top: 20,
                                              bottom: 20,
                                              right: 20),
                                          child: CartItems(
                                            from: "3",
                                            shippingprice: shippingPrice,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              tablet: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left: x < 280 ? 300.0 - x : 50,
                                        top: 20,
                                        bottom: 20,
                                        right: 50),
                                    child: shippingCol(context),
                                  ),
                                  ExpansionTile(
                                    initiallyExpanded: true,
                                    title: Row(
                                      children: [
                                        Text("Show Order Summary",
                                            style: GoogleFonts.aBeeZee(
                                                fontSize: 20)),
                                        const Icon(Icons.keyboard_arrow_down),
                                      ],
                                    ),
                                    leading: const Icon(
                                      CupertinoIcons.cart,
                                      color: Colors.black,
                                      weight: Checkbox.width,
                                    ),
                                    trailing: Text(
                                      "Rs.",
                                      style: GoogleFonts.b612Mono(fontSize: 20),
                                    ),
                                    children: [
                                      Container(
                                        color: const Color(0xfff5f5f5),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                              left: 50.0,
                                              top: 20,
                                              bottom: 20,
                                              right: x < 380 ? 400 - x : 100),
                                          child: CartItems(
                                            from: "3",
                                            shippingprice: shippingPrice,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              desktop: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                          left: x < 280 ? 300.0 - x : 50,
                                          top: 20,
                                          bottom: 20,
                                          right: 50),
                                      child: shippingCol(context),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      constraints:
                                          const BoxConstraints(minHeight: 800),
                                      color: const Color(0xfff5f5f5),
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                            left: 50.0,
                                            top: 20,
                                            bottom: 20,
                                            right: x < 380 ? 400 - x : 100),
                                        child: CartItems(
                                          from: "3",
                                          shippingprice: shippingPrice,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: size.width >= 1024 ? 15 : 12),
                      decoration: const BoxDecoration(
                          color: themeColorLite,
                          boxShadow: [
                            BoxShadow(color: Colors.black12, blurRadius: 6)
                          ]),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          if (size.width >= 1024)
                            const Spacer(
                              flex: 1,
                            ),
                          InkWell(
                            onTap: () {
                              context.go(
                                  '${Routes.checkoutshipping}/${widget.id}');
                            },
                            child: Row(
                              children: [
                                Icon(
                                  color: const Color(0xffe9867f),
                                  Icons.arrow_back_ios_outlined,
                                  size: size.width <= 510 ? 12 : 15,
                                ),
                                const SizedBox(width: 5),
                                const Text(
                                  "Return to Shipping",
                                  style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xffe9867f)),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(
                            flex: 2,
                          ),
                          paying
                              ? Center(
                                  child: SizedBox(
                                      height: 30,
                                      width: 30,
                                      child: CircularProgressIndicator()))
                              : ElevatedButton(
                                  style: ButtonStyle(
                                      backgroundColor:
                                          const WidgetStatePropertyAll(
                                              Color(0xffe9867f)),
                                      padding: const WidgetStatePropertyAll(
                                          EdgeInsets.symmetric(
                                              vertical: 20, horizontal: 20)),
                                      shape: WidgetStatePropertyAll(
                                          RoundedRectangleBorder(
                                              side: const BorderSide(
                                                  color: Color(0xffe9867f)),
                                              borderRadius:
                                                  BorderRadius.circular(5)))),
                                  onPressed: () async {
                                    if (userdetails?.cartitems.isEmpty ??
                                        true) {
                                      Fluttertoast.showToast(
                                          msg: "Cart is Empty!!");
                                      return;
                                    }
                                    try {
                                      paying = true;
                                      setState(() {});
                                      final ctrl = Get.find<ProductCtrl>();

                                      final data = <String, dynamic>{
                                        // "docId": widget.id,
                                        "orderProductsMap": {
                                          for (CartItemsModel item
                                              in userdetails?.cartitems ?? [])
                                            item.id: item.toJson()
                                        },
                                        // "time": cartitems?.delDate.toIso8601String(),
                                        "address": addressDetail?.address,
                                        "subTotal": ctrl.totalPrice,
                                        "gst": cartitems?.price,
                                        "total": ctrl.finalPrice,
                                        "shippingPrice": shippingPrice,
                                        "transId": widget.id,
                                        "isPaid": isPaid,
                                        "paidOn": null,
                                        "oId": getRandomId(6), //widget.id,
                                        "uId": userdetails?.docId,
                                        "discountCoupon": pctrl.coupon == null
                                            ? {}
                                            : ctrl.coupon!.toMap(),
                                        "note": widget.instructions,
                                        "name": addressDetail?.name,
                                        "phone": addressDetail?.phone,
                                        "email": addressDetail?.email,
                                        "pincode": addressDetail?.pincode,
                                        "area": addressDetail?.area,
                                        'createdAt': DateTime.now(),
                                        'status': 'Unfulfilled',
                                        'orderNo': "-",
                                        "fulfilledNote": "Unfulfilled",
                                        // "delDate": ctrl.delDate,
                                        // "deliveryTime": ctrl.deliveryTime,
                                        // "delivery": ctrl.delivery,
                                      };

                                      final user =
                                          FirebaseAuth.instance.currentUser;
                                      if (user != null) {
                                        final res =
                                            await FBFireStore.order.add(data);
                                        // if (true) {
                                        //   await FBFireStore.users
                                        //       .doc(user.uid)
                                        //       .update({
                                        //     'cartitems': {},
                                        //   });

                                        //   // ctrl.area = null;
                                        //   // ctrl.isLateNight = null;
                                        //   // ctrl.delDate = null;
                                        //   // ctrl.deliveryTime = null;
                                        // }
                                        orderId = res.id;
                                        setState(() {});
                                        final pg = RazorpayPG();

                                        await pg.createRazorpayOrder(
                                          orderDocId: res.id,
                                          receiptNo: res.id,
                                          amount: ctrl.finalPrice.toDouble(),
                                          // amount: (subtotal + tax).truncateToDouble(),
                                        );
                                        OrderModel order = await res.get().then(
                                            (value) =>
                                                OrderModel.fromDocSnapshot(
                                                    value));

                                        await paymentListners(
                                            pg, res.id, order);
                                        // context.go(Routes.home); // Uncomment this line if using the context for navigation
                                      } else {
                                        print(
                                            "No user is currently signed in.");
                                      }
                                      paying = false;
                                      setState(() {});
                                    } catch (e) {
                                      orderId = null;
                                      print("Failed to update order: $e");
                                    }

                                    //
                                  },
                                  child: Text(
                                    "Complete Order",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: size.width <= 510 ? 12 : 15,
                                    ),
                                  )),
                          if (size.width >= 1024)
                            const Spacer(
                              flex: 1,
                            ),
                        ],
                      ),
                    ),
                  ],
                ));
    });
  }

  Column shippingCol(BuildContext context) {
    List<String> x = [];
    for (CartItemsModel element in userdetails?.cartitems ?? []) {
      x.add(element.delivery);
      x.toSet();
    }

    final size = MediaQuery.sizeOf(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            InkWell(
              onTap: () {
                context.go(Routes.cart);
              },
              child: Text(
                "Cart",
                style: TextStyle(
                    color: const Color(0xffe9867f),
                    fontSize: size.width <= 510 ? 12 : 20),
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 8),
            InkWell(
              onTap: () {
                context.go(Routes.checkout);
              },
              child: Text(
                "Information",
                style: TextStyle(
                    color: const Color(0xffe9867f),
                    fontSize: size.width <= 510 ? 12 : 20),
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 8),
            InkWell(
                onTap: () {
                  context.go('${Routes.checkoutshipping}/${widget.id}');
                },
                child: Text(
                  "Shippping",
                  style: TextStyle(
                      color: const Color(0xffe9867f),
                      fontSize: size.width <= 510 ? 12 : 20),
                )),
            const SizedBox(width: 7),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 7),
            Text(
              "Payment",
              style: TextStyle(fontSize: size.width <= 510 ? 12 : 20),
            ),
          ],
        ),
        const SizedBox(height: 30),
        Container(
            decoration: BoxDecoration(border: Border.all(color: Colors.grey)),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20, vertical: 10.0),
                  child: Row(
                    children: [
                      const Text("Contact"),
                      SizedBox(width: size.width <= 510 ? 25 : 50),
                      Text(
                        addressDetail?.phone ?? "",
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const Spacer(),
                      InkWell(
                          onTap: () {
                            context.go(Routes.checkout);
                          },
                          child: const Text("Change",
                              style: TextStyle(color: Color(0xffe9867f))))
                    ],
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.0),
                  child: Divider(
                    thickness: .3,
                    color: Colors.grey,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20, vertical: 10.0),
                  child: Row(
                    children: [
                      const Text("Ship to"),
                      SizedBox(width: size.width <= 510 ? 25 : 50),
                      Text(
                        "${addressDetail?.area}, ${addressDetail?.pincode}",
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const Spacer(),
                      InkWell(
                          onTap: () {
                            context.go(Routes.checkout);
                          },
                          child: const Text("Change",
                              style: TextStyle(color: Color(0xffe9867f))))
                    ],
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.0),
                  child: Divider(
                    thickness: .3,
                    color: Colors.grey,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20, vertical: 10.0),
                  child: Row(
                    children: [
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("Shipping"),
                          Text("method"),
                        ],
                      ),
                      SizedBox(width: size.width <= 510 ? 20 : 40),
                      size.width <= 510
                          ? Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    maxLines: 2,
                                    "${x.map((e) => e).toString().replaceAll("(", "").replaceAll(")", "")} - ₹$shippingPrice",
                                  ),
                                ],
                              ),
                            )
                          : Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    maxLines: 2,
                                    "${x.map((e) => e).toString().replaceAll("(", "").replaceAll(")", "")} - ₹$shippingPrice",
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                    ],
                  ),
                )
              ],
            )),
        const SizedBox(height: 20),
//         Row(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             InkWell(
//               onTap: () {
//                 context.go('${Routes.checkoutshipping}/${widget.id}');
//               },
//               child: Row(
//                 children: [
//                   Icon(
//                     color: Color(0xffe9867f),
//                     Icons.arrow_back_ios_outlined,
//                     size: size.width <= 510 ? 12 : 15,
//                   ),
//                   SizedBox(width: 5),
//                   Text(
//                     "Return to Shipping",
//                     style: TextStyle(
//                         fontWeight: FontWeight.w500, color: Color(0xffe9867f)),
//                   ),
//                 ],
//               ),
//             ),
//             const Spacer(),
//             ElevatedButton(
//                 style: ButtonStyle(
//                     backgroundColor:
//                         const WidgetStatePropertyAll(Color(0xffe9867f)),
//                     padding: const WidgetStatePropertyAll(
//                         EdgeInsets.symmetric(vertical: 20, horizontal: 20)),
//                     shape: WidgetStatePropertyAll(RoundedRectangleBorder(
//                         side: const BorderSide(color: Color(0xffe9867f)),
//                         borderRadius: BorderRadius.circular(5)))),
//                 onPressed: () async {
//                   if (userdetails?.cartitems.isEmpty ?? true) {
//                     Fluttertoast.showToast(msg: "Cart is Empty!!");
//                     return;
//                   }
//                   try {
//                     final ctrl = Get.find<ProductCtrl>();
//                     final data = <String, dynamic>{
//                       // "docId": widget.id,
//                       "orderProductsMap": {
//                         for (var item in userdetails?.cartitems ?? [])
//                           item.id: item.toJson()
//                       },
//                       // "time": cartitems?.delDate.toIso8601String(),
//                       "address": addressDetail?.address,
//                       "subTotal": ctrl.totalPrice,
//                       "gst": cartitems?.price,
//                       "total": ctrl.totalPrice,
//                       "shippingPrice": shippingPrice,
//                       "transId": widget.id,
//                       "isPaid": isPaid,
//                       "oId": getRandomId(6), //widget.id,
//                       "uId": userdetails?.docId,
//                       "discountCoupon": {},
//                       "note": widget.instructions,
//                       "name": addressDetail?.name,
//                       "phone": addressDetail?.phone,
//                       "pincode": addressDetail?.pincode,
//                       "area": addressDetail?.area,
//                       'createdAt': DateTime.now(),
//                       'status': 'Unfulfilled'
//                       // "delDate": ctrl.delDate,
//                       // "deliveryTime": ctrl.deliveryTime,
//                       // "delivery": ctrl.delivery,
//                     };

//                     final user = FirebaseAuth.instance.currentUser;
//                     if (user != null) {
//                       final res = await FBFireStore.order.add(data).then(
//                         (value) {
//                           return true;
//                         },
//                       );
//                       if (res) {
//                         await FBFireStore.users.doc(user.uid).update({
//                           'cartitems': {},
//                         });
//                         // ctrl.area = null;
//                         // ctrl.isLateNight = null;
//                         // ctrl.delDate = null;
//                         // ctrl.deliveryTime = null;
//                       }
//                       // context.go(Routes.home); // Uncomment this line if using the context for navigation
//                     } else {
//                       print("No user is currently signed in.");
//                     }
//                   } catch (e) {
//                     print("Failed to update order: $e");
//                   }
//                   context.go(Routes.home);
// //
//                   // // context.go(Routes.home);
//                   // try {
//                   //   // print(cartitems);
//                   //   // print(addressDetail);
//                   //   // print(productm);
//                   //   // print(userdetails);
//                   //   final data = <String, dynamic>{
//                   //     // "docId": widget.id,
//                   //     "orderProducts":
//                   //         userdetails?.cartitems.map((e) => e.toJson()).map(
//                   //       (e) {
//                   //         final json = e.toString();
//                   //         return json;
//                   //       },
//                   //     ),
//                   //     "time": cartitems?.delDate,
//                   //     "address": addressDetail?.address,
//                   //     "subTotal": totalPrice,
//                   //     "gst": cartitems?.price,
//                   //     "total": totalPrice,
//                   //     "transId": widget.id,
//                   //     "isPaid": isPaid,
//                   //     "oId": widget.id,
//                   //     "uId": userdetails?.docId,
//                   //     "discountCoupon": {},
//                   //     "note": productm?.desc,
//                   //   };

//                   //   final user = FirebaseAuth.instance.currentUser;
//                   //   if (user != null) {
//                   //     await FBFireStore.order.add(data);
//                   //     // context.go(Routes.home); // Uncomment this line if using the context for navigation
//                   //   } else {
//                   //     print("No user is currently signed in.");
//                   //   }
//                   // } catch (e) {
//                   //   print("Failed to update order: $e");
//                   // }
//                   // context.go(Routes.home);

//                   // try {
//                   //   final user = FirebaseAuth.instance.currentUser;
//                   //   if (user != null) {
//                   //     await FBFireStore.fb
//                   //         .collection('order')
//                   //         // .doc() // Assuming you use the user ID as the document ID
//                   //         .add({
//                   //       "order": "completed",
//                   //       // // Add the fields you want to update
//                   //       "status": "completed",
//                   //       "completedAt": FieldValue.serverTimestamp(),
//                   //       // Example field
//                   //     });
//                   //     // context.go(Routes.home); // Uncomment this line if using the context for navigation
//                   //   } else {
//                   //     print("No user is currently signed in.");
//                   //   }
//                   // } catch (e) {
//                   //   print("Failed to update order: $e");
//                   // }
//                 },
//                 child: Text(
//                   "Complete Order",
//                   style: TextStyle(
//                     color: Colors.white,
//                     fontSize: size.width <= 510 ? 12 : 15,
//                   ),
//                 )),
//           ],
//         ),
        // const SizedBox(height: 40),
        const Divider(color: Color.fromARGB(255, 220, 217, 217)),
        const SizedBox(height: 10),
        Row(
          children: [
            InkWell(
                onTap: () => context.go(Routes.returnandrefundPolicy),
                child: Text(
                  "Refund policy",
                  style: TextStyle(
                    color: const Color(0xffe9867f),
                    fontSize: size.width <= 510 ? 12 : 15,
                  ),
                )),
            SizedBox(
              width: size.width <= 510 ? 10 : 20,
            ),
            InkWell(
                onTap: () => context.go(Routes.privacy),
                child: Text(
                  "Privacy policy",
                  style: TextStyle(
                    color: const Color(0xffe9867f),
                    fontSize: size.width <= 510 ? 12 : 15,
                  ),
                )),
            SizedBox(
              width: size.width <= 510 ? 10 : 20,
            ),
            InkWell(
                onTap: () => context.go(Routes.terms),
                child: Text(
                  "Terms of service",
                  style: TextStyle(
                    color: const Color(0xffe9867f),
                    fontSize: size.width <= 510 ? 12 : 15,
                  ),
                )),
          ],
        )
      ],
    );
  }

  // Column cartCol(ProductCtrl pctrl) {
  //   getProductPrice();
  //   return Column(
  //     mainAxisAlignment: MainAxisAlignment.start,
  //     children: [
  //       ...List.generate(
  //         pctrl.userDetails?.cartitems.length ?? 0,
  //         (index) {
  //           return Padding(
  //             padding: const EdgeInsets.only(top: 20.0),
  //             child: CheckOutdetails(index: index),
  //           );
  //         },
  //       ),
  //       const SizedBox(height: 20),
  //       Row(
  //         children: [
  //           Expanded(
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 TextField(
  //                     // controller: tagctrl,

  //                     // onChanged: (value) {
  //                     //   tag = value;
  //                     //   descriptionctrl.text = tag;
  //                     // },
  //                     decoration: textfieldDecoration()
  //                         .copyWith(hintText: 'Discount code')),
  //               ],
  //             ),
  //           ),
  //           const SizedBox(width: 20),
  //           ElevatedButton(
  //               style: ButtonStyle(
  //                   backgroundColor: const WidgetStatePropertyAll(
  //                       Color.fromARGB(255, 205, 200, 200)),
  //                   padding: const WidgetStatePropertyAll(
  //                       EdgeInsets.symmetric(vertical: 20, horizontal: 20)),
  //                   shape: WidgetStatePropertyAll(RoundedRectangleBorder(
  //                       side: const BorderSide(
  //                           color: Color.fromARGB(255, 205, 200, 200)),
  //                       borderRadius: BorderRadius.circular(5)))),
  //               onPressed: () {},
  //               child: const Text(
  //                 "Apply",
  //                 style: TextStyle(color: Colors.white),
  //               ))
  //         ],
  //       ),
  //       const Padding(
  //         padding: EdgeInsets.symmetric(vertical: 20.0),
  //         child: Divider(
  //           color: Color.fromARGB(255, 202, 199, 199),
  //           indent: 20,
  //           endIndent: 20,
  //         ),
  //       ),
  //       Row(
  //         children: [
  //           const Text("Subtotal"),
  //           const Spacer(),
  //           Text(
  //             "Rs.$totalPrice",
  //             style: GoogleFonts.b612Mono(
  //                 fontSize: 15, fontWeight: FontWeight.bold),
  //           )
  //         ],
  //       ),
  //       const SizedBox(
  //         height: 10,
  //       ),
  //       const Row(
  //         children: [Text("Shipping"), Spacer(), Text("Free")],
  //       ),
  //       const Padding(
  //         padding: EdgeInsets.symmetric(vertical: 20.0),
  //         child: Divider(
  //           color: Color.fromARGB(255, 202, 199, 199),
  //           indent: 20,
  //           endIndent: 20,
  //         ),
  //       ),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.start,
  //         crossAxisAlignment: CrossAxisAlignment.center,
  //         children: [
  //           const Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Text(
  //                 "Total",
  //                 style: TextStyle(fontSize: 18),
  //               ),
  //               Text("Including ₹57.05 in taxes"),
  //             ],
  //           ),
  //           const Spacer(),
  //           Text(
  //             "Rs.$totalPrice",
  //             style: GoogleFonts.b612Mono(
  //                 fontSize: 25, fontWeight: FontWeight.bold),
  //           )
  //         ],
  //       ),
  //       // const SizedBox(
  //       //   height: 400,
  //       // )
  //     ],
  //   );
  // }
}
