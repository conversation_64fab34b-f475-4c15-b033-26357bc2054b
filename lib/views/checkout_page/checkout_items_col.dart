import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:tbb_web/Models/cart_items.dart';
import 'package:tbb_web/Models/discount_coupon.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/theme.dart';
import 'package:tbb_web/views/common/responsive.dart';

class CartItems extends StatefulWidget {
  const CartItems({
    super.key,
    this.shippingprice,
    required this.from,
  });
  final int? shippingprice;
  final String from;

  @override
  State<CartItems> createState() => _CartItemsState();
}

class _CartItemsState extends State<CartItems> {
  int tax = 0;
  final discouponctrl = TextEditingController();
  bool applied = false;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      getProductPrice();

      // tax = ((pctrl.totalPrice * 5) / 100).ceil();
      pctrl.finalPrice = pctrl.totalPrice +
          (widget.shippingprice?.toInt() ?? 0) +
          (widget.shippingprice != null ? tax : 0);
      pctrl.finalPrice = pctrl.finalPrice - pctrl.couponvalue;
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          ...List.generate(
            pctrl.userDetails?.cartitems.length ?? 0,
            (index) {
              return Padding(
                padding: const EdgeInsets.only(top: 20.0),
                child: CheckOutdetails(index: index),
              );
            },
          ),
          const SizedBox(height: 20),
          if (widget.from == "3")
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextField(
                              onSubmitted: (value) async {
                                DiscountCouponModel? coupon = pctrl
                                    .settings?.coupons
                                    .firstWhereOrNull((element) =>
                                        (element.couponCode.toUpperCase() ==
                                            discouponctrl.text.toUpperCase()) &&
                                        (!element.blocked));

                                if (coupon != null && coupon.couponCode!= "FLASHDEAL") {
                                  applied = true;
                                  pctrl.coupon = coupon;
                                  if (coupon.isPercentage ) {
                                    if (coupon.minOrderValue <=
                                        pctrl.finalPrice) {
                                      num discount = (pctrl.finalPrice *
                                              (coupon.value / 100))
                                          .ceil();
                                      if (discount > coupon.maxDiscount) {
                                        pctrl.couponvalue = coupon.maxDiscount;
                                      } else {
                                        pctrl.couponvalue = discount;
                                      }
                                    }
                                  } else {
                                    if (coupon.minOrderValue <=
                                        pctrl.finalPrice) {
                                      pctrl.couponvalue = coupon.value;
                                    }
                                  }

                                  setState(() {});
                                } else {
                                  showAppSnackBar('Invalild Coupon!');
                                }
                              },
                              inputFormatters: [
                                TextInputFormatter.withFunction(
                                  (oldValue, newValue) => TextEditingValue(
                                    text: newValue.text.toUpperCase(),
                                    selection: newValue
                                        .selection, // Preserve the cursor position
                                  ),
                                )
                              ],
                              enabled: !applied,
                              controller: discouponctrl,

                              // onChanged: (value) {
                              //   tag = value;
                              //   descriptionctrl.text = tag;
                              // },
                              decoration: textfieldDecoration()
                                  .copyWith(hintText: 'Discount code')),
                        ],
                      ),
                    ),
                    const SizedBox(width: 20),
                    applied
                        ? SizedBox(
                            height: 40,
                            child: Image.asset(
                              "assets/images/verify_9458915.png",
                            ),
                          )
                        : ElevatedButton(
                            // style: ButtonStyle(
                            //     backgroundColor:
                            //         const WidgetStatePropertyAll(themeColor),
                            //     padding: const WidgetStatePropertyAll(
                            //         EdgeInsets.symmetric(
                            //             vertical: 20, horizontal: 20)),
                            //     shape: WidgetStatePropertyAll(
                            //         RoundedRectangleBorder(
                            //             side: const BorderSide(
                            //                 color: Color.fromARGB(
                            //                     255, 205, 200, 200)),
                            //             borderRadius:
                            //                 BorderRadius.circular(5)))),
                            style: ButtonStyle(
                                backgroundColor: const WidgetStatePropertyAll(
                                    Color(0xffe9867f)),
                                padding: const WidgetStatePropertyAll(
                                    EdgeInsets.symmetric(
                                        vertical: 20, horizontal: 20)),
                                shape: WidgetStatePropertyAll(
                                    RoundedRectangleBorder(
                                        side: const BorderSide(
                                            color: Color(0xffe9867f)),
                                        borderRadius:
                                            BorderRadius.circular(5)))),
                            onPressed: () async {
                              DiscountCouponModel? coupon = pctrl
                                  .settings?.coupons
                                  .firstWhereOrNull((element) =>
                                      (element.couponCode.toUpperCase() ==
                                          discouponctrl.text.toUpperCase()) &&
                                      (!element.blocked));
                              if (coupon != null && coupon.couponCode!= "FLASHDEAL") {
                                applied = true;
                                pctrl.coupon = coupon;
                                if (coupon.isPercentage) {
                                  if (coupon.minOrderValue <=
                                      pctrl.finalPrice) {
                                    num discount = (pctrl.finalPrice *
                                            (coupon.value / 100))
                                        .ceil();
                                    if (discount > coupon.maxDiscount) {
                                      pctrl.couponvalue = coupon.maxDiscount;
                                    } else {
                                      pctrl.couponvalue = discount;
                                    }
                                  }
                                } else {
                                  if (coupon.minOrderValue <=
                                      pctrl.finalPrice) {
                                    pctrl.couponvalue = coupon.value;
                                  }
                                }

                                setState(() {});
                              } else {
                                showAppSnackBar('Invalild Coupon!');
                              }
                            },
                            child: const Text(
                              "Apply",
                              style: TextStyle(color: Colors.white),
                            ))
                  ],
                ),
                if (applied)
                  TextButton(
                      style: const ButtonStyle(
                          padding: WidgetStatePropertyAll(EdgeInsets.all(0))),
                      onPressed: () {
                        applied = false;
                        discouponctrl.text = "";
                        pctrl.couponvalue = 0;
                        setState(() {});
                      },
                      child: const Text("remove"))
              ],
            ),
          if (widget.from == "3")
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 20.0),
              child: Divider(
                color: Color.fromARGB(255, 202, 199, 199),
                indent: 20,
                endIndent: 20,
              ),
            ),
          Row(
            children: [
              const Text("Subtotal"),
              const Spacer(),
              Text(
                "Rs.${pctrl.totalPrice}",
                style: GoogleFonts.b612Mono(
                    fontSize: 15, fontWeight: FontWeight.bold),
              )
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          Row(
            children: [
              const Text("Shipping"),
              const Spacer(),
              Text(widget.shippingprice != null
                  ? "Rs.${widget.shippingprice}"
                  : "Calculated at next step")
            ],
          ),
          if (widget.shippingprice != null)
            const SizedBox(
              height: 10,
            ),
          // if (widget.shippingprice != null)
          //   Row(
          //     children: [const Text("Tax"), const Spacer(), Text("Rs.$tax")],
          //   ),
          // const SizedBox(
          //   height: 10,
          // ),
          if (pctrl.couponvalue != 0)
            Row(
              children: [
                const Text("Discount"),
                const Spacer(),
                Text("- Rs.${pctrl.couponvalue.toStringAsFixed(2)}")
              ],
            ),
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 20.0),
            child: Divider(
              color: Color.fromARGB(255, 202, 199, 199),
              indent: 20,
              endIndent: 20,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Total",
                    style: TextStyle(fontSize: 18),
                  ),
                  // Text("Including ₹57.05 in taxes"),
                ],
              ),
              const Spacer(),
              Text(
                "Rs.${pctrl.finalPrice}",
                style: GoogleFonts.b612Mono(
                    fontSize: 25, fontWeight: FontWeight.bold),
              )
            ],
          ),
          // const SizedBox(
          //   height: 400,
          // )
        ],
      );
    });
  }
}

class CheckOutdetails extends StatefulWidget {
  const CheckOutdetails({
    super.key,
    required this.index,
  });
  final int index;

  @override
  State<CheckOutdetails> createState() => _CheckOutdetailsState();
}

class _CheckOutdetailsState extends State<CheckOutdetails> {
  num countvalue = 0;
  CartItemsModel? cartproduct;
  ProductModel? product;
  // num? qtyValue;
  // num? price;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    cartproduct = Get.find<ProductCtrl>().userDetails?.cartitems[widget.index];
    dataFetch();
    // product = Get.find<ProductCtrl>()
    //     .products
    //     .firstWhere((element) => element.docId == cartproduct?.pId);
    // qtyValue = Get.find<ProductCtrl>().orderProduct[widget.index].qty;
    // cartproduct = Get.find<ProductCtrl>().orderProduct[widget.index];
  }

  dataFetch() async {
    product = await FBFireStore.product
        .where(FieldPath.documentId, isEqualTo: cartproduct?.pId)
        .where('notavailable', isEqualTo: false)
        .get()
        .then((value) => value.docs.isEmpty
            ? null
            : ProductModel.fromSnapshot(value.docs.first));
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return FutureBuilder(
        future: dataFetch(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return const Text("Facing some issue!!");
          } else if (product != null) {
            return GetBuilder<ProductCtrl>(builder: (pctrl) {
              cartproduct =
                  Get.find<ProductCtrl>().userDetails?.cartitems[widget.index];
              dataFetch();
              // product = Get.find<ProductCtrl>()
              //     .products
              //     .firstWhere((element) => element.docId == cartproduct?.pId);

              return ResponsiveWid(
                mobile: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: () async {
                            await FBFireStore.users
                                .doc(FBAuth.auth.currentUser?.uid)
                                .update({
                              "cartitems.${cartproduct!.id}":
                                  FieldValue.delete()
                            });
                            // Get.find<ProductCtrl>().orderProduct.removeAt(widget.index);
                          },
                          icon: const Icon(
                            CupertinoIcons.delete_solid,
                            size: 20,
                          ),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        SizedBox(
                          height: 90,
                          width: 72,
                          child: Stack(fit: StackFit.expand, children: [
                            Image.asset(
                              "assets/images/TBB-Fix-BG.png",
                              fit: BoxFit.cover,
                              // width: size.width <= mobileMinsize
                              //     ? 150
                              //     : 250,
                              // height: size.width <= mobileMinsize
                              //     ? 200
                              //     : 334,
                            ),
                            Image.network(
                              product?.images.isEmpty ?? true
                                  ? ""
                                  : product?.images.first ?? "",
                              fit: BoxFit.cover,
                            ),
                            Align(
                              alignment: const Alignment(1.4, -1.2),
                              child: Container(
                                width: 20,
                                height: 20,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    color: Colors.grey),
                                child: Center(
                                  child: Text(
                                    cartproduct?.qty.toString() ?? "",
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                            )
                          ]),
                          // Stack(
                          //   // alignment: const Alignment(1, -1),
                          //   children: [
                          //     Image.asset(
                          //       "assets/images/TBB-Fix-BG.png",
                          //       fit: BoxFit.cover,
                          //       width: 70,
                          //       height: 90,
                          //     ),
                          //     Align(
                          //       alignment: Alignment(0, 0),
                          //       child: Padding(
                          //         padding: const EdgeInsets.all(5.0),
                          //         child: Image(
                          //           fit: BoxFit.cover,
                          //           // height: 120,
                          //           width: 70,
                          //           image: NetworkImage(
                          //               product?.images.first ?? ""),
                          //         ),
                          //       ),
                          //     ),
                          //     Align(
                          //       alignment: Alignment(1.2, -1),
                          //       child: Container(
                          //         width: 20,
                          //         height: 20,
                          //         decoration: BoxDecoration(
                          //             borderRadius: BorderRadius.circular(10),
                          //             color: Colors.grey),
                          //         child: Center(
                          //           child: Text(
                          //             cartproduct?.qty.toString() ?? "",
                          //             style:
                          //                 const TextStyle(color: Colors.white),
                          //           ),
                          //         ),
                          //       ),
                          //     ),
                          //   ],
                          // ),
                        ),
                        const SizedBox(
                          width: 8,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width: size.width <= 510
                                      ? 200
                                      : size.width <= 1112
                                          ? 200
                                          : null,
                                  child: Text(
                                    overflow: TextOverflow.ellipsis,
                                    cartproduct?.name ?? "",
                                    style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold),
                                    // style: GoogleFonts.b612Mono(fontSize: 20),
                                  ),
                                ),
                                const SizedBox(width: 10),
                              ],
                            ),
                            if (cartproduct?.selectedflavour != "")
                              Text(
                                "Flavour : ${cartproduct?.selectedflavour ?? ""}",
                                style: const TextStyle(fontSize: 12),
                              ),
                            if (cartproduct?.weightSelected != "null")
                              Text("${cartproduct?.weightSelected} KG"),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Rs.${cartproduct?.price}",
                                  style: GoogleFonts.b612Mono(fontSize: 15),
                                )
                              ],
                            )
                          ],
                        ),
                        const Spacer(),
                        // const SizedBox(
                        //   width: 600,
                        // ),
                      ],
                    ),
                    const Divider(
                      color: Color.fromARGB(255, 202, 199, 199),
                      indent: 20,
                      endIndent: 20,
                    ),
                  ],
                ),
                tablet: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: () async {
                            await FBFireStore.users
                                .doc(FBAuth.auth.currentUser?.uid)
                                .update({
                              "cartitems.${cartproduct!.id}":
                                  FieldValue.delete()
                            });
                            // Get.find<ProductCtrl>().orderProduct.removeAt(widget.index);
                          },
                          icon: const Icon(
                            CupertinoIcons.delete_solid,
                            size: 20,
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        SizedBox(
                          height: 120,
                          width: 96,
                          child: Stack(fit: StackFit.expand, children: [
                            Image.asset(
                              "assets/images/TBB-Fix-BG.png",
                              fit: BoxFit.cover,
                              // width: size.width <= mobileMinsize
                              //     ? 150
                              //     : 250,
                              // height: size.width <= mobileMinsize
                              //     ? 200
                              //     : 334,
                            ),
                            Image.network(
                              product?.images.isEmpty ?? true
                                  ? ""
                                  : product?.images.first ?? "",
                              fit: BoxFit.cover,
                            ),
                            Align(
                              alignment: const Alignment(1.4, -1.2),
                              child: Container(
                                width: 20,
                                height: 20,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    color: Colors.grey),
                                child: Center(
                                  child: Text(
                                    cartproduct?.qty.toString() ?? "",
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                            )
                          ]),
                          //  Stack(
                          //   // alignment: const Alignment(1, -1),
                          //   children: [
                          //     Image.asset(
                          //       "assets/images/TBB-Fix-BG.png",
                          //       fit: BoxFit.cover,
                          //       width: size.width <= mobileMinsize ? 150 : 90,
                          //       height: size.width <= mobileMinsize ? 200 : 120,
                          //     ),
                          //     Align(
                          //       alignment: Alignment(0, 0),
                          //       child: Padding(
                          //         padding: const EdgeInsets.all(5.0),
                          //         child: Image(
                          //           fit: BoxFit.cover,
                          //           // height: 120,
                          //           width: 70,
                          //           image: NetworkImage(
                          //               product?.images.first ?? ""),
                          //         ),
                          //       ),
                          //     ),
                          //     Align(
                          //       alignment: Alignment(1.2, -1),
                          //       child: Container(
                          //         width: 20,
                          //         height: 20,
                          //         decoration: BoxDecoration(
                          //             borderRadius: BorderRadius.circular(10),
                          //             color: Colors.grey),
                          //         child: Center(
                          //           child: Text(
                          //             cartproduct?.qty.toString() ?? "",
                          //             style:
                          //                 const TextStyle(color: Colors.white),
                          //           ),
                          //         ),
                          //       ),
                          //     ),
                          //   ],
                          // ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              overflow: TextOverflow.ellipsis,
                              cartproduct?.name ?? "",
                              style: const TextStyle(
                                  fontSize: 14, fontWeight: FontWeight.bold),
                              // style: GoogleFonts.b612Mono(fontSize: 20),
                            ),

                            if (cartproduct?.selectedflavour != "")
                              Text(
                                  "Flavour : ${cartproduct?.selectedflavour ?? ""}"),
                            if (cartproduct?.weightSelected != "null")
                              Text("${cartproduct?.weightSelected} KG"),
                            // const SizedBox(
                            //     width: 250,
                            //     child: Divider(
                            //       color: Colors.black,
                            //     )),
                            // Text(
                            //     "Day : ${cartproduct?.delDate.day.compareTo(DateTime.now().day) == 0 ? "Today" : cartproduct?.delDate.day.compareTo(DateTime.now().add(const Duration(days: 1)).day) == 00 ? "Tomorrow" : cartproduct?.delDate.toString().split(" ").first}"),
                            // Text("Time: ${cartproduct?.deliveryTime}"),
                            // Text("Delievery: ${cartproduct?.delivery}"),
                            // Text("Area: ${cartproduct?.area}")
                          ],
                        ),
                        const Spacer(),
                        // const SizedBox(
                        //   width: 600,
                        // ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            // Text(
                            //   "Total",
                            //   style: GoogleFonts.b612Mono(
                            //     fontSize: 20,
                            //   ),
                            // ),
                            Text(
                              "Rs.${cartproduct?.price}",
                              style: GoogleFonts.b612Mono(fontSize: 15),
                            )
                          ],
                        )
                      ],
                    ),
                    const Divider(
                      color: Color.fromARGB(255, 202, 199, 199),
                      indent: 20,
                      endIndent: 20,
                    ),
                  ],
                ),
                desktop: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: () async {
                            await FBFireStore.users
                                .doc(FBAuth.auth.currentUser?.uid)
                                .update({
                              "cartitems.${cartproduct!.id}":
                                  FieldValue.delete()
                            });
                            // Get.find<ProductCtrl>().orderProduct.removeAt(widget.index);
                          },
                          icon: const Icon(
                            CupertinoIcons.delete_solid,
                            size: 20,
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        SizedBox(
                          height: 120,
                          width: 96,
                          child: Stack(
                            fit: StackFit.expand,
                            children: [
                              Image.asset(
                                "assets/images/TBB-Fix-BG.png",
                                fit: BoxFit.cover,
                                // width: size.width <= mobileMinsize
                                //     ? 150
                                //     : 250,
                                // height: size.width <= mobileMinsize
                                //     ? 200
                                //     : 334,
                              ),

                              Image.network(
                                product?.images.isEmpty ?? true
                                    ? ""
                                    : product?.images.first ?? "",
                                fit: BoxFit.cover,
                              ),

                              Align(
                                alignment: const Alignment(1.4, -1.2),
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: Colors.grey),
                                  child: Center(
                                    child: Text(
                                      cartproduct?.qty.toString() ?? "",
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ),
                              )
                              // Stack(
                              //   // alignment: const Alignment(1, -1),
                              //   children: [
                              //     Image.asset(
                              //       "assets/images/TBB-Fix-BG.png",
                              //       fit: BoxFit.cover,
                              //       width: size.width <= mobileMinsize ? 150 : 90,
                              //       height: size.width <= mobileMinsize ? 200 : 120,
                              //     ),
                              //     Align(
                              //       alignment: Alignment(0, 0),
                              //       child: Padding(
                              //         padding: const EdgeInsets.all(5.0),
                              //         child: Image(
                              //           fit: BoxFit.cover,
                              //           // height: 120,
                              //           width: 70,
                              //           image: NetworkImage(
                              //               product?.images.first ?? ""),
                              //         ),
                              //       ),
                              //     ),
                              //     Align(
                              //       alignment: Alignment(1.2, -1),
                              //       child: Container(
                              //         width: 20,
                              //         height: 20,
                              //         decoration: BoxDecoration(
                              //             borderRadius: BorderRadius.circular(10),
                              //             color: Colors.grey),
                              //         child: Center(
                              //           child: Text(
                              //             cartproduct?.qty.toString() ?? "",
                              //             style:
                              //                 const TextStyle(color: Colors.white),
                              //           ),
                              //         ),
                              //       ),
                              //     ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              cartproduct?.name ?? "",
                              style: const TextStyle(
                                  overflow: TextOverflow.ellipsis,
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold),
                              // style: GoogleFonts.b612Mono(fontSize: 20),
                            ),
                            if (cartproduct?.selectedflavour != "")
                              Text(
                                  "Flavour : ${cartproduct?.selectedflavour ?? ""}"),
                            if (cartproduct?.weightSelected != "null")
                              Text("${cartproduct?.weightSelected} KG"),
                            // const SizedBox(
                            //     width: 250,
                            //     child: Divider(
                            //       color: Colors.black,
                            //     )),
                            // Text(
                            //     "Day : ${cartproduct?.delDate.day.compareTo(DateTime.now().day) == 0 ? "Today" : cartproduct?.delDate.day.compareTo(DateTime.now().add(const Duration(days: 1)).day) == 00 ? "Tomorrow" : cartproduct?.delDate.toString().split(" ").first}"),
                            // Text("Time: ${cartproduct?.deliveryTime}"),
                            // Text("Delievery: ${cartproduct?.delivery}"),
                            // Text("Area: ${cartproduct?.area}")
                          ],
                        ),
                        const Spacer(),
                        // const SizedBox(
                        //   width: 600,
                        // ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            // Text(
                            //   "Total",
                            //   style: GoogleFonts.b612Mono(
                            //     fontSize: 20,
                            //   ),
                            // ),
                            Text(
                              "Rs.${cartproduct?.price}",
                              style: GoogleFonts.b612Mono(fontSize: 15),
                            )
                          ],
                        )
                      ],
                    ),
                    const Divider(
                      color: Color.fromARGB(255, 202, 199, 199),
                      indent: 20,
                      endIndent: 20,
                    ),
                  ],
                ),
              );
            });
          } else {
            return Padding(
              padding: const EdgeInsets.all(40.0),
              child: Center(
                child: SizedBox(
                  child: LoadingAnimationWidget.staggeredDotsWave(
                    color: themeColor,
                    size: 20,
                  ),
                ),
              ),
            );
          }
        });
  }
}
