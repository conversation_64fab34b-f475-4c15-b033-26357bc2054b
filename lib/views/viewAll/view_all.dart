import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/views/SubCat_page/sortby_dialogbox.dart';
import 'package:tbb_web/views/SubCat_page/subcat_Filter.dart';
import 'package:tbb_web/views/SubCat_page/subcat_Products.dart';
import 'package:tbb_web/views/SubCat_page/subcat_banner.dart';
import 'package:tbb_web/views/common/responsive.dart';
import 'package:tbb_web/wrapper.dart';

class ViewAllPAge extends StatefulWidget {
  const ViewAllPAge({super.key, required this.from});
  final String from;
  @override
  State<ViewAllPAge> createState() => _ViewAllPAgeState();
}

class _ViewAllPAgeState extends State<ViewAllPAge> {
  List<ProductModel> products = [];
  final NumberPaginatorController paginatorController =
      NumberPaginatorController();
  int _currentPage = 0;
  int _previousPage = 0;
  int totalCount = 0;

  int pageNum = 0;
  final ScrollController scrolCtrl = ScrollController();

  // List<ProductModel> filteredList = [];

  DocumentSnapshot? _lastDocument;
  DocumentSnapshot? _firstDocument;
  late Query<Map<String, dynamic>> first;

  _fetchInitialData() async {
    QuerySnapshot querySnapshot = await first.get();
    products =
        querySnapshot.docs.map((e) => ProductModel.fromSnapshot(e)).toList();
    _lastDocument = products.isNotEmpty ? querySnapshot.docs.last : null;
    _firstDocument = products.isNotEmpty ? querySnapshot.docs.first : null;
    setState(() {});
  }

  void _fetchNextPage() async {
    if (_lastDocument != null) {
      Query nextQuery = first.startAfterDocument(_lastDocument!).limit(24);
      QuerySnapshot querySnapshot = await nextQuery.get();
      List<ProductModel> newDocuments =
          querySnapshot.docs.map((e) => ProductModel.fromSnapshot(e)).toList();
      if (newDocuments.isNotEmpty) {
        products = newDocuments;
        _lastDocument = querySnapshot.docs.last;
        _firstDocument = querySnapshot.docs.first;
        pageNum = pageNum + 1;

        setState(() {});
      }
    }
  }

  cal() async {
    totalCount = (await FBFireStore.product
                .where('type', isEqualTo: widget.from)
                .count()
                .get())
            .count ??
        0;
    print("--$totalCount");
    setState(() {});
  }
//  void _fetchSlot() async {
//     final slot = FBFireStore.product
//         .orderBy("name", descending: false)
//         .limit(10)
//         .startAt([1000]).limit(10);
//     QuerySnapshot querySnapshot = await slot.get();
//     _documents =
//         querySnapshot.docs.map((e) => ProductModel.fromSnapshot(e)).toList();
//     _lastDocument = _documents.isNotEmpty ? querySnapshot.docs.last : null;
//     _firstDocument = _documents.isNotEmpty ? querySnapshot.docs.first : null;

//     setState(() {});
//   }
  void _refresheddata() async {
    Query fetchCurrentData = first
        .startAtDocument(_firstDocument!)
        .endAtDocument(_lastDocument!)
        .limit(24);
    QuerySnapshot querySnapshot = await fetchCurrentData.get();
    products =
        querySnapshot.docs.map((e) => ProductModel.fromSnapshot(e)).toList();
    setState(() {});
  }

  void _fetchPreviousPage() async {
    if (_lastDocument != null) {
      Query nextQuery = first.endBeforeDocument(_firstDocument!).limit(24);
      QuerySnapshot querySnapshot = await nextQuery.get();
      List<ProductModel> newDocuments =
          querySnapshot.docs.map((e) => ProductModel.fromSnapshot(e)).toList();
      if (newDocuments.isNotEmpty && newDocuments.length != 0) {
        products = newDocuments;
        _lastDocument = querySnapshot.docs.last;
        _firstDocument = querySnapshot.docs.first;
        pageNum = pageNum - 1;
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    cal();
    first = FBFireStore.product
        .where('notavailable', isEqualTo: false)
        .where('type', isEqualTo: widget.from)
        .orderBy("name", descending: false)
        .limit(24);
    _fetchInitialData();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    // products.clear();
    // products = p;
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      // if (widget.from == "All") {
      //   loadAllData();
      // } else {
      //   loadData();
      // }
      return Wrapper(
          scrollController: scrolCtrl,
          body: products.isEmpty
              ? const Padding(
                  padding: EdgeInsets.all(50.0),
                  child: Center(
                      child: SizedBox(
                          height: 30,
                          width: 30,
                          child: CircularProgressIndicator())),
                )
              : ResponsiveWid(
                  mobile: products.isEmpty
                      ? Column(
                          children: [
                            SubCatBanner(name: widget.from),
                            const Center(
                                child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 210.0),
                              child: Text(
                                "Empty!!",
                                style: TextStyle(fontSize: 24),
                              ),
                            )),
                          ],
                        )
                      : Column(
                          children: [
                            SubCatBanner(name: widget.from),
                            // Row(
                            //   children: [
                            //     Expanded(
                            //       child: InkWell(
                            //         onTap: () => showDialog(
                            //             context: context,
                            //             builder: (BuildContext context) {
                            //               return Dialog(
                            //                 child: SingleChildScrollView(
                            //                   child: Container(
                            //                     color: Colors.white,
                            //                     width: 300,
                            //                     // height: 320,
                            //                     child: Column(
                            //                       mainAxisSize:
                            //                           MainAxisSize.min,
                            //                       mainAxisAlignment:
                            //                           MainAxisAlignment.center,
                            //                       children: [
                            //                         Padding(
                            //                           padding:
                            //                               const EdgeInsets.only(
                            //                                   left: 10,
                            //                                   right: 10,
                            //                                   top: 10),
                            //                           child: Row(
                            //                             mainAxisAlignment:
                            //                                 MainAxisAlignment
                            //                                     .spaceBetween,
                            //                             children: [
                            //                               Text(
                            //                                 "FILTER",
                            //                                 style: GoogleFonts
                            //                                     .aBeeZee(
                            //                                         fontSize:
                            //                                             22,
                            //                                         fontWeight:
                            //                                             FontWeight
                            //                                                 .w600),
                            //                               ),
                            //                               IconButton(
                            //                                   hoverColor: Colors
                            //                                       .transparent,
                            //                                   highlightColor:
                            //                                       Colors
                            //                                           .transparent,
                            //                                   onPressed: () =>
                            //                                       Navigator.pop(
                            //                                           context),
                            //                                   icon: const Icon(
                            //                                       CupertinoIcons
                            //                                           .xmark)),
                            //                             ],
                            //                           ),
                            //                         ),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Weightfilter(),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Availabilityfilter(),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Pricefilter(),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Typefilter(),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                       ],
                            //                     ),
                            //                   ),
                            //                 ),
                            //               );
                            //             }),
                            //         child: Container(
                            //           padding: const EdgeInsets.only(
                            //               top: 15, bottom: 10),
                            //           decoration: const BoxDecoration(
                            //               border: Border(
                            //                   bottom: BorderSide(
                            //                       color: Colors.black))),
                            //           child: Row(
                            //             mainAxisAlignment:
                            //                 MainAxisAlignment.center,
                            //             crossAxisAlignment:
                            //                 CrossAxisAlignment.center,
                            //             children: [
                            //               const Icon(Icons.filter_alt_outlined),
                            //               Text(
                            //                 "FILTER",
                            //                 style: GoogleFonts.aBeeZee(
                            //                     fontSize: 18,
                            //                     fontWeight: FontWeight.w600),
                            //               ),
                            //             ],
                            //           ),
                            //         ),
                            //       ),
                            //     ),
                            //     const SizedBox(width: 30),
                            //     Expanded(
                            //       child: InkWell(
                            //         onTap: () => showDialog(
                            //           context: context,
                            //           builder: (BuildContext context) {
                            //             return Column(
                            //               crossAxisAlignment:
                            //                   CrossAxisAlignment.center,
                            //               mainAxisAlignment:
                            //                   MainAxisAlignment.center,
                            //               children: [
                            //                 Material(
                            //                   child: Container(
                            //                     color: Colors.white,
                            //                     width: 300,
                            //                     height: 320,
                            //                     child: Column(
                            //                       children: [
                            //                         Padding(
                            //                           padding:
                            //                               const EdgeInsets.only(
                            //                                   left: 10,
                            //                                   right: 10,
                            //                                   top: 15),
                            //                           child: Row(
                            //                             mainAxisAlignment:
                            //                                 MainAxisAlignment
                            //                                     .spaceBetween,
                            //                             children: [
                            //                               Text(
                            //                                 "SORT BY",
                            //                                 style: GoogleFonts
                            //                                     .aBeeZee(
                            //                                         fontSize:
                            //                                             25,
                            //                                         fontWeight:
                            //                                             FontWeight
                            //                                                 .w600),
                            //                               ),
                            //                               IconButton(
                            //                                   hoverColor: Colors
                            //                                       .transparent,
                            //                                   highlightColor:
                            //                                       Colors
                            //                                           .transparent,
                            //                                   onPressed: () =>
                            //                                       Navigator.pop(
                            //                                           context),
                            //                                   icon: const Icon(
                            //                                       CupertinoIcons
                            //                                           .xmark)),
                            //                             ],
                            //                           ),
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               // highlightColor:
                            //                               //     Colors.transparent,
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Featured",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Best Selling",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Alphabetically, A-Z",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Alphabetically, Z-A",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Price, Low to High",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Price, High to Low",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Date, Old to New",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Date, New to Old",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                       ],
                            //                     ),
                            //                   ),
                            //                 )
                            //               ],
                            //             );
                            //           },
                            //         ),
                            //         child: Container(
                            //           padding: const EdgeInsets.only(
                            //               top: 15, bottom: 10),
                            //           decoration: const BoxDecoration(
                            //               border: Border(
                            //                   bottom: BorderSide(
                            //                       color: Colors.black))),
                            //           child: Row(
                            //             mainAxisAlignment:
                            //                 MainAxisAlignment.center,
                            //             crossAxisAlignment:
                            //                 CrossAxisAlignment.center,
                            //             children: [
                            //               const Icon(CupertinoIcons.sort_down),
                            //               Text(
                            //                 "SORT BY",
                            //                 style: GoogleFonts.aBeeZee(
                            //                     fontSize: 16,
                            //                     fontWeight: FontWeight.w600),
                            //               ),
                            //             ],
                            //           ),
                            //         ),
                            //       ),
                            //     ),
                            //   ],
                            // ),
                            SubcatProducts(
                              // scrolCtrl: scrolCtrl,
                              productlist: products,
                            ),
                            const SizedBox(height: 20),
                            if ((totalCount) > 24)
                              products.isEmpty
                                  ? const SizedBox()
                                  : Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10.0),
                                      child: Row(
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 10.0, top: 10),
                                            child: IconButton(
                                                onPressed: () {
                                                  setState(() {
                                                    _previousPage =
                                                        _currentPage;
                                                    _currentPage =
                                                        _currentPage - 1;
                                                    paginatorController
                                                            .currentPage =
                                                        paginatorController
                                                                .currentPage -
                                                            1;
                                                    _fetchPreviousPage();
                                                    scrolCtrl.jumpTo(0);
                                                  });
                                                },
                                                icon: const Icon(Icons
                                                    .arrow_back_ios_new_sharp)),
                                          ),
                                          const Spacer(),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                right: 10.0, top: 10),
                                            child: IconButton(
                                                onPressed: () {
                                                  setState(() {
                                                    _previousPage =
                                                        _currentPage;
                                                    _currentPage =
                                                        _currentPage + 1;
                                                    paginatorController
                                                            .currentPage =
                                                        paginatorController
                                                                .currentPage +
                                                            1;
                                                    _fetchNextPage();
                                                    scrolCtrl.jumpTo(0);
                                                  });
                                                },
                                                icon: const Icon(Icons
                                                    .arrow_forward_ios_sharp)),
                                          )
                                        ],
                                      ),
                                    ),
                            const SizedBox(
                              height: 30,
                            ),
                          ],
                        ),

                  //tablet

                  tablet: products.isEmpty
                      ? Column(
                          children: [
                            SubCatBanner(name: widget.from),
                            const Center(
                                child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 285.0),
                              child: Text(
                                "Empty!!",
                                style: TextStyle(fontSize: 24),
                              ),
                            )),
                          ],
                        )
                      : Column(
                          children: [
                            SubCatBanner(name: widget.from),
                            // Row(
                            //   children: [
                            //     Expanded(
                            //       child: InkWell(
                            //         onTap: () => showDialog(
                            //             context: context,
                            //             builder: (BuildContext context) {
                            //               return Dialog(
                            //                 child: SingleChildScrollView(
                            //                   child: Container(
                            //                     color: Colors.white,
                            //                     width: 300,
                            //                     // height: 320,
                            //                     child: Column(
                            //                       mainAxisSize:
                            //                           MainAxisSize.min,
                            //                       mainAxisAlignment:
                            //                           MainAxisAlignment.center,
                            //                       children: [
                            //                         Padding(
                            //                           padding:
                            //                               const EdgeInsets.only(
                            //                                   left: 10,
                            //                                   right: 10,
                            //                                   top: 10),
                            //                           child: Row(
                            //                             mainAxisAlignment:
                            //                                 MainAxisAlignment
                            //                                     .spaceBetween,
                            //                             children: [
                            //                               Text(
                            //                                 "FILTER",
                            //                                 style: GoogleFonts
                            //                                     .aBeeZee(
                            //                                         fontSize:
                            //                                             22,
                            //                                         fontWeight:
                            //                                             FontWeight
                            //                                                 .w600),
                            //                               ),
                            //                               IconButton(
                            //                                   hoverColor: Colors
                            //                                       .transparent,
                            //                                   highlightColor:
                            //                                       Colors
                            //                                           .transparent,
                            //                                   onPressed: () =>
                            //                                       Navigator.pop(
                            //                                           context),
                            //                                   icon: const Icon(
                            //                                       CupertinoIcons
                            //                                           .xmark)),
                            //                             ],
                            //                           ),
                            //                         ),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Weightfilter(),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Availabilityfilter(),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Pricefilter(),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Typefilter(),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                         const Divider(
                            //                           height: 0,
                            //                         ),
                            //                       ],
                            //                     ),
                            //                   ),
                            //                 ),
                            //               );
                            //             }),
                            //         child: Container(
                            //           padding: const EdgeInsets.only(
                            //               top: 15, bottom: 10),
                            //           decoration: const BoxDecoration(
                            //               border: Border(
                            //                   bottom: BorderSide(
                            //                       color: Colors.black))),
                            //           child: Row(
                            //             mainAxisAlignment:
                            //                 MainAxisAlignment.center,
                            //             crossAxisAlignment:
                            //                 CrossAxisAlignment.center,
                            //             children: [
                            //               const Icon(Icons.filter_alt_outlined),
                            //               Text(
                            //                 "FILTER",
                            //                 style: GoogleFonts.aBeeZee(
                            //                     fontSize: 18,
                            //                     fontWeight: FontWeight.w600),
                            //               ),
                            //             ],
                            //           ),
                            //         ),
                            //       ),
                            //     ),
                            //     const SizedBox(width: 30),
                            //     Expanded(
                            //       child: InkWell(
                            //         onTap: () => showDialog(
                            //           context: context,
                            //           builder: (BuildContext context) {
                            //             return Column(
                            //               crossAxisAlignment:
                            //                   CrossAxisAlignment.center,
                            //               mainAxisAlignment:
                            //                   MainAxisAlignment.center,
                            //               children: [
                            //                 Material(
                            //                   child: Container(
                            //                     color: Colors.white,
                            //                     width: 300,
                            //                     height: 320,
                            //                     child: Column(
                            //                       children: [
                            //                         Padding(
                            //                           padding:
                            //                               const EdgeInsets.only(
                            //                                   left: 10,
                            //                                   right: 10,
                            //                                   top: 15),
                            //                           child: Row(
                            //                             mainAxisAlignment:
                            //                                 MainAxisAlignment
                            //                                     .spaceBetween,
                            //                             children: [
                            //                               Text(
                            //                                 "SORT BY",
                            //                                 style: GoogleFonts
                            //                                     .aBeeZee(
                            //                                         fontSize:
                            //                                             25,
                            //                                         fontWeight:
                            //                                             FontWeight
                            //                                                 .w600),
                            //                               ),
                            //                               IconButton(
                            //                                   hoverColor: Colors
                            //                                       .transparent,
                            //                                   highlightColor:
                            //                                       Colors
                            //                                           .transparent,
                            //                                   onPressed: () =>
                            //                                       Navigator.pop(
                            //                                           context),
                            //                                   icon: const Icon(
                            //                                       CupertinoIcons
                            //                                           .xmark)),
                            //                             ],
                            //                           ),
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               // highlightColor:
                            //                               //     Colors.transparent,
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Featured",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Best Selling",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Alphabetically, A-Z",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Alphabetically, Z-A",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Price, Low to High",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Price, High to Low",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Date, Old to New",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                         Row(
                            //                           children: [
                            //                             Checkbox(
                            //                               hoverColor: Colors
                            //                                   .transparent,
                            //                               overlayColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors
                            //                                           .transparent),
                            //                               checkColor:
                            //                                   Colors.black,
                            //                               fillColor:
                            //                                   const WidgetStatePropertyAll(
                            //                                       Colors.black),
                            //                               shape: const CircleBorder(
                            //                                   side:
                            //                                       BorderSide()),
                            //                               value: true,
                            //                               onChanged: (value) {},
                            //                             ),
                            //                             InkWell(
                            //                               onTap: () {},
                            //                               child: const Text(
                            //                                 "Date, New to Old",
                            //                                 style: TextStyle(
                            //                                   fontSize: 13,
                            //                                 ),
                            //                               ),
                            //                             )
                            //                           ],
                            //                         ),
                            //                       ],
                            //                     ),
                            //                   ),
                            //                 )
                            //               ],
                            //             );
                            //           },
                            //         ),
                            //         child: Container(
                            //           padding: const EdgeInsets.only(
                            //               top: 15, bottom: 10),
                            //           decoration: const BoxDecoration(
                            //               border: Border(
                            //                   bottom: BorderSide(
                            //                       color: Colors.black))),
                            //           child: Row(
                            //             mainAxisAlignment:
                            //                 MainAxisAlignment.center,
                            //             crossAxisAlignment:
                            //                 CrossAxisAlignment.center,
                            //             children: [
                            //               const Icon(CupertinoIcons.sort_down),
                            //               Text(
                            //                 "SORT BY",
                            //                 style: GoogleFonts.aBeeZee(
                            //                     fontSize: 16,
                            //                     fontWeight: FontWeight.w600),
                            //               ),
                            //             ],
                            //           ),
                            //         ),
                            //       ),
                            //     ),
                            //   ],
                            // ),
                            SubcatProducts(
                              // scrolCtrl: scrolCtrl,
                              productlist: products,
                            ),
                            const SizedBox(height: 20),
                            if ((totalCount) > 24)
                              products.isEmpty
                                  ? const SizedBox()
                                  : Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 30.0),
                                      child: Row(
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 10.0, top: 10),
                                            child: IconButton(
                                                onPressed: () {
                                                  setState(() {
                                                    _previousPage =
                                                        _currentPage;
                                                    _currentPage =
                                                        _currentPage - 1;
                                                    paginatorController
                                                            .currentPage =
                                                        paginatorController
                                                                .currentPage -
                                                            1;
                                                    _fetchPreviousPage();
                                                    scrolCtrl.jumpTo(0);
                                                  });
                                                },
                                                icon: const Icon(Icons
                                                    .arrow_back_ios_new_sharp)),
                                          ),
                                          const Spacer(),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                right: 10.0, top: 10),
                                            child: IconButton(
                                                onPressed: () {
                                                  setState(() {
                                                    _previousPage =
                                                        _currentPage;
                                                    _currentPage =
                                                        _currentPage + 1;
                                                    paginatorController
                                                            .currentPage =
                                                        paginatorController
                                                                .currentPage +
                                                            1;
                                                    _fetchNextPage();
                                                    scrolCtrl.jumpTo(0);
                                                  });
                                                },
                                                icon: const Icon(Icons
                                                    .arrow_forward_ios_sharp)),
                                          )
                                        ],
                                      ),
                                    ),
                            const SizedBox(
                              height: 30,
                            ),
                          ],
                        ),

                  //desktop

                  desktop: products.isEmpty
                      ? Column(
                          children: [
                            SubCatBanner(name: widget.from),
                            const Center(
                                child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 215.0),
                              child: Text(
                                "Empty!!",
                                style: TextStyle(fontSize: 24),
                              ),
                            )),
                          ],
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SubCatBanner(name: widget.from),
                            // SortByDialogBox(
                            //   onTapSort: (x) {
                            //     switch (x) {
                            //       case 'Featured':
                            //         products.sort(
                            //             (b, a) => a.name.compareTo(b.name));
                            //         break;
                            //       case 'Best Selling':
                            //         products.sort(
                            //             (b, a) => a.name.compareTo(b.name));
                            //         break;
                            //       case 'Alphabetically, A-Z':
                            //         products.sort(
                            //             (a, b) => a.name.compareTo(b.name));
                            //         break;
                            //       case 'Alphabetically, Z-A':
                            //         products.sort(
                            //             (b, a) => a.name.compareTo(b.name));
                            //         break;
                            //       case 'Price, Low to High':
                            //         products.sort((a, b) => (pctrl.designCost
                            //                 .firstWhere((element) =>
                            //                     element.docId ==
                            //                     a.designCostdocId)
                            //                 .basePrice)
                            //             .compareTo((pctrl.designCost.firstWhere(
                            //                 (element) =>
                            //                     element.docId ==
                            //                     b.designCostdocId)).basePrice));
                            //         break;
                            //       case 'Price, High to Low':
                            //         products.sort((b, a) => (pctrl.designCost
                            //                 .firstWhere((element) =>
                            //                     element.docId ==
                            //                     a.designCostdocId)
                            //                 .basePrice)
                            //             .compareTo((pctrl.designCost.firstWhere(
                            //                 (element) =>
                            //                     element.docId ==
                            //                     b.designCostdocId)).basePrice));
                            //         break;
                            //       case 'Date, Old to New':
                            //         products.sort((a, b) =>
                            //             a.createdAt.compareTo(b.createdAt));
                            //         break;
                            //       case 'Date, New to Old':
                            //         products.sort((b, a) =>
                            //             a.createdAt.compareTo(b.createdAt));
                            //         break;
                            //       default:
                            //     }

                            //     print(products.map((e) => e.name));
                            //   },
                            // ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Expanded(
                                //     child: SubCatFilter(
                                //   isCake: widget.from,
                                // )),
                                Expanded(
                                    flex: 4,
                                    child: SubcatProducts(
                                      // scrolCtrl: scrolCtrl,
                                      productlist: products,
                                      // nextPage: () {
                                      //   // setState(() {
                                      //   _previousPage = _currentPage;
                                      //   _currentPage = _currentPage + 1;
                                      //   paginatorController.currentPage =
                                      //       paginatorController.currentPage + 1;
                                      //   _fetchNextPage();
                                      //   // });
                                      // },
                                      // previospage: () {
                                      //   setState(() {
                                      //   _previousPage = _currentPage;
                                      //   _currentPage = _currentPage - 1;
                                      //   paginatorController.currentPage =
                                      //       paginatorController.currentPage - 1;
                                      //   _fetchPreviousPage();
                                      //   });
                                      // },
                                    )),
                              ],
                            ),
                            // const SizedBox(height: 20),
                            if ((totalCount) > 24)
                              products.isEmpty
                                  ? const SizedBox()
                                  : Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 80.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 10.0, top: 10),
                                            child: IconButton(
                                                onPressed: () {
                                                  setState(() {
                                                    _previousPage =
                                                        _currentPage;
                                                    _currentPage =
                                                        _currentPage - 1;
                                                    paginatorController
                                                            .currentPage =
                                                        paginatorController
                                                                .currentPage -
                                                            1;
                                                    _fetchPreviousPage();
                                                    scrolCtrl.jumpTo(0);
                                                  });
                                                },
                                                icon: const Icon(Icons
                                                    .arrow_back_ios_new_sharp)),
                                          ),
                                          Spacer(),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                right: 10.0, top: 10),
                                            child: IconButton(
                                                onPressed: () {
                                                  setState(() {
                                                    _previousPage =
                                                        _currentPage;
                                                    _currentPage =
                                                        _currentPage + 1;
                                                    paginatorController
                                                            .currentPage =
                                                        paginatorController
                                                                .currentPage +
                                                            1;
                                                    _fetchNextPage();
                                                    scrolCtrl.jumpTo(0);
                                                  });
                                                },
                                                icon: const Icon(Icons
                                                    .arrow_forward_ios_sharp)),
                                          )
                                        ],
                                      ),
                                    ),
                            const SizedBox(
                              height: 30,
                            ),
                          ],
                        ),
                ));
    });
  }
}
