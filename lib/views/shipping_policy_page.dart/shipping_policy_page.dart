import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/wrapper.dart';

class ShippingPolicy extends StatelessWidget {
  const ShippingPolicy({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Wrapper(
      body: Padding(
        padding: EdgeInsets.only(
            left: 40.0,
            right: 40,
            top: size.width <= 510 ? 80 : 150,
            bottom: 40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 50.0, bottom: 40),
              child: Center(
                child: Text(
                  'Shipping Policy'.toUpperCase(),
                  style: GoogleFonts.crimsonPro(
                    color: const Color.fromARGB(255, 216, 122, 153),
                    fontSize: size.width <= 510 ? 30 : 45,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            Container(
              constraints: const BoxConstraints(maxWidth: 1000),
              child: Text(
                "Standard cakes are delivered within 3 hours, while designer cakes take 5 hours or both can arrive at your chosen time slot.\nWe delivery within the specified timeframe.\nMidnight delivery is also available, between 11 PM and 12 AM.\nFor more details, reach us at +91 9265537579.",
                style: TextStyle(fontSize: size.width <= 510 ? 16 : 20),
                // textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
                style: ButtonStyle(
                    padding: const WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 20, vertical: 10)),
                    shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15))),
                    backgroundColor: const WidgetStatePropertyAll(
                        Color.fromARGB(255, 222, 155, 177))),
                onPressed: () {
                  context.go(Routes.home);
                },
                child: const Text(
                  "Back To Home",
                  style: TextStyle(color: Colors.white),
                )),
            SizedBox(height: size.width <= 510 ? 120 : 160),
          ],
        ),
      ),
    );
  }
}
