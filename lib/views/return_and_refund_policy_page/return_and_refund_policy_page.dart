import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/wrapper.dart';

class ReturnAndRefundPolicy extends StatelessWidget {
  const ReturnAndRefundPolicy({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Wrapper(
      body: Padding(
        padding: EdgeInsets.only(
            left: 40.0,
            right: 40,
            top: size.width <= 510 ? 80 : 150,
            bottom: 40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 50.0, bottom: 40),
              child: Center(
                child: Text(
                  'Return & Refund Policy'.toUpperCase(),
                  style: GoogleFonts.crimsonPro(
                    color: const Color.fromARGB(255, 216, 122, 153),
                    fontSize: size.width <= 510 ? 30 : 45,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            Container(
              constraints: const BoxConstraints(maxWidth: 1000),
              child: Text(
                "All items on our website are perishable, made to order, and customized. Therefore, we do not offer product returns. Once delivered, the order is non-returnable.\nYou may cancel your order within 30 minutes of placing it. Beyond this period, cancellations and refunds are not allowed.\nRefunds will be processed to your bank account within 2-5 working days.\nCancellation charges may apply, depending on the case.\nFor further inquiries, please contact us at +91 **********.",
                style: TextStyle(fontSize: size.width <= 510 ? 16 : 20),
                // textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
                style: ButtonStyle(
                    padding: const WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 20, vertical: 10)),
                    shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15))),
                    backgroundColor: const WidgetStatePropertyAll(
                        const Color.fromARGB(255, 222, 155, 177))),
                onPressed: () {
                  context.go(Routes.home);
                },
                child: const Text(
                  "Back To Home",
                  style: TextStyle(color: Colors.white),
                )),
            SizedBox(height: size.width <= 510 ? 120 : 160),
          ],
        ),
      ),
    );
  }
}
