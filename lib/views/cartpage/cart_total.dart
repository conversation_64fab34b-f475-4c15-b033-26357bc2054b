import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tbb_web/Models/cart_items.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/views/common/responsive.dart';

class Carttotal extends StatefulWidget {
  const Carttotal({
    super.key,
  });
  @override
  State<Carttotal> createState() => _CarttotalState();
}

class _CarttotalState extends State<Carttotal> {
  CartItemsModel? cartproduct;
  bool typeis = true;
  ProductModel? product;
  num totalPrice = 0;
  @override
  void initState() {
    super.initState();
  }

  getProductPrice() {
    totalPrice = 0;
    final ctrl = Get.find<ProductCtrl>();
    if (ctrl.userDetails?.cartitems == null ||
        ctrl.userDetails!.cartitems.isEmpty) {
      for (var element in ctrl.orderProduct) {
        totalPrice += element.price * element.qty;
      }
    } else
      for (var cartItem in Get.find<ProductCtrl>().userDetails!.cartitems) {
        totalPrice += cartItem.price * cartItem.qty;
      }

    // print(totalPrice);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(builder: (pctrl) {
      // typeis =product!.type.contains("Flower") ||
      //     product!.type.contains("Add-On");
      // print(pctrl.userDetails?.cartitems.toSet());
      getProductPrice();
      return ResponsiveWid(
        //mobile

        mobile: Padding(
          padding: const EdgeInsets.only(
            bottom: 50,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                // crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Subtotal",
                    style: GoogleFonts.b612Mono(fontSize: 23),
                  ),
                  const SizedBox(
                    width: 80,
                  ),
                  Text(
                    "Rs.$totalPrice",
                    style: GoogleFonts.b612Mono(fontSize: 20),
                  ),
                ],
              ),
              const Text("Tax included and shipping calculated at checkout"),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: InkWell(
                  focusColor: Colors.amber,
                  child: ElevatedButton(
                    style: const ButtonStyle(
                      foregroundColor: WidgetStatePropertyAll(Colors.white),
                      backgroundColor: WidgetStatePropertyAll(
                          Color.fromARGB(255, 41, 40, 40)),
                      shadowColor: WidgetStatePropertyAll(Colors.transparent),
                      fixedSize: WidgetStatePropertyAll(Size.fromWidth(320)),
                      shape: WidgetStatePropertyAll(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(5),
                          ),
                        ),
                      ),
                      elevation: WidgetStatePropertyAll(10),
                    ),
                    onPressed: () async {
                      // bool updated = true;
                      // for (CartItemsModel x
                      //     in Get.find<ProductCtrl>().userDetails?.cartitems ??
                      //         []) {
                      //   if ((x.delDate.day.compareTo(DateTime.now().day) ==
                      //       -1)) {
                      //     updated = false;
                      //   }
                      //   if (updated == false) {
                      //     break;
                      //   }
                      // }
                      if (isLoggedIn()) {
                        // print("Loggedin check");
                        // if (updated) {
                        // print("updated check");

                        context.go(Routes.checkout);
                        // } else {
                        //   Fluttertoast.showToast(msg: "invalid Date");
                        // }
                      } else {
                        Fluttertoast.showToast(msg: "Please Login first");
                      }
                    },
                    child: const Text(
                      "CHECK OUT",
                      style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 15,
                          color: Colors.white),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        //tablet

        tablet: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    "Subtotal",
                    style: GoogleFonts.b612Mono(fontSize: 28),
                  ),
                  const Spacer(),
                  Text(
                    "Rs.$totalPrice",
                    style: GoogleFonts.b612Mono(fontSize: 20),
                  ),
                ],
              ),
              const Text("Tax included and shipping calculated at checkout"),
              const SizedBox(
                height: 10,
              ),
              InkWell(
                focusColor: Colors.amber,
                child: ElevatedButton(
                  style: const ButtonStyle(
                    foregroundColor: WidgetStatePropertyAll(Colors.white),
                    backgroundColor:
                        WidgetStatePropertyAll(Color.fromARGB(255, 41, 40, 40)),
                    shadowColor: WidgetStatePropertyAll(Colors.transparent),
                    fixedSize: WidgetStatePropertyAll(Size.fromWidth(320)),
                    shape: WidgetStatePropertyAll(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(5),
                        ),
                      ),
                    ),
                    elevation: WidgetStatePropertyAll(10),
                  ),
                  onPressed: () async {
                    // bool updated = true;
                    // for (CartItemsModel x
                    //     in Get.find<ProductCtrl>().userDetails?.cartitems ??
                    //         []) {
                    //   if ((x.delDate.day.compareTo(DateTime.now().day) ==
                    //       -1)) {
                    //     updated = false;
                    //   }
                    //   if (updated == false) {
                    //     break;
                    //   }
                    // }
                    if (isLoggedIn()) {
                      // print("Loggedin check");
                      // if (updated) {
                      // print("updated check");

                      context.go(Routes.checkout);
                      // } else {
                      //   Fluttertoast.showToast(msg: "invalid Date");
                      // }
                    } else {
                      Fluttertoast.showToast(msg: "Please Login first");
                    }
                  },
                  child: const Text(
                    "CHECK OUT",
                    style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 15,
                        color: Colors.white),
                  ),
                ),
              )
            ],
          ),
        ),

        //desktop

        desktop: Padding(
          padding:
              const EdgeInsets.only(left: 50.0, bottom: 5, top: 50, right: 110),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Subtotal",
                        style: GoogleFonts.b612Mono(fontSize: 28),
                      ),
                      const SizedBox(width: 80),
                      Text(
                        // "Rs.${cartproduct?.price}",
                        "Rs.$totalPrice",
                        // "",
                        style: GoogleFonts.b612Mono(fontSize: 20),
                      ),
                    ],
                  ),
                  const Text(
                      "Tax included and shipping calculated at checkout"),
                  const SizedBox(
                    height: 10,
                  ),
                  InkWell(
                    focusColor: Colors.amber,
                    child: ElevatedButton(
                      style: const ButtonStyle(
                        foregroundColor: WidgetStatePropertyAll(Colors.white),
                        backgroundColor: WidgetStatePropertyAll(
                            Color.fromARGB(255, 41, 40, 40)),
                        shadowColor: WidgetStatePropertyAll(Colors.transparent),
                        fixedSize: WidgetStatePropertyAll(Size.fromWidth(320)),
                        shape: WidgetStatePropertyAll(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5),
                            ),
                          ),
                        ),
                        elevation: WidgetStatePropertyAll(10),
                      ),
                      onPressed: () async {
                        // bool updated = true;
                        // for (CartItemsModel x
                        //     in Get.find<ProductCtrl>().userDetails?.cartitems ??
                        //         []) {
                        //   if ((x.delDate.day.compareTo(DateTime.now().day) ==
                        //       -1)) {
                        //     updated = false;
                        //   }
                        //   if (updated == false) {
                        //     break;
                        //   }
                        // }
                        if (isLoggedIn()) {
                          // print("Loggedin check");
                          // if (updated) {
                          // print("updated check");

                          context.go(Routes.checkout);
                          // } else {
                          //   Fluttertoast.showToast(msg: "invalid Date");
                          // }
                        } else {
                          context.go(Routes.auth);

                          // Fluttertoast.showToast(msg: "Please Login first");
                        }
                      },
                      child: const Text(
                        "CHECK OUT",
                        style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 15,
                            color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}
