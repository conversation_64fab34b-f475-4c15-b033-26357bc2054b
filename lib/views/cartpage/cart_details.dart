import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:tbb_web/Models/cart_items.dart';
import 'package:tbb_web/Models/productmodel.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/firebase.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:tbb_web/shared/router.dart';
import 'package:tbb_web/shared/theme.dart';
import 'package:tbb_web/views/common/responsive.dart';
import 'package:item_count_number_button/item_count_number_button.dart';

class Cartdetails extends StatefulWidget {
  const Cartdetails({
    super.key,
    required this.cartproduct,
  });
  final CartItemsModel cartproduct;

  @override
  State<Cartdetails> createState() => _CartdetailsState();
}

class _CartdetailsState extends State<Cartdetails> {
  bool typeis = true;
  num countvalue = 0;
  // CartItemsModel? widget.cartproduct;
  ProductModel? product;
  // num? qtyValue;
  // num? price;

  @override
  void initState() {
    super.initState();

    // product = Get.find<ProductCtrl>()
    //     .products
    //     .firstWhere((element) => element.docId == widget.cartproduct.pId);
    dataFetch();
  }

  dataFetch() async {
    product = await FBFireStore.product
        .where(FieldPath.documentId, isEqualTo: widget.cartproduct.pId)
        .where('notavailable', isEqualTo: false)
        .get()
        .then((value) => value.docs.isEmpty
            ? null
            : ProductModel.fromSnapshot(value.docs.first));
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return FutureBuilder(
        future: dataFetch(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return const Text("Facing some issue!!");
          } else if (product != null) {
            return GetBuilder<ProductCtrl>(builder: (pctrl) {
              typeis = (product!.type == "Flower" || product!.type == "Add-On");

              // product = Get.find<ProductCtrl>()
              //     .products
              //     .firstWhere((element) => element.docId == widget.cartproduct.pId);
              dataFetch();

              return ResponsiveWid(
                //mobile

                mobile: Padding(
                  padding: const EdgeInsets.only(top: 10.0, bottom: 20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // const Spacer(),
                            // InkWell(
                            //   onTap: () {
                            //     context
                            //         .go('${Routes.product}/${widget.cartproduct.pId}');
                            //   },
                            //   child: Image(
                            //     fit: BoxFit.cover,
                            //     height: 150,
                            //     width: 120,
                            //     image: NetworkImage(product!.images.first),
                            //   ),
                            // ),
                            SizedBox(
                              height: 150,
                              // width: 120,
                              child: AspectRatio(
                                aspectRatio: 0.75,
                                child: InkWell(
                                  onTap: () => context.go(
                                      '${Routes.product}/${widget.cartproduct.pId}'),
                                  child: Stack(
                                    fit: StackFit.expand,
                                    children: [
                                      Image.asset(
                                        "assets/images/TBB-Fix-BG.png",
                                        fit: BoxFit.cover,
                                        // width: size.width <= mobileMinsize
                                        //     ? 150
                                        //     : 250,
                                        // height: size.width <= mobileMinsize
                                        //     ? 200
                                        //     : 334,
                                      ),
                                      Image.network(
                                        product?.images.isEmpty ?? true
                                            ? ""
                                            : product?.images.first ?? "",
                                        fit: BoxFit.cover,
                                      )
                                      // Align(
                                      //   alignment: Alignment(0, 0),
                                      //   child: Padding(
                                      //     padding: const EdgeInsets.all(10.0),
                                      //     child: Image(
                                      //       // width: 300,
                                      //       // height: 400,
                                      //       fit: BoxFit.cover,
                                      //       image: NetworkImage(
                                      //           product?.images.isEmpty ?? true
                                      //               ? ""
                                      //               : product?.images.first ??
                                      //                   ""),
                                      //     ),
                                      //   ),
                                      // ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            // const Spacer(),
                            const SizedBox(
                              width: 10,
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      context.go(
                                          '${Routes.product}/${widget.cartproduct.pId}');
                                    },
                                    child: Text(
                                      maxLines: 2,
                                      widget.cartproduct.name.toUpperCase(),
                                      style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w500),
                                      // style: GoogleFonts.b612Mono(fontSize: 20),
                                    ),
                                  ),
                                  IntrinsicHeight(
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          top: 5.0, bottom: 10),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          // if (typeis = false)
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              if (widget.cartproduct
                                                      .selectedflavour !=
                                                  "")
                                                Text(
                                                  "Flavour : ${widget.cartproduct.selectedflavour}",
                                                ),
                                              if (widget.cartproduct
                                                      .weightSelected !=
                                                  "null")
                                                Text(
                                                    "${widget.cartproduct.weightSelected} KG"),
                                            ],
                                          ),
                                          // const VerticalDivider(),
                                          const SizedBox(
                                            width: 20,
                                          ),
                                          const Column(
                                            children: [
                                              // Text(
                                              //     "Day : ${widget.cartproduct?.delDate.day.compareTo(DateTime.now().day) == 0 ? "Today" : widget.cartproduct?.delDate.day.compareTo(DateTime.now().add(const Duration(days: 1)).day) == 00 ? "Tomorrow" : widget.cartproduct?.delDate.toString().split(" ").first}"),
                                              // Text("Time: ${widget.cartproduct?.deliveryTime}"),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Price : ",
                                        style: GoogleFonts.b612Mono(
                                          fontSize: 15,
                                        ),
                                      ),
                                      const Spacer(),
                                      Text(
                                        "Rs.${widget.cartproduct.price}",
                                        style:
                                            GoogleFonts.b612Mono(fontSize: 15),
                                      )
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(right: 20.0),
                                        child: IconButton(
                                          color: Colors.black,
                                          highlightColor: Colors.transparent,
                                          hoverColor: Colors.transparent,
                                          onPressed: () async {
                                            if (isLoggedIn()) {
                                              await FBFireStore.users
                                                  .doc(FBAuth
                                                      .auth.currentUser?.uid)
                                                  .update({
                                                "cartitems.${widget.cartproduct.id}":
                                                    FieldValue.delete()
                                              });
                                              // pctrl.update();
                                            } else {
                                              pctrl.orderProduct
                                                  .removeWhere((element) {
                                                return element.id ==
                                                    widget.cartproduct.id;
                                              });
                                            }
                                          },
                                          icon: const Icon(
                                            CupertinoIcons.delete_solid,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 5),
                                      ItemCount(
                                        initialValue: widget.cartproduct.qty,
                                        color: themeColor,
                                        minValue: 1,
                                        maxValue: 10,
                                        decimalPlaces: 0,
                                        onChanged: (value) {
                                          widget.cartproduct.qty = value;
                                          setState(() {
                                            final cartitem = <String, dynamic>{
                                              "pId": widget.cartproduct.pId,
                                              "name": widget.cartproduct.name,
                                              "selectedflavour": widget
                                                  .cartproduct.selectedflavour,
                                              "weightSelected": widget
                                                  .cartproduct.weightSelected,
                                              "price": widget.cartproduct.price,
                                              "messageOnCake": widget
                                                  .cartproduct.messageOnCake,
                                              "deliveryTime": widget
                                                  .cartproduct.deliveryTime,
                                              "delivery":
                                                  widget.cartproduct.delivery,
                                              // "area": widget.cartproduct!.area,
                                              "delDate": deldatefromat(
                                                  widget.cartproduct.delDate),
                                              "isLateNight": widget
                                                  .cartproduct.isLateNight,
                                              'qty': widget.cartproduct.qty
                                            };
                                            if (isLoggedIn()) {
                                              FBFireStore.users
                                                  .doc(FBAuth
                                                      .auth.currentUser?.uid)
                                                  .update({
                                                "cartitems.${widget.cartproduct.id}":
                                                    cartitem
                                              });
                                            } else {
                                              pctrl.userDetails?.cartitems
                                                      .firstWhere(
                                                    (element) {
                                                      return element.id ==
                                                          widget.cartproduct.id;
                                                    },
                                                  ).qty ==
                                                  widget.cartproduct.qty;

                                              //     Get.find<ProductCtrl>()
                                              //         .orderProduct[widget.index]
                                              //         .qty = qtyValue;
                                              //     print(Get.find<ProductCtrl>()
                                              //         .orderProduct[widget.index]
                                              //         .qty);
                                              //     print("object");
                                            }
                                            pctrl.update();
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.0),
                        child: Divider(),
                      )
                    ],
                  ),
                ),
                //tablet

                tablet: Padding(
                  padding: EdgeInsets.only(
                      top: 20,
                      left: size.width < 640 ? 10 : 20,
                      right: size.width < 640 ? 10 : 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          IconButton(
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            onPressed: () async {
                              if (isLoggedIn()) {
                                await FBFireStore.users
                                    .doc(FBAuth.auth.currentUser?.uid)
                                    .update({
                                  "cartitems.${widget.cartproduct.id}":
                                      FieldValue.delete()
                                });
                                // pctrl.update();
                              } else {
                                pctrl.orderProduct.removeWhere((element) {
                                  return element.id == widget.cartproduct.id;
                                });
                              }
                            },
                            icon: Icon(
                              CupertinoIcons.delete_solid,
                              size: size.width < 640 ? 20 : 30,
                            ),
                          ),
                          SizedBox(
                            width: size.width < 640 ? 10 : 20,
                          ),
                          SizedBox(
                            height: size.width < 640 ? 110 : 150,
                            width: size.width < 640 ? 90 : 120,
                            child: AspectRatio(
                              aspectRatio: 0.75,
                              child: InkWell(
                                onTap: () => context.go(
                                    '${Routes.product}/${widget.cartproduct.pId}'),
                                child: Stack(fit: StackFit.expand, children: [
                                  Image.asset(
                                    "assets/images/TBB-Fix-BG.png",
                                    fit: BoxFit.cover,
                                    // width: size.width <= mobileMinsize
                                    //     ? 150
                                    //     : 250,
                                    // height: size.width <= mobileMinsize
                                    //     ? 200
                                    //     : 334,
                                  ),
                                  Image.network(
                                    product?.images.isEmpty ?? true
                                        ? ""
                                        : product?.images.first ?? "",
                                    fit: BoxFit.cover,
                                  )
                                ]),
                                // Stack(
                                //   children: [
                                //     Image.asset(
                                //       "assets/images/TBB-Fix-BG.png",
                                //       fit: BoxFit.cover,
                                //       width: size.width <= mobileMinsize
                                //           ? 150
                                //           : 250,
                                //       height: size.width <= mobileMinsize
                                //           ? 200
                                //           : 334,
                                //     ),
                                //     Align(
                                //       alignment: Alignment(0, 0),
                                //       child: Padding(
                                //         padding: const EdgeInsets.all(10.0),
                                //         child: Image(
                                //           // width: 300,
                                //           // height: 400,
                                //           fit: BoxFit.cover,
                                //           image: NetworkImage(
                                //               product?.images.isEmpty ?? true
                                //                   ? ""
                                //                   : product?.images.first ??
                                //                       ""),
                                //         ),
                                //       ),
                                //     ),
                                //   ],
                                // ),
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 20,
                          ),
                          // InkWell(
                          //   onTap: () {
                          //     context.go('${Routes.product}/${widget.cartproduct.pId}');
                          //   },
                          //   child: Image(
                          //     fit: BoxFit.cover,
                          //     height: size.width < 640 ? 110 : 150,
                          //     width: size.width < 640 ? 90 : 120,
                          //     image: NetworkImage(product!.images.first),
                          //   ),
                          // ),
                          // const SizedBox(
                          //   width: 20,
                          // ),
                          InkWell(
                            onTap: () {
                              context.go(
                                  '${Routes.product}/${widget.cartproduct.pId}');
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      widget.cartproduct.name,
                                      style: TextStyle(
                                          fontSize: size.width < 640 ? 15 : 20,
                                          fontWeight: FontWeight.bold),
                                      // style: GoogleFonts.b612Mono(fontSize: 20),
                                    ),
                                    const SizedBox(width: 20),
                                    ItemCount(
                                      initialValue: widget.cartproduct.qty,
                                      color: themeColor,
                                      minValue: 1,
                                      maxValue: 10,
                                      decimalPlaces: 0,
                                      onChanged: (value) {
                                        widget.cartproduct.qty = value;
                                        setState(() {
                                          final cartitem = <String, dynamic>{
                                            "pId": widget.cartproduct.pId,
                                            "name": widget.cartproduct.name,
                                            "selectedflavour": widget
                                                .cartproduct.selectedflavour,
                                            "weightSelected": widget
                                                .cartproduct.weightSelected,
                                            "deliveryTime":
                                                widget.cartproduct.deliveryTime,
                                            "delivery":
                                                widget.cartproduct.delivery,
                                            "price": typeis
                                                ? product?.fixedprice
                                                : widget.cartproduct.price,
                                            //  "price": widget.cartproduct!.price,
                                            "messageOnCake": widget
                                                .cartproduct.messageOnCake,
                                            // "area": pctrl.area,
                                            "delDate": deldatefromat(
                                                widget.cartproduct.delDate),
                                            "isLateNight":
                                                widget.cartproduct.isLateNight,
                                            'qty': widget.cartproduct.qty
                                          };
                                          if (isLoggedIn()) {
                                            FBFireStore.users
                                                .doc(FBAuth
                                                    .auth.currentUser?.uid)
                                                .update({
                                              "cartitems.${widget.cartproduct.id}":
                                                  cartitem
                                            });
                                          } else {
                                            pctrl.userDetails?.cartitems
                                                    .firstWhere(
                                                  (element) {
                                                    return element.id ==
                                                        widget.cartproduct.id;
                                                  },
                                                ).qty ==
                                                widget.cartproduct.qty;

                                            //     Get.find<ProductCtrl>()
                                            //         .orderProduct[widget.index]
                                            //         .qty = qtyValue;
                                            //     print(Get.find<ProductCtrl>()
                                            //         .orderProduct[widget.index]
                                            //         .qty);
                                            //     print("object");
                                          }
                                          pctrl.update();
                                        });
                                      },
                                    ),
                                  ],
                                ),
                                if (widget.cartproduct.selectedflavour != "")
                                  Text(
                                      "Flavour : ${widget.cartproduct.selectedflavour}"),
                                if (widget.cartproduct.weightSelected != "null")
                                  Text(
                                      "${widget.cartproduct.weightSelected} KG"),
                                const SizedBox(
                                    width: 250,
                                    child: Divider(
                                      color: Colors.black,
                                    )),
                                // Text(
                                //     "Day : ${widget.cartproduct?.delDate.day.compareTo(DateTime.now().day) == 0 ? "Today" : widget.cartproduct?.delDate.day.compareTo(DateTime.now().add(const Duration(days: 1)).day) == 00 ? "Tomorrow" : widget.cartproduct?.delDate.toString().split(" ").first}"),
                                // Text("Time: ${widget.cartproduct?.deliveryTime}"),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Total",
                                      style: GoogleFonts.b612Mono(
                                        fontSize: size.width < 640 ? 20 : 22,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 20,
                                    ),
                                    Text(
                                      "Rs.${widget.cartproduct.price}",
                                      style: GoogleFonts.b612Mono(fontSize: 20),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),

                //desktop

                desktop: Padding(
                  padding: const EdgeInsets.only(
                      left: 50.0, bottom: 50, top: 50, right: 110),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          IconButton(
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            onPressed: () async {
                              if (isLoggedIn()) {
                                await FBFireStore.users
                                    .doc(FBAuth.auth.currentUser?.uid)
                                    .update({
                                  "cartitems.${widget.cartproduct.id}":
                                      FieldValue.delete()
                                });
                                // pctrl.update();
                              } else {
                                pctrl.orderProduct.removeWhere((element) =>
                                    element.id == widget.cartproduct.id);
                              }
                              pctrl.update();
                              setState(() {});
                            },
                            icon: const Icon(
                              CupertinoIcons.delete_solid,
                              size: 30,
                            ),
                          ),
                          const SizedBox(
                            width: 20,
                          ),
                          SizedBox(
                            height: 150,
                            width: 120,
                            child: AspectRatio(
                              aspectRatio: 0.75,
                              child: InkWell(
                                onTap: () => context.go(
                                    '${Routes.product}/${widget.cartproduct.pId}'),
                                child: Stack(fit: StackFit.expand, children: [
                                  Image.asset(
                                    "assets/images/TBB-Fix-BG.png",
                                    fit: BoxFit.cover,
                                    // width: size.width <= mobileMinsize
                                    //     ? 150
                                    //     : 250,
                                    // height: size.width <= mobileMinsize
                                    //     ? 200
                                    //     : 334,
                                  ),
                                  Image.network(
                                    product?.images.isEmpty ?? true
                                        ? ""
                                        : product?.images.first ?? "",
                                    fit: BoxFit.cover,
                                  )
                                ]),
                                // Stack(
                                //   children: [
                                //     Image.asset(
                                //       "assets/images/TBB-Fix-BG.png",
                                //       fit: BoxFit.cover,
                                //       width: size.width <= mobileMinsize
                                //           ? 150
                                //           : 250,
                                //       height: size.width <= mobileMinsize
                                //           ? 200
                                //           : 334,
                                //     ),
                                //     Align(
                                //       alignment: Alignment(0, 0),
                                //       child: Padding(
                                //         padding: const EdgeInsets.all(10.0),
                                //         child: Image(
                                //           // width: 300,
                                //           // height: 400,
                                //           fit: BoxFit.cover,
                                //           image: NetworkImage(
                                //               product?.images.isEmpty ?? true
                                //                   ? ""
                                //                   : product?.images.first ??
                                //                       ""),
                                //         ),
                                //       ),
                                //     ),
                                //   ],
                                // ),
                              ),
                            ),
                          ),
                          // InkWell(
                          //   overlayColor:
                          //       const WidgetStatePropertyAll(Colors.transparent),
                          //   highlightColor: Colors.transparent,
                          //   hoverColor: Colors.transparent,
                          //   onTap: () {
                          //     context.go('${Routes.product}/${widget.cartproduct.pId}');
                          //   },
                          //   child: Image(
                          //     fit: BoxFit.cover,
                          //     height: 150,
                          //     width: 120,
                          //     image: NetworkImage(product?.images.first ?? ""),
                          //   ),
                          // ),
                          const SizedBox(
                            width: 20,
                          ),
                          InkWell(
                            overlayColor: const WidgetStatePropertyAll(
                                Colors.transparent),
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            onTap: () {
                              context.go(
                                  '${Routes.product}/${widget.cartproduct.pId}');
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      overflow: TextOverflow.ellipsis,
                                      widget.cartproduct.name,
                                      style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold),
                                      // style: GoogleFonts.b612Mono(fontSize: 20),
                                    ),
                                    const SizedBox(width: 20),
                                    ItemCount(
                                      initialValue: widget.cartproduct.qty,
                                      color: themeColor,
                                      minValue: 1,
                                      maxValue: 10,
                                      decimalPlaces: 0,
                                      onChanged: (value) {
                                        widget.cartproduct.qty = value;
                                        setState(() {
                                          final cartitem = <String, dynamic>{
                                            "pId": widget.cartproduct.pId,
                                            "name": widget.cartproduct.name,
                                            "selectedflavour": widget
                                                .cartproduct.selectedflavour,
                                            "weightSelected": widget
                                                .cartproduct.weightSelected,
                                            "deliveryTime":
                                                widget.cartproduct.deliveryTime,
                                            "delivery":
                                                widget.cartproduct.delivery,
                                            "price": typeis
                                                ? product?.fixedprice
                                                : widget.cartproduct.price,
                                            //  "price": widget.cartproduct!.price,
                                            "messageOnCake": widget
                                                .cartproduct.messageOnCake,
                                            // "area": pctrl.area,
                                            "delDate": deldatefromat(
                                                widget.cartproduct.delDate),
                                            "isLateNight":
                                                widget.cartproduct.isLateNight,
                                            'qty': widget.cartproduct.qty
                                          };
                                          if (isLoggedIn()) {
                                            FBFireStore.users
                                                .doc(FBAuth
                                                    .auth.currentUser?.uid)
                                                .update({
                                              "cartitems.${widget.cartproduct.id}":
                                                  cartitem
                                            });
                                          } else {
                                            pctrl.userDetails?.cartitems
                                                    .firstWhere(
                                                  (element) {
                                                    return element.id ==
                                                        widget.cartproduct.id;
                                                  },
                                                ).qty ==
                                                widget.cartproduct.qty;

                                            //     Get.find<ProductCtrl>()
                                            //         .orderProduct[widget.index]
                                            //         .qty = qtyValue;
                                            //     print(Get.find<ProductCtrl>()
                                            //         .orderProduct[widget.index]
                                            //         .qty);
                                            //     print("object");
                                          }
                                          pctrl.update();
                                        });
                                      },
                                    ),
                                  ],
                                ),
                                if (widget.cartproduct.selectedflavour != "")
                                  Text(
                                      "Flavour : ${widget.cartproduct.selectedflavour}"),
                                if (widget.cartproduct.weightSelected != "null")
                                  Text(
                                      "${widget.cartproduct.weightSelected} KG"),
                                const SizedBox(
                                    width: 250,
                                    child: Divider(
                                      color: Colors.black,
                                    )),
                                // Text(
                                //     "Day : ${widget.cartproduct?.delDate.day.compareTo(DateTime.now().day) == 0 ? "Today" : widget.cartproduct?.delDate.day.compareTo(DateTime.now().add(const Duration(days: 1)).day) == 00 ? "Tomorrow" : widget.cartproduct?.delDate.toString().split(" ").first}"),
                                // Text("Time: ${widget.cartproduct?.deliveryTime}"),
                              ],
                            ),
                          ),
                          const Spacer(),
                          // const SizedBox(
                          //   width: 600,
                          // ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                "Total",
                                style: GoogleFonts.b612Mono(
                                  fontSize: 28,
                                ),
                              ),
                              Text(
                                typeis
                                    ? "Rs.${(product?.fixedprice ?? 0) * widget.cartproduct.qty}"
                                    : "Rs.${widget.cartproduct.price * widget.cartproduct.qty}",
                                style: GoogleFonts.b612Mono(fontSize: 20),
                              )
                            ],
                          )
                        ],
                      ),
                      const Divider(
                        color: Color.fromARGB(255, 202, 199, 199),
                        indent: 20,
                        endIndent: 20,
                      ),
                    ],
                  ),
                ),
              );
            });
          } else {
            return Padding(
              padding: const EdgeInsets.all(60.0),
              child: Center(
                child: SizedBox(
                  child: LoadingAnimationWidget.staggeredDotsWave(
                    color: themeColor,
                    size: 30,
                  ),
                ),
              ),
            );
          }
        });
  }
}
