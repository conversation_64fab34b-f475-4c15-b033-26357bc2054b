import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:tbb_web/Models/order_product_model.dart';

class OrderModel {
  final String docId;
  final List<OrderProduct> orderProductsMap;
  final DateTime createdAt;
  final DateTime? paidOn;
  final DateTime? fulfilledDate;
  final String address;
  final num subTotal;
  final num shippingPrice;
  num? gst;
  final num total;
  late String transId;
  late bool isPaid;
  final String oId;
  final String uId;
  final Map<String, dynamic>? discountCoupon;
  String? note;
  final String? area;
  final String? name;
  final String? pincode;
  final String? phone;
  final String? email;
  final String status;
  late String? orderNo;
  late String? fulfilledNote;
  // final DateTime delDate;
  // final bool isLateNight;
  // final String deliveryTime;
  // final String delivery;

  OrderModel({
    required this.docId,
    required this.orderProductsMap,
    required this.createdAt,
    this.paidOn,
    this.fulfilledDate,
    required this.address,
    required this.subTotal,
    required this.shippingPrice,
    required this.status,
    required this.gst,
    required this.name,
    required this.phone,
    required this.email,
    required this.pincode,
    required this.total,
    required this.transId,
    required this.orderNo,
    required this.isPaid,
    required this.oId,
    required this.uId,
    required this.discountCoupon,
    required this.note,
    required this.area,
    required this.fulfilledNote,
    // required this.delDate,
    // required this.isLateNight,
    // required this.deliveryTime,
    // required this.delivery,
  });

  factory OrderModel.fromJson(String id, Map<String, dynamic> json) {
    return OrderModel(
      docId: id,

      orderProductsMap: [],
      // for (var item in json['orderProducts'])
      //   item['productId']: OrderProduct.fromJson(item)

      createdAt: DateTime.parse(json['createdAt']),
      paidOn: DateTime.parse(json['paidOn']),
      fulfilledDate: DateTime.parse(json['fulfilledDate']),
      address: json['address'],
      subTotal: json['subTotal'],
      shippingPrice: json['shippingPrice'],
      status: json['status'],
      name: json['name'],
      phone: json['phone'],
      email: json['email'],
      pincode: json['pincode'],
      gst: json['gst'] ?? 0,
      total: json['total'],
      transId: json['transId'],
      isPaid: json['isPaid'],
      oId: json['oId'],
      uId: json['uId'],
      discountCoupon: Map<String, dynamic>.from(json['discountCoupon']),
      note: json['note'] ?? "",
      area: json['area'], orderNo: json['orderNo'],
      fulfilledNote: json['fulfilledNote'],
      // isLateNight: json['isLateNight'], deliveryTime: json['deliveryTime'],
      // delivery: json['delivery'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      // 'orderProducts': orderProductsMap.values.map((x) => x.toJson()).toList(),
      'orderProducts': [],
      'createdAt': createdAt?.toIso8601String(),
      'paidOn': paidOn?.toIso8601String(),
      'fulfilledDate': fulfilledDate?.toIso8601String(),
      'address': address,
      'subTotal': subTotal,
      'shippingPrice': shippingPrice,
      'phone': phone,
      'email': email,
      'name': name,
      'pincode': pincode,
      'gst': gst,
      'total': total,
      'transId': transId,
      'isPaid': isPaid,
      'status': status,
      'oId': oId,
      'uId': uId,
      'discountCoupon': discountCoupon,
      'note': note,
      'area': area,
      'orderNo': orderNo,
      // 'delDate': delDate.toString(),
      // 'isLateNight': isLateNight,
      // 'deliveryTime': deliveryTime,
      // 'delivery': delivery,
    };
  }

  factory OrderModel.fromSnapshot(QueryDocumentSnapshot snapshot) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;

    return OrderModel(
      docId: snapshot.id,
      orderProductsMap: Map.from(data['orderProductsMap']).entries.map(
        (e) {
          return OrderProduct.fromJson(e.key, e.value);
        },
      ).toList(),
      createdAt: data['createdAt'] == null
          ? DateTime.now()
          : (data['createdAt'] as Timestamp).toDate(),
      paidOn: (data['paidOn'] as Timestamp?)?.toDate(),
      fulfilledDate: data['fulfilledDate'] == null
          ? null
          : (data['fulfilledDate'] as Timestamp).toDate(),
      address: data['address'],
      subTotal: data['subTotal'],
      shippingPrice: data['shippingPrice'],
      name: data['name'],
      pincode: data['pincode'],
      orderNo: data['orderNo'],
      fulfilledNote: data['fulfilledNote'],
      status: data['status'],
      phone: data['phone'],
      email: data['email'],
      gst: data['gst'] ?? 0,
      total: data['total'],
      transId: data['transId'] ?? "",
      isPaid: data['isPaid'],
      oId: data['oId'],
      uId: data['uId'],
      discountCoupon: {},
      // Map<String, dynamic>.from(data['discountCoupon']),
      note: data['note'],
      area: data['area'] ?? "-",
      // delDate: data['delDate'].toDate(),
      // isLateNight: data['isLateNight'] ?? false,
      // deliveryTime: data['deliveryTime'] ?? "-",
      // delivery: data['delivery'] ?? "-"
    );
  }

  factory OrderModel.fromDocSnapshot(DocumentSnapshot snapshot) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;

    return OrderModel(
      docId: snapshot.id,
      orderProductsMap: Map.from(data['orderProductsMap']).entries.map(
        (e) {
          return OrderProduct.fromJson(e.key, e.value);
        },
      ).toList(),
      createdAt: data['createdAt'] == null
          ? DateTime.now()
          : (data['createdAt'] as Timestamp).toDate(),
      paidOn: (data['paidOn'] as Timestamp).toDate(),
      fulfilledDate: data['fulfilledDate'] == null
          ? null
          : (data['fulfilledDate'] as Timestamp).toDate(),
      address: data['address'],
      subTotal: data['subTotal'],
      shippingPrice: data['shippingPrice'],
      name: data['name'],
      pincode: data['pincode'],
      orderNo: data['orderNo'],
      fulfilledNote: data['fulfilledNote'],
      status: data['status'],
      phone: data['phone'],
      email: data['email'],
      gst: data['gst'] ?? 0,
      total: data['total'],
      transId: data['transId'] ?? "",
      isPaid: data['isPaid'],
      oId: data['oId'],
      uId: data['uId'],
      discountCoupon: {},
      // Map<String, dynamic>.from(data['discountCoupon']),
      note: data['note'],
      area: data['area'] ?? "-",
      // delDate: data['delDate'].toDate(),
      // isLateNight: data['isLateNight'] ?? false,
      // deliveryTime: data['deliveryTime'] ?? "-",
      // delivery: data['delivery'] ?? "-"
    );
  }
}

// class OrderModel {
//   final String docId;
//   final Map<String, OrderProduct> orderProductsMap;
//   final DateTime time;
//   final String address;
//   final num subTotal;
//   final num gst;
//   final num total;
//   final String transId;
//   final bool isPaid;
//   final String oId;
//   final String uId;
//   final Map<String, dynamic>? discountCoupon;
//   final String note;
//   final String area;
//   // final DateTime delDate;
//   // final bool isLateNight;
//   // final String deliveryTime;
//   // final String delivery;

//   OrderModel({
//     required this.docId,
//     required this.orderProductsMap,
//     required this.time,
//     required this.address,
//     required this.subTotal,
//     required this.gst,
//     required this.total,
//     required this.transId,
//     required this.isPaid,
//     required this.oId,
//     required this.uId,
//     required this.discountCoupon,
//     required this.note,
//     required this.area,
//     // required this.delDate,
//     // required this.deliveryTime,
//     // required this.isLateNight,
//     // required this.delivery,
//   });

//   factory OrderModel.fromJson(Map<String, dynamic> json) {
//     return OrderModel(
//       docId: json['docId'],
//       orderProductsMap: {
//         for (var item in json['orderProducts'])
//           item['productId']: OrderProduct.fromJson(item)
//       },
//       time: DateTime.parse(json['time']),
//       address: json['address'],
//       subTotal: json['subTotal'],
//       gst: json['gst'],
//       total: json['total'],
//       transId: json['transId'],
//       isPaid: json['isPaid'],
//       oId: json['oId'],
//       uId: json['uId'],
//       discountCoupon: Map<String, dynamic>.from(json['discountCoupon']),
//       // deliveryTime: json['deliveryTime'],
//       note: json['note'],
//       area: json['area'],
//       // delDate: DateTime.parse(json['delDate']),
//       // isLateNight: json['isLateNight'],
//       // delivery: json['delivery'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'docId': docId,
//       'orderProducts': orderProductsMap.values.map((x) => x.toJson()).toList(),
//       'time': time.toIso8601String(),
//       'address': address,
//       'subTotal': subTotal,
//       'gst': gst,
//       'total': total,
//       'transId': transId,
//       'isPaid': isPaid,
//       'oId': oId,
//       'uId': uId,
//       'discountCoupon': discountCoupon,
//       // 'deliveryTime': deliveryTime,
//       'note': note,
//       'area': area,
//       // 'delDate': delDate.toIso8601String(),
//       // 'isLateNight': isLateNight,
//     };
//   }

//   factory OrderModel.fromSnapshot(DocumentSnapshot snapshot) {
//     Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
//     return OrderModel(
//       docId: snapshot.id,
//       orderProductsMap: {
//         for (var item in data['orderProducts'])
//           item['productId']: OrderProduct.fromJson(item)
//       },
//       time: (data['time'] as Timestamp).toDate(),
//       address: data['address'],
//       subTotal: data['subTotal'],
//       gst: data['gst'],
//       total: data['total'],
//       transId: data['transId'],
//       isPaid: data['isPaid'],
//       oId: data['oId'],
//       uId: data['uId'],
//       discountCoupon: Map<String, dynamic>.from(data['discountCoupon']),
//       note: data['note'],
//       area: data['area'],
//       // delDate: data['delDate'],
//       // isLateNight: data['isLateNight'],
//       // deliveryTime: data['deliverytime'],
//       // delivery: data['delivery'],
//     );
//   }
// }
