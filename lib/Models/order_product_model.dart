class OrderProduct {
  final String id;
  final String pId;
  final String name;
  final String selectedflavour;
  final String? sku;
  final String? weightSelected;
  final String? imageUrl;
  final num price;
  final num qty;
  final num taxPercent;
  final String? messageOnCake;
  final DateTime delDate;
  final bool isLateNight;
  final String deliveryTime;
  final String delivery;
  final String? itemFulfilled;

  OrderProduct({
    required this.id,
    required this.pId,
    required this.name,
    required this.selectedflavour,
    required this.sku,
    required this.weightSelected,
    required this.imageUrl,
    required this.price,
    required this.qty,
    required this.taxPercent,
    required this.messageOnCake,
    required this.delDate,
    required this.isLateNight,
    required this.deliveryTime,
    required this.delivery,
    required this.itemFulfilled,
  });

  factory OrderProduct.fromJson(String id, Map<String, dynamic> json) {
    return OrderProduct(
      id: id,
      pId: json['pId'],
      name: json['name'],
      selectedflavour: json['selectedflavour'],
      sku: json['sku'],
      qty: json['qty'],
      weightSelected: json['weightSelected'],
      imageUrl: json['imageUrl'],
      price: json['price'] ?? "",
      taxPercent: json['taxPercent'] ?? "",
      messageOnCake: json['messageOnCake'],
      delDate: DateTime.tryParse(json['delDate']) ?? DateTime.now(),
      isLateNight: json['isLateNight'],
      deliveryTime: json['deliveryTime'],
      itemFulfilled: json['itemFulfilled'],
      delivery: json['delivery'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pId': pId,
      'name': name,
      'qty': qty,
      'selectedflavour': selectedflavour,
      'sku': sku,
      'weightSelected': weightSelected,
      'imageUrl': imageUrl,
      'price': price,
      'taxPercent': taxPercent,
      'messageOnCake': messageOnCake,
      'delDate': delDate.toIso8601String(),
      'isLateNight': isLateNight,
      'deliveryTime': deliveryTime,
      'delivery': delivery,
      'itemFulfilled': itemFulfilled,
    };
  }
}

// class OrderProduct {
//   final String pId;
//   final String name;
//   final String selectedflavour;
//   final String sku;
//   final String weightSelected;
//   final String imageUrl;
//   final num price;
//   final num designPrice;
//   final num taxPercent;
//   final String messageOnCake;

//   OrderProduct({
//     required this.pId,
//     required this.name,
//     required this.selectedflavour,
//     required this.sku,
//     required this.weightSelected,
//     required this.imageUrl,
//     required this.designPrice,
//     required this.taxPercent,
//     required this.price,
//     required this.messageOnCake,
//   });

//   factory OrderProduct.fromJson(Map<String, dynamic> json) {
//     return OrderProduct(
//       pId: json['pId'],
//       name: json['name'],
//       selectedflavour: json['selectedflavour'],
//       sku: json['sku'],
//       weightSelected: json['weightSelected'],
//       imageUrl: json['imageUrl'],
//       price: json['price'],
//       designPrice: json['designPrice'] ?? "",
//       taxPercent: json['taxPercent'] ?? "",
//       messageOnCake: json['messageOnCake'],
//     );
//   }

//   // To JSON
//   Map<String, dynamic> toJson() {
//     return {
//       'pId': pId,
//       'name': name,
//       'selectedflavour': selectedflavour,
//       'sku': sku,
//       'weightSelected': weightSelected,
//       'imageUrl': imageUrl,
//       'price': price,
//       'designPrice': designPrice,
//       'taxPercent': taxPercent,
//       'messageOnCake': messageOnCake,
//     };
//   }

//   // From Firestore snapshot
//   factory OrderProduct.fromSnapshot(DocumentSnapshot snapshot) {
//     Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
//     return OrderProduct.fromJson(data);
//   }
// }

