import 'package:cloud_firestore/cloud_firestore.dart';

class WeightRangeModel {
  final String docId;
  final List<num> weightlist;

  WeightRangeModel({
    required this.docId,
    required this.weightlist,
  });

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'weightlist': weightlist,
    };
  }

  factory WeightRangeModel.fromJson(Map<String, dynamic> json) {
    return WeightRangeModel(
      docId: json['docId'],
      weightlist:
          (json['weightlist'] as List<dynamic>).map((e) => e as num).toList(),
    );
  }
  factory WeightRangeModel.fromSnapshot(DocumentSnapshot snapshot) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    return WeightRangeModel(
      docId: snapshot.id,
      weightlist:
          (data['weightlist'] as List<dynamic>).map((e) => e as num).toList(),
    );
  }
}
