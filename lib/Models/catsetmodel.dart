import 'package:cloud_firestore/cloud_firestore.dart';

class CatSetModel {
  final String id;
  final String name;
  final List<String> subCats;

  CatSetModel({
    required this.id,
    required this.name,
    required this.subCats,
  });

  factory CatSetModel.fromJson(String vId, Map<String, dynamic> json) {
    return CatSetModel(
      id: vId,
      name: json['name'],
      subCats: List<String>.from(json['subCats']),
    );
  }

  toJson() {
    return {
      id: {
        'name': name,
        'subCats': subCats,
      }
    };
  }

  factory CatSetModel.fromSnapshot(DocumentSnapshot snapshot) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    return CatSetModel(
      id: snapshot.id,
      name: data['name'] ?? '',
      subCats: data['subCats'] ?? '',
    );
  }
  // factory CatSetModel.fromJson(Map<String, dynamic> json) {
  //   return CatSetModel(
  //     id: json['id'],
  //     name: json['name'],
  //     subCats: List<String>.from(json['subCats']),
  //   );
  // }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'id': id,
  //     'name': name,
  //     'subCats': subCats,
  //   };
  // }

  // factory CatSetModel.fromSnapshot(DocumentSnapshot snapshot) {
  //   // Extract data from the snapshot using data() method
  //   Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;

  //   return CatSetModel(
  //     id: data['id'],
  //     name: data['name'],
  //     subCats: List<String>.from(data['subCats']),
  //   );
  // }
}
