class OurCollectionModel {
  final String name;
  final String image;
  final String id;

  OurCollectionModel(
      {required this.name, required this.image, required this.id});

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'image': image,
      'id': id,
    };
  }

  factory OurCollectionModel.fromJson(docId, Map<String, dynamic> json) {
    // print(json);
    return OurCollectionModel(
      name: json['name'],
      image: json['image'],
      id: json['id'],
    );
  }
}
