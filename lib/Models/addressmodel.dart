class AddressModel {
  final String docId;
  final String name;
  final String area;
  final String address;
  final String city;
  final String state;
  final String pincode;
  final String phone;
  final String? email;
  // final DateTime delDate;
  // final bool isLateNight;
  // final String deliveryTime;
  // final String delivery;

  AddressModel({
    // required this.delDate,
    // required this.isLateNight,
    // required this.delivery,
    // required this.deliveryTime,
    required this.docId,
    required this.address,
    required this.name,
    required this.area,
    required this.city,
    required this.state,
    required this.pincode,
    required this.phone,
    required this.email,
  });

  factory AddressModel.fromJson(docId, Map<String, dynamic> json) {
    // print(json);
    return AddressModel(
      docId: docId,
      // delivery: json['delivery'],
      // deliveryTime: json['deliveryTime'],
      // delDate: DateTime.parse(
      // json['delDate'] as String? ?? DateTime.now().toIso8601String()),
      // delDate: DateTime.tryParse(json['delDate']) ?? DateTime.now(),
      // isLateNight: json['isLateNight'],
      name: json['name'],
      area: json['area'],
      city: json['city'],
      address: json['address'],
      state: json['state'],
      pincode: json['pincode'],
      phone: json['phone'],
      email: json['email'],
    );
  }

  toJson() {
    return {
      docId: {
        // 'delivery': delivery,
        // 'deliveryTime': deliveryTime,
        // 'delDate': delDate.toIso8601String(),
        // 'isLateNight': isLateNight,
        'name': name,
        'area': area,
        'city': city,
        'address': address,
        'state': state,
        'pincode': pincode,
        'phone': phone,
        'email': email,
      }
    };
  }
}
