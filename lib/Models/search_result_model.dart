import 'package:tbb_web/Models/maincategorymodel.dart';
import 'package:tbb_web/Models/productmodel.dart';

class SearchRequirementModel {
  final MainCategoryModel? mainCategory;
  final String? subCategoriesModel;
  final ProductModel? product;

  SearchRequirementModel({
    this.mainCategory,
    this.subCategoriesModel,
    this.product,
  });

  String type() {
    if (mainCategory != null) {
      return SearchResultTypes.mainCategory;
    } else if (subCategoriesModel != null) {
      return SearchResultTypes.subCategoriesModel;
    } else {
      return SearchResultTypes.product;
    }
  }
}

class SearchResultTypes {
  static const mainCategory = "mainCategory";
  static const subCategoriesModel = "subCategoriesModel";
  static const product = "product";
}
