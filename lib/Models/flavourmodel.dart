import 'package:cloud_firestore/cloud_firestore.dart';

class FlavourModel {
  final String docId;
  final String name;
  final num perKgPrice;
  final bool ischocolate;
  final bool status;

  FlavourModel({
    required this.docId,
    required this.name,
    required this.perKgPrice,
    required this.ischocolate,
    required this.status,
  });

  factory FlavourModel.fromJson(Map<String, dynamic> json) {
    return FlavourModel(
      docId: json['docId'],
      name: json['name'],
      perKgPrice: json['perKgPrice'],
      ischocolate: json['ischocolate'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'perKgPrice': perKgPrice,
      'ischocolate': ischocolate,
      'status': status,
    };
  }

  factory FlavourModel.fromSnapshot(DocumentSnapshot snapshot) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    return FlavourModel(
      docId: snapshot.id,
      name: data['name'] ?? '',
      perKgPrice: data['perKgPrice'] ?? 0,
      ischocolate: data['ischocolate'] ?? false,
      status: data['status'] ?? false,
    );
  }
}
