import 'package:cloud_firestore/cloud_firestore.dart';

class DessertModel {
  final String docId;
  final String name;
  final String type;
  final String tags;
  final DateTime createdAt;
  final String sku;
  final List<String> images;
  final String desc;
  final bool available;
  final bool isdessert;
  final bool isflower;
  final num fixedprice;

  DessertModel({
    required this.docId,
    required this.name,
    required this.type,
    required this.tags,
    required this.createdAt,
    required this.sku,
    required this.images,
    required this.desc,
    required this.available,
    required this.isdessert,
    required this.isflower,
    required this.fixedprice,
  });

  factory DessertModel.fromJson(Map<String, dynamic> json) {
    return DessertModel(
      docId: json['docId'],
      name: json['name'],
      type: json['type'],
      tags: json['tags'],
      isdessert: json['isdessert'],
      isflower: json['isflower'],
      fixedprice: json['fixedprice'],
      createdAt: DateTime.parse(json['createdAt']),
      sku: json['sku'],
      images: List<String>.from(json['images']),
      desc: json['desc'],
      available: json['available'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'type': type,
      'tags': tags,
      'isdessert': isdessert,
      'isflower': isflower,
      'fixedprice': fixedprice,
      'createdAt': createdAt.toIso8601String(),
      'sku': sku,
      'images': images,
      'desc': desc,
      'available': available,
    };
  }

  factory DessertModel.fromSnapshot(DocumentSnapshot snapshot) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    return DessertModel(
      docId: snapshot.id,
      name: data['name'],
      type: data['type'],
      tags: data['tags'],
      isdessert: data['isdessert'],
      isflower: data['isflower'],
      fixedprice: data['fixedprice'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      sku: data['sku'],
      images: List<String>.from(data['images']),
      desc: data['desc'],
      available: data['available'],
    );
  }
}
