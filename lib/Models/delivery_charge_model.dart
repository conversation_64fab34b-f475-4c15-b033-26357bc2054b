class DelchargeModel {
  final String id;
  final String name;
  final String pincode;
  final num price;

  DelchargeModel({
    required this.id,
    required this.name,
    required this.pincode,
    required this.price,
  });

  factory DelchargeModel.fromJson(String vId, Map<String, dynamic> json) {
    return DelchargeModel(
      id: vId,
      name: json['name'],
      pincode: json['pincode'],
      price: json['price'],
    );
  }

  toJson() {
    return {
      id: {
        'name': name,
        'pincode': pincode,
        'price': price,
      }
    };
  }
}
