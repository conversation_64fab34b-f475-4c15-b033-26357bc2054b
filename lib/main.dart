import 'dart:ui';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:tbb_web/shared/methods.dart';
import 'package:url_strategy/url_strategy.dart';
import 'firebase_options.dart';
import 'shared/router.dart';
import 'shared/theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    Get.put(ProductCtrl());

    // trackFacebookEvent('PageView');
  }

  // void trackFacebookEvent(String eventName,
  //     [Map<String, dynamic>? parameters]) {
  //   if (parameters != null) {
  //     js.context.callMethod('fbq', ['track', eventName, parameters]);
  //   } else {
  //     js.context.callMethod('fbq', ['track', eventName]);
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    setPathUrlStrategy();
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      scaffoldMessengerKey: snackbarKey,
      title: 'The Bakers Bar',
      routerConfig: appRouter,
      theme: themeData,
      scrollBehavior: MyCustomScrollBehavior(),
    );
  }
}

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.stylus,
        PointerDeviceKind.unknown,
        PointerDeviceKind.trackpad,
      };
}
