import 'dart:math';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/instance_manager.dart';
import 'package:intl/intl.dart';
import 'package:tbb_web/controllers/product_ctrl.dart';
import 'package:flutter/material.dart';
import 'package:js/js.dart';
import 'package:js/js_util.dart' as js_util;
import '../services/image_picker.dart';
import 'firebase.dart';

bool isLoggedIn() => FBAuth.auth.currentUser != null;

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomId(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

String formatDate(DateTime date) {
  // Extract year, month, and day from the DateTime object
  String year = date.year.toString();
  String month =
      date.month.toString().padLeft(2, '0'); // Ensure two digits for month
  String day = date.day.toString().padLeft(2, '0'); // Ensure two digits for day

  // Concatenate the formatted components with "-"
  return '$day-$month-$year';
}

String deldatefromat(DateTime date) {
  // Extract year, month, and day from the DateTime object
  String year = date.year.toString();
  String month =
      date.month.toString().padLeft(2, '0'); // Ensure two digits for month
  String day = date.day.toString().padLeft(2, '0'); // Ensure two digits for day

  // Concatenate the formatted components with "-"
  return '$year-$month-$day';
}

// num priceCalc(num? flavourprice, num? indexcount, DesignCostModel? designcost,
//     num? selectedweight) {
//   num incr = (designcost?.increment ?? 0) * (indexcount ?? 0 + 1);
//   num price = (flavourprice ?? 0) * (selectedweight ?? 0) +
//       incr +
//       (designcost?.basePrice ?? 0);
//   return price;
// }

showAppSnack(String message,
    {SNACKBARTYPE snackbartype = SNACKBARTYPE.normal, Duration? duration}) {
  try {
    return;
    // return Flushbar(
    //   message: message,
    //   icon: Icon(
    //     Icons.info_outline,
    //     size: 28.0,
    //     color: _getIconColor(snackbartype),
    //   ),
    //   backgroundColor: _getColor(snackbartype),
    //   duration: duration ?? const Duration(seconds: 3),
    //   leftBarIndicatorColor: _getIconColor(snackbartype),
    // ).show(appRouter.configuration.navigatorKey.currentContext!);
  } catch (e) {
    debugPrint(e.toString());
  }
}

final GlobalKey<ScaffoldMessengerState> snackbarKey =
    GlobalKey<ScaffoldMessengerState>();
showAppSnackBar(String message,
        {SnackBarAction? action,
        Duration duration = const Duration(milliseconds: 1500)}) =>
    snackbarKey.currentState?.showSnackBar(SnackBar(
      content: Text(
        message,
        style: TextStyle(color: Colors.white),
      ),
      action: action,
      duration: duration,
    ));

// Color _getIconColor(SNACKBARTYPE snackbartype) {
//   switch (snackbartype) {
//     case SNACKBARTYPE.error:
//       return Colors.grey.shade200;
//     default:
//       return themeColor;
//   }
// }

// Color _getColor(SNACKBARTYPE snackbartype) {
//   switch (snackbartype) {
//     case SNACKBARTYPE.error:
//       return Colors.red.shade700;
//     default:
//       return Colors.black;
//   }
// }

enum SNACKBARTYPE {
  normal,
  error,
}
/* 
Future<String?> uploadFile(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.category.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadFileFood(SelectedImage imageFile) async {
  try {
    // print('image/${imageFile.extention}');
    final imageRef = FBStorage.food.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(
        imageFile.uInt8List,
        SettableMetadata(
          contentType: 'image/${imageFile.extention}',
        ));
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
} */

extension MetaWid on DateTime {
  String goodDate() {
    try {
      return DateFormat.yMMMM().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDayDate() {
    try {
      return DateFormat.yMMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodTime() {
    try {
      return DateFormat('hh:mm a').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }
}

InputDecoration textfieldDecoration() {
  return InputDecoration(
    focusedBorder: const OutlineInputBorder(
        borderSide: BorderSide(color: Color.fromARGB(255, 200, 81, 72))),
    fillColor: Colors.white,
    filled: true,
    border: OutlineInputBorder(
        borderSide: const BorderSide(color: Colors.black),
        borderRadius: BorderRadius.circular(3)),
  );
}

Future<dynamic> showDragableSheet(BuildContext context, Widget child) {
  return showModalBottomSheet(
      // showDragHandle: true,
      backgroundColor: Colors.grey[200],
      useRootNavigator: true,
      isScrollControlled: true,
      context: context,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) => DraggableScrollableSheet(
            expand: false,
            builder: (context, scrollController) {
              return SingleChildScrollView(
                controller: scrollController,
                child: child,
              );
            },
          ));
}

InputDecoration textFieldDecoration() {
  return InputDecoration(
    fillColor: Colors.white,
    filled: true,
    border: OutlineInputBorder(
        borderSide: BorderSide.none, borderRadius: BorderRadius.circular(6)),
  );
}

InputDecoration settingsTextFieldDecoration() {
  return InputDecoration(
    fillColor: Colors.grey.shade100,
    filled: true,
    border: OutlineInputBorder(
        borderSide: BorderSide.none, borderRadius: BorderRadius.circular(6)),
  );
}

getProductPrice() {
  final ctrl = Get.find<ProductCtrl>();

  ctrl.totalPrice = 0;
  if ((ctrl.userDetails?.cartitems == null && ctrl.orderProduct.isEmpty)) {
    return;
  } else if (ctrl.orderProduct.isNotEmpty) {
    for (var cartItem in Get.find<ProductCtrl>().orderProduct) {
      for (int i = 0; i < cartItem.qty; i++) {
        ctrl.totalPrice += cartItem.price;
      }
    }
  } else {
    for (var cartItem in Get.find<ProductCtrl>().userDetails!.cartitems) {
      for (int i = 0; i < cartItem.qty; i++) {
        ctrl.totalPrice += cartItem.price;
      }
    }
  }
}

String capitalizeFirstLetters(String str) {
  List<String> words = str.split(' ');
  return words.map((word) => word.capitalizeFirst).join(' ');
}

Map<String, List<String>> getTimeSlots({
  required bool isDesigner,
  required int kitchenTime,
  required DateTime currentTime,
}) {
  // currentTime = DateTime(
  //     DateTime.now().year, DateTime.now().month, DateTime.now().day, 0);
  // Helper to generate time slots for a specific day
  List<String> generateSlots(int startHour, int endHour) {
    List<String> slots = [];
    for (int hour = startHour; hour < endHour; hour++) {
      slots.add(
          "${DateFormat('ha').format(DateTime(0, 0, 0, hour))}-${DateFormat('ha').format(DateTime(0, 0, 0, hour + 1))}");
    }
    return slots;
  }

  int todayStart = isDesigner ? 13 : 11;
  int todayEnd = isDesigner ? 23 : 23;

  DateTime roundedCurrent = DateTime(
    currentTime.year,
    currentTime.month,
    currentTime.day,
    currentTime.hour,
  ).add(Duration(hours: kitchenTime));
  if (roundedCurrent.hour >= todayStart && roundedCurrent.hour < todayEnd) {
    todayStart = roundedCurrent.hour + 1;
  }

  int tomorrowStart = currentTime.hour < (isDesigner ? 20 : 21)
      ? 11
      : isDesigner
          ? 13
          : 12;
  int tomorrowEnd = isDesigner ? 23 : 23;

  int customStart = 11;
  int customEnd = isDesigner ? 23 : 23;
  List<String> todaySlots = [];

  if (roundedCurrent.hour >= todayEnd) {
    todaySlots = [];
  } else {
    todaySlots = generateSlots(todayStart, todayEnd);
  }
  List<String> tomorrowSlots = generateSlots(tomorrowStart, tomorrowEnd);
  List<String> customSlots = generateSlots(customStart, customEnd);
  if (currentTime.hour > 20 && isDesigner) {
    todaySlots = [];
  } else if (currentTime.hour > 21 && !isDesigner) {
    todaySlots = [];
  }
  return {
    "Today": todaySlots,
    "Tomorrow": tomorrowSlots,
    "Custom": customSlots,
  };
}

@JS()
external void fbq(String eventName, [String? action, dynamic parameters]);

void trackFacebookEvent(String eventName, [Map<String, dynamic>? parameters]) {
  if (parameters != null) {
    final jsParams = js_util.jsify(parameters); // Convert Dart Map to JS Object
    fbq('track', eventName, jsParams);
  } else {
    fbq('track', eventName);
  }
}
