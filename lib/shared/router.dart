import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tbb_web/Models/order_model.dart';
import 'package:tbb_web/views/SubCat_page/subcat_page.dart';
import 'package:tbb_web/views/account/account_page.dart';
import 'package:tbb_web/views/account/new_user_page.dart';
import 'package:tbb_web/views/auth/auth.dart';
import 'package:tbb_web/views/cartpage/cart_page.dart';
import 'package:tbb_web/views/checkout_page/checkout_info.dart';
import 'package:tbb_web/views/checkout_page/checkout_payment.dart';
import 'package:tbb_web/views/checkout_page/checkout_shipping.dart';
import 'package:tbb_web/views/contact_us_page/contact_us_page.dart';
import 'package:tbb_web/views/footer_pages/refund.dart';
import 'package:tbb_web/views/homepage/home_page.dart';
import 'package:tbb_web/views/productpage/product_page.dart';
import 'package:tbb_web/views/return_and_refund_policy_page/return_and_refund_policy_page.dart';
import 'package:tbb_web/views/search/search_page.dart';
import 'package:tbb_web/views/shipping_policy_page.dart/shipping_policy_page.dart';
import 'package:tbb_web/views/thankyou_page.dart';
import 'package:tbb_web/views/viewAll/view_all.dart';
import '../views/about_us_page/about_us_page.dart';
import '../views/privacy_policy_page/privacy_policy_page.dart';
import '../views/terms_and_conditions_page.dart/terms_and_conditions.dart';
import 'error_page.dart';
import 'methods.dart';

final routeHistory = [Routes.home];

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: Routes.home,
  routes: _routes,
  redirect: redirector,
  errorBuilder: (context, state) => const ErrorPage(),
);
FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  routeHistory.add(state.uri.path);
  if (isLoggedIn() && state.fullPath == Routes.auth) {
    // return routeHistory.reversed.elementAt(1);
    return Routes.account;
    // return Routes.home;
  }
  return null;
}

List<RouteBase> get _routes {
  return <RouteBase>[
    GoRoute(
      path: Routes.home,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          // const NoTransitionPage(child: NewHomePage()),
          const NoTransitionPage(child: Homepage()),
      // const NoTransitionPage(child: Wrapper(body: Homepage())),
    ),
    GoRoute(
      path: Routes.auth,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: AuthPage(
                  goTo: state.extra != null ? state.extra as String : null)),
      // LoginPage()),
    ),
    GoRoute(
      path: Routes.account,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: Account()
              // AuthPage(
              //     goTo: state.extra != null ? state.extra as String : null)
              ),
    ),
    GoRoute(
      path: Routes.cart,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: CartPage()),
    ),
    GoRoute(
      path: '${Routes.product}/:id',
      pageBuilder: (BuildContext context, GoRouterState state) {
        return NoTransitionPage(
            child: Productpage(product: state.pathParameters['id'] ?? ""));
      },
    ),
    // GoRoute(
    //   path: Routes.checkout,
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       const NoTransitionPage(child: CheckoutPage()),
    // ),
    GoRoute(
      path: '${Routes.category}/:name',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: SubcatPage(
        name: state.pathParameters['name'] ?? "",
      )),
    ),

    // GoRoute(
    //   path: Routes.orders,
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       const NoTransitionPage(child: MyOrders()),
    // ),
    // GoRoute(
    //   path: '${Routes.orders}/:oId',
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       NoTransitionPage(
    //           child: OrderDetails(orderDocId: state.pathParameters['oId'])),
    // ),

    GoRoute(
      path: Routes.contact,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: ContactUs()),
    ),
    GoRoute(
      path: Routes.about,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: AboutUs()),
    ),
    GoRoute(
      path: '${Routes.signup}/:phoneemail',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: NewUserPage(
                  phoneemail: state.pathParameters['phoneemail'] ?? "")),
    ),

    GoRoute(
      path: Routes.terms,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: TermsAndConditions()),
    ),
    GoRoute(
      path: Routes.privacy,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: PrivacyPolicy()),
    ),

    GoRoute(
      path: Routes.returnandrefundPolicy,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: ReturnAndRefundPolicy()),
    ),
    GoRoute(
      path: Routes.refundPolicy,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: RefundPolicy()),
    ),
    GoRoute(
      path: Routes.shippingPolicy,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: ShippingPolicy()),
    ),
    GoRoute(
      path: '${Routes.allproducts}/:from',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: ViewAllPAge(from: state.pathParameters['from'] ?? "")),
    ),
    GoRoute(
      path: Routes.checkout,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: CheckOut()),
    ),
    GoRoute(
      path: Routes.thankyouPage,
      pageBuilder: (BuildContext context, GoRouterState state) {
        final order = state.extra as OrderModel?;
        return NoTransitionPage(child: ThankyouPage(order: order));
      },
    ),
    GoRoute(
      path: '${Routes.search}/:search',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: SearchPage(
        search: state.pathParameters['search'] ?? "",
      )),
    ),
    GoRoute(
      path: '${Routes.checkoutshipping}/:id',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: CheckOutShipping(
        id: state.pathParameters['id'] ?? "",
        instructions: state.extra.toString(),
      )),
    ),
    GoRoute(
      path: '${Routes.checkoutpayment}/:id',
      pageBuilder: (BuildContext context, GoRouterState state) {
        return NoTransitionPage(
            child: CheckOutPayment(
          id: state.pathParameters['id'] ?? "",
          instructions: state.extra.toString(),
        ));
      },
    ),
  ];
}

class Routes {
  static const home = "/";
  static const category = "/category";
  static const auth = "/auth";
  static const account = "/account";
  static const contact = "/contact";
  static const privacy = "/privacy";
  static const cart = "/cart";
  static const product = "/product";
  static const checkout = "/checkout";
  static const checkoutshipping = "/checkoutshipping";
  static const checkoutpayment = "/checkoutpayment";
  static const orders = "/orders";
  static const about = "/about";
  static const faqs = "/faqs";
  static const terms = "/terms";
  static const search = "/search";
  static const returnandrefundPolicy = "/return&refund-policy";
  static const refundPolicy = "/refundfund-policy";
  static const shippingPolicy = "/shipping-policy";
  static const signup = "/signup";
  static const allproducts = "/allproducts";
  static const thankyouPage = "/thankyou";

  static go() {}
}
